[{"C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\about\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\contact\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\dashboard\\influencer\\page.tsx": "3", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\dashboard\\merchant\\page.tsx": "4", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\dashboard\\ugc-creator\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\dashboard\\ugc-creator\\pricing\\page.tsx": "6", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\faq\\page.tsx": "7", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\forgot-password\\page.tsx": "8", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\help\\page.tsx": "9", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\how-it-works\\page.tsx": "10", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\influencers\\page.tsx": "11", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\influencers\\[id]\\page.tsx": "12", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\layout.tsx": "13", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\login\\page.tsx": "14", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\merchants\\page.tsx": "15", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\messages\\page.tsx": "16", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\page.tsx": "17", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\pricing\\page.tsx": "18", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\privacy\\page.tsx": "19", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\projects\\[id]\\review\\page.tsx": "20", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\refund\\page.tsx": "21", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\register\\influencer\\page.tsx": "22", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\register\\merchant\\page.tsx": "23", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\register\\page.tsx": "24", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\register\\ugc-creator\\page.tsx": "25", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\support\\page.tsx": "26", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\terms\\page.tsx": "27", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\ugc-creators\\page.tsx": "28", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\ugc-creators\\[id]\\order\\page.tsx": "29", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\ugc-creators\\[id]\\page.tsx": "30", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\layout\\Footer.tsx": "31", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\layout\\Header.tsx": "32", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\layout\\MobileNavigation.tsx": "33", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\ui\\Badge.tsx": "34", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\ui\\Breadcrumb.tsx": "35", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\ui\\Button.tsx": "36", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\ui\\Card.tsx": "37", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\ui\\Container.tsx": "38", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\ui\\FloatingActionButton.tsx": "39", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\ui\\Input.tsx": "40", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\ui\\Loading.tsx": "41", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\ui\\MobileNotification.tsx": "42"}, {"size": 13950, "mtime": 1748051709726, "results": "43", "hashOfConfig": "44"}, {"size": 12174, "mtime": 1748051540484, "results": "45", "hashOfConfig": "44"}, {"size": 17047, "mtime": 1748052076350, "results": "46", "hashOfConfig": "44"}, {"size": 20325, "mtime": 1748052134248, "results": "47", "hashOfConfig": "44"}, {"size": 26615, "mtime": 1748285790813, "results": "48", "hashOfConfig": "44"}, {"size": 21158, "mtime": 1748285748234, "results": "49", "hashOfConfig": "44"}, {"size": 12660, "mtime": 1748051663641, "results": "50", "hashOfConfig": "44"}, {"size": 6282, "mtime": 1748051563830, "results": "51", "hashOfConfig": "44"}, {"size": 13357, "mtime": 1748051850675, "results": "52", "hashOfConfig": "44"}, {"size": 13609, "mtime": 1748048454984, "results": "53", "hashOfConfig": "44"}, {"size": 13137, "mtime": 1748188324706, "results": "54", "hashOfConfig": "44"}, {"size": 19409, "mtime": 1748051620168, "results": "55", "hashOfConfig": "44"}, {"size": 1460, "mtime": 1748048127392, "results": "56", "hashOfConfig": "44"}, {"size": 6658, "mtime": 1748048360001, "results": "57", "hashOfConfig": "44"}, {"size": 15518, "mtime": 1748292568596, "results": "58", "hashOfConfig": "44"}, {"size": 15295, "mtime": 1748285681972, "results": "59", "hashOfConfig": "44"}, {"size": 17934, "mtime": 1748292693935, "results": "60", "hashOfConfig": "44"}, {"size": 12702, "mtime": 1748051502014, "results": "61", "hashOfConfig": "44"}, {"size": 16698, "mtime": 1748051807879, "results": "62", "hashOfConfig": "44"}, {"size": 16439, "mtime": 1748285920615, "results": "63", "hashOfConfig": "44"}, {"size": 13780, "mtime": 1748051897877, "results": "64", "hashOfConfig": "44"}, {"size": 17120, "mtime": 1748048300357, "results": "65", "hashOfConfig": "44"}, {"size": 14263, "mtime": 1748048339106, "results": "66", "hashOfConfig": "44"}, {"size": 7199, "mtime": 1748292732672, "results": "67", "hashOfConfig": "44"}, {"size": 23119, "mtime": 1748293284566, "results": "68", "hashOfConfig": "44"}, {"size": 20162, "mtime": 1748052023987, "results": "69", "hashOfConfig": "44"}, {"size": 11938, "mtime": 1748051754246, "results": "70", "hashOfConfig": "44"}, {"size": 18678, "mtime": 1748293321764, "results": "71", "hashOfConfig": "44"}, {"size": 10322, "mtime": 1748292480247, "results": "72", "hashOfConfig": "44"}, {"size": 22961, "mtime": 1748285613735, "results": "73", "hashOfConfig": "44"}, {"size": 7480, "mtime": 1748193684603, "results": "74", "hashOfConfig": "44"}, {"size": 4765, "mtime": 1748287087849, "results": "75", "hashOfConfig": "44"}, {"size": 2759, "mtime": 1748288915669, "results": "76", "hashOfConfig": "44"}, {"size": 915, "mtime": 1748288671132, "results": "77", "hashOfConfig": "44"}, {"size": 1139, "mtime": 1748286927623, "results": "78", "hashOfConfig": "44"}, {"size": 1718, "mtime": 1748286835897, "results": "79", "hashOfConfig": "44"}, {"size": 935, "mtime": 1748286779160, "results": "80", "hashOfConfig": "44"}, {"size": 615, "mtime": 1748286937604, "results": "81", "hashOfConfig": "44"}, {"size": 4602, "mtime": 1748288973086, "results": "82", "hashOfConfig": "44"}, {"size": 1682, "mtime": 1748286907779, "results": "83", "hashOfConfig": "44"}, {"size": 669, "mtime": 1748288659656, "results": "84", "hashOfConfig": "44"}, {"size": 6615, "mtime": 1748188374310, "results": "85", "hashOfConfig": "44"}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1bs3hp3", {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\about\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\contact\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\dashboard\\influencer\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\dashboard\\merchant\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\dashboard\\ugc-creator\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\dashboard\\ugc-creator\\pricing\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\faq\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\forgot-password\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\help\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\how-it-works\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\influencers\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\influencers\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\login\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\merchants\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\messages\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\pricing\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\privacy\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\projects\\[id]\\review\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\refund\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\register\\influencer\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\register\\merchant\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\register\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\register\\ugc-creator\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\support\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\terms\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\ugc-creators\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\ugc-creators\\[id]\\order\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\ugc-creators\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\layout\\Footer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\layout\\MobileNavigation.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\ui\\Badge.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\ui\\Breadcrumb.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\ui\\Button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\ui\\Card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\ui\\Container.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\ui\\FloatingActionButton.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\ui\\Input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\ui\\Loading.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\ui\\MobileNotification.tsx", ["212"], [], {"ruleId": "213", "severity": 1, "message": "214", "line": 33, "column": 6, "nodeType": "215", "endLine": 33, "endColumn": 16, "suggestions": "216"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'handleClose'. Either include it or remove the dependency array.", "ArrayExpression", ["217"], {"desc": "218", "fix": "219"}, "Update the dependencies array to be: [duration, handleClose]", {"range": "220", "text": "221"}, [729, 739], "[duration, handleClose]"]