[{"C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\about\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\contact\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\dashboard\\influencer\\page.tsx": "3", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\dashboard\\merchant\\page.tsx": "4", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\dashboard\\ugc-creator\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\dashboard\\ugc-creator\\pricing\\page.tsx": "6", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\faq\\page.tsx": "7", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\forgot-password\\page.tsx": "8", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\help\\page.tsx": "9", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\how-it-works\\page.tsx": "10", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\influencers\\page.tsx": "11", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\influencers\\[id]\\page.tsx": "12", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\layout.tsx": "13", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\login\\page.tsx": "14", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\merchants\\page.tsx": "15", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\messages\\page.tsx": "16", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\page.tsx": "17", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\pricing\\page.tsx": "18", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\privacy\\page.tsx": "19", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\projects\\[id]\\review\\page.tsx": "20", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\refund\\page.tsx": "21", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\register\\influencer\\page.tsx": "22", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\register\\merchant\\page.tsx": "23", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\register\\page.tsx": "24", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\register\\ugc-creator\\page.tsx": "25", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\support\\page.tsx": "26", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\terms\\page.tsx": "27", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\ugc-creators\\page.tsx": "28", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\ugc-creators\\[id]\\order\\page.tsx": "29", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\ugc-creators\\[id]\\page.tsx": "30", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\layout\\Footer.tsx": "31", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\layout\\Header.tsx": "32", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\layout\\MobileNavigation.tsx": "33", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\ui\\Badge.tsx": "34", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\ui\\Breadcrumb.tsx": "35", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\ui\\Button.tsx": "36", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\ui\\Card.tsx": "37", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\ui\\Container.tsx": "38", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\ui\\FloatingActionButton.tsx": "39", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\ui\\Input.tsx": "40", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\ui\\Loading.tsx": "41", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\ui\\MobileNotification.tsx": "42", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\agreement\\page.tsx": "43", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\campaigns\\create\\page.tsx": "44", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\careers\\page.tsx": "45", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\dashboard\\page.tsx": "46", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\news\\page.tsx": "47", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\reports\\page.tsx": "48", "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\team\\page.tsx": "49"}, {"size": 13950, "mtime": 1748051709726, "results": "50", "hashOfConfig": "51"}, {"size": 12174, "mtime": 1748051540484, "results": "52", "hashOfConfig": "51"}, {"size": 17047, "mtime": 1748052076350, "results": "53", "hashOfConfig": "51"}, {"size": 20325, "mtime": 1748052134248, "results": "54", "hashOfConfig": "51"}, {"size": 26615, "mtime": 1748285790813, "results": "55", "hashOfConfig": "51"}, {"size": 20675, "mtime": 1748294330109, "results": "56", "hashOfConfig": "51"}, {"size": 12660, "mtime": 1748051663641, "results": "57", "hashOfConfig": "51"}, {"size": 6282, "mtime": 1748051563830, "results": "58", "hashOfConfig": "51"}, {"size": 13357, "mtime": 1748051850675, "results": "59", "hashOfConfig": "51"}, {"size": 13609, "mtime": 1748048454984, "results": "60", "hashOfConfig": "51"}, {"size": 13137, "mtime": 1748188324706, "results": "61", "hashOfConfig": "51"}, {"size": 19409, "mtime": 1748051620168, "results": "62", "hashOfConfig": "51"}, {"size": 1460, "mtime": 1748048127392, "results": "63", "hashOfConfig": "51"}, {"size": 6658, "mtime": 1748048360001, "results": "64", "hashOfConfig": "51"}, {"size": 15518, "mtime": 1748292568596, "results": "65", "hashOfConfig": "51"}, {"size": 15061, "mtime": 1748293885914, "results": "66", "hashOfConfig": "51"}, {"size": 17934, "mtime": 1748292693935, "results": "67", "hashOfConfig": "51"}, {"size": 12702, "mtime": 1748051502014, "results": "68", "hashOfConfig": "51"}, {"size": 16698, "mtime": 1748051807879, "results": "69", "hashOfConfig": "51"}, {"size": 16439, "mtime": 1748285920615, "results": "70", "hashOfConfig": "51"}, {"size": 13780, "mtime": 1748051897877, "results": "71", "hashOfConfig": "51"}, {"size": 17120, "mtime": 1748048300357, "results": "72", "hashOfConfig": "51"}, {"size": 14263, "mtime": 1748048339106, "results": "73", "hashOfConfig": "51"}, {"size": 7199, "mtime": 1748292732672, "results": "74", "hashOfConfig": "51"}, {"size": 23119, "mtime": 1748293284566, "results": "75", "hashOfConfig": "51"}, {"size": 20162, "mtime": 1748052023987, "results": "76", "hashOfConfig": "51"}, {"size": 11938, "mtime": 1748051754246, "results": "77", "hashOfConfig": "51"}, {"size": 18678, "mtime": 1748293321764, "results": "78", "hashOfConfig": "51"}, {"size": 10322, "mtime": 1748292480247, "results": "79", "hashOfConfig": "51"}, {"size": 22961, "mtime": 1748285613735, "results": "80", "hashOfConfig": "51"}, {"size": 7480, "mtime": 1748193684603, "results": "81", "hashOfConfig": "51"}, {"size": 4765, "mtime": 1748287087849, "results": "82", "hashOfConfig": "51"}, {"size": 2759, "mtime": 1748288915669, "results": "83", "hashOfConfig": "51"}, {"size": 915, "mtime": 1748288671132, "results": "84", "hashOfConfig": "51"}, {"size": 1139, "mtime": 1748286927623, "results": "85", "hashOfConfig": "51"}, {"size": 1718, "mtime": 1748286835897, "results": "86", "hashOfConfig": "51"}, {"size": 935, "mtime": 1748286779160, "results": "87", "hashOfConfig": "51"}, {"size": 615, "mtime": 1748286937604, "results": "88", "hashOfConfig": "51"}, {"size": 4602, "mtime": 1748288973086, "results": "89", "hashOfConfig": "51"}, {"size": 1682, "mtime": 1748286907779, "results": "90", "hashOfConfig": "51"}, {"size": 669, "mtime": 1748288659656, "results": "91", "hashOfConfig": "51"}, {"size": 6615, "mtime": 1748188374310, "results": "92", "hashOfConfig": "51"}, {"size": 12438, "mtime": 1748293872278, "results": "93", "hashOfConfig": "51"}, {"size": 20930, "mtime": 1748293950079, "results": "94", "hashOfConfig": "51"}, {"size": 17751, "mtime": 1748293775859, "results": "95", "hashOfConfig": "51"}, {"size": 13480, "mtime": 1748293593254, "results": "96", "hashOfConfig": "51"}, {"size": 16263, "mtime": 1748293828766, "results": "97", "hashOfConfig": "51"}, {"size": 16469, "mtime": 1748294007063, "results": "98", "hashOfConfig": "51"}, {"size": 14092, "mtime": 1748293717803, "results": "99", "hashOfConfig": "51"}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1bs3hp3", {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\about\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\contact\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\dashboard\\influencer\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\dashboard\\merchant\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\dashboard\\ugc-creator\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\dashboard\\ugc-creator\\pricing\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\faq\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\forgot-password\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\help\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\how-it-works\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\influencers\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\influencers\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\login\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\merchants\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\messages\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\pricing\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\privacy\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\projects\\[id]\\review\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\refund\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\register\\influencer\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\register\\merchant\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\register\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\register\\ugc-creator\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\support\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\terms\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\ugc-creators\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\ugc-creators\\[id]\\order\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\ugc-creators\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\layout\\Footer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\layout\\MobileNavigation.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\ui\\Badge.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\ui\\Breadcrumb.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\ui\\Button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\ui\\Card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\ui\\Container.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\ui\\FloatingActionButton.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\ui\\Input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\ui\\Loading.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\components\\ui\\MobileNotification.tsx", ["247"], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\agreement\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\campaigns\\create\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\careers\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\news\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\reports\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\team\\page.tsx", [], [], {"ruleId": "248", "severity": 1, "message": "249", "line": 33, "column": 6, "nodeType": "250", "endLine": 33, "endColumn": 16, "suggestions": "251"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'handleClose'. Either include it or remove the dependency array.", "ArrayExpression", ["252"], {"desc": "253", "fix": "254"}, "Update the dependencies array to be: [duration, handleClose]", {"range": "255", "text": "256"}, [729, 739], "[duration, handleClose]"]