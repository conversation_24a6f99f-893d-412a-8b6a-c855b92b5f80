'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { 
  MessageCircle, 
  Send, 
  Paperclip, 
  Search, 
  MoreVertical,
  Phone,
  Video,
  Star,
  CheckCircle,
  Clock,
  Image,
  File,
  Smile
} from 'lucide-react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import MobileNavigation from '@/components/layout/MobileNavigation'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'

const MessagesPage: React.FC = () => {
  const [selectedChat, setSelectedChat] = useState(1)
  const [newMessage, setNewMessage] = useState('')
  const [searchTerm, setSearchTerm] = useState('')

  // Mock conversations data
  const conversations = [
    {
      id: 1,
      name: 'سارة المبدعة',
      avatar: '👩‍🎨',
      lastMessage: 'شكراً لك! سأبدأ العمل على الفيديو غداً صباحاً',
      timestamp: 'منذ 5 دقائق',
      unread: 2,
      online: true,
      type: 'ugc-creator',
      project: 'فيديو تيك توك للمنتج الجديد'
    },
    {
      id: 2,
      name: 'أحمد المصور',
      avatar: '👨‍💻',
      lastMessage: 'تم إرسال الصور المعدلة، يرجى المراجعة',
      timestamp: 'منذ ساعة',
      unread: 0,
      online: false,
      type: 'ugc-creator',
      project: 'تصوير منتجات المتجر'
    },
    {
      id: 3,
      name: 'نورا الفنانة',
      avatar: '🎨',
      lastMessage: 'هل يمكن تعديل الألوان في التصميم؟',
      timestamp: 'منذ 3 ساعات',
      unread: 1,
      online: true,
      type: 'ugc-creator',
      project: 'تصميم لوجو الشركة'
    },
    {
      id: 4,
      name: 'متجر الأناقة',
      avatar: '🏪',
      lastMessage: 'ممتاز! نريد المزيد من هذا النوع من المحتوى',
      timestamp: 'منذ يوم',
      unread: 0,
      online: false,
      type: 'merchant',
      project: 'حملة الشتاء الجديدة'
    }
  ]

  // Mock messages for selected chat
  const messages = [
    {
      id: 1,
      sender: 'other',
      content: 'مرحباً! شكراً لاختياري لهذا المشروع',
      timestamp: '10:30 ص',
      type: 'text'
    },
    {
      id: 2,
      sender: 'me',
      content: 'أهلاً وسهلاً! متحمس للعمل معك',
      timestamp: '10:32 ص',
      type: 'text'
    },
    {
      id: 3,
      sender: 'other',
      content: 'هل يمكنك إرسال تفاصيل أكثر عن المنتج؟',
      timestamp: '10:35 ص',
      type: 'text'
    },
    {
      id: 4,
      sender: 'me',
      content: 'بالطبع! إليك صور المنتج والمعلومات المطلوبة',
      timestamp: '10:40 ص',
      type: 'text'
    },
    {
      id: 5,
      sender: 'me',
      content: 'product-images.zip',
      timestamp: '10:40 ص',
      type: 'file'
    },
    {
      id: 6,
      sender: 'other',
      content: 'ممتاز! سأراجع المواد وأبدأ العمل',
      timestamp: '11:15 ص',
      type: 'text'
    },
    {
      id: 7,
      sender: 'other',
      content: 'شكراً لك! سأبدأ العمل على الفيديو غداً صباحاً',
      timestamp: '2:20 م',
      type: 'text'
    }
  ]

  const selectedConversation = conversations.find(c => c.id === selectedChat)
  const filteredConversations = conversations.filter(conv =>
    conv.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    conv.project.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const sendMessage = () => {
    if (newMessage.trim()) {
      console.log('Sending message:', newMessage)
      setNewMessage('')
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  return (
    <div className="min-h-screen bg-slate-900">
      <Header />
      
      <div className="py-8">
        <div className="max-w-7xl mx-auto mobile-container">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-white mb-2">الرسائل</h1>
            <p className="text-gray-300">تواصل مع المبدعين والعملاء</p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[600px]">
            {/* Conversations List */}
            <div className="lg:col-span-1">
              <Card className="card-mobile h-full flex flex-col">
                {/* Search */}
                <div className="p-4 border-b border-gray-600">
                  <div className="relative">
                    <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <input
                      type="text"
                      placeholder="ابحث في المحادثات..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full bg-white/10 border border-gray-600 rounded-xl px-4 py-2 pr-10 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                    />
                  </div>
                </div>

                {/* Conversations */}
                <div className="flex-1 overflow-y-auto">
                  {filteredConversations.map((conversation) => (
                    <div
                      key={conversation.id}
                      onClick={() => setSelectedChat(conversation.id)}
                      className={`p-4 border-b border-gray-700 cursor-pointer transition-colors hover:bg-white/5 ${
                        selectedChat === conversation.id ? 'bg-purple-500/20 border-purple-500' : ''
                      }`}
                    >
                      <div className="flex items-start gap-3">
                        <div className="relative">
                          <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-xl">
                            {conversation.avatar}
                          </div>
                          {conversation.online && (
                            <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-slate-900"></div>
                          )}
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between mb-1">
                            <h3 className="font-semibold text-white truncate">{conversation.name}</h3>
                            <div className="flex items-center gap-1">
                              {conversation.unread > 0 && (
                                <span className="bg-purple-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                                  {conversation.unread}
                                </span>
                              )}
                              <span className="text-xs text-gray-400">{conversation.timestamp}</span>
                            </div>
                          </div>
                          
                          <p className="text-sm text-gray-400 truncate mb-1">{conversation.lastMessage}</p>
                          
                          <div className="flex items-center gap-2">
                            <span className={`text-xs px-2 py-1 rounded-full ${
                              conversation.type === 'ugc-creator' 
                                ? 'bg-purple-500/20 text-purple-400' 
                                : 'bg-blue-500/20 text-blue-400'
                            }`}>
                              {conversation.type === 'ugc-creator' ? '🎬 مبدع' : '🏪 تاجر'}
                            </span>
                            <span className="text-xs text-gray-500 truncate">{conversation.project}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </Card>
            </div>

            {/* Chat Area */}
            <div className="lg:col-span-2">
              <Card className="card-mobile h-full flex flex-col">
                {selectedConversation ? (
                  <>
                    {/* Chat Header */}
                    <div className="p-4 border-b border-gray-600 flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="relative">
                          <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-lg">
                            {selectedConversation.avatar}
                          </div>
                          {selectedConversation.online && (
                            <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 rounded-full border border-slate-900"></div>
                          )}
                        </div>
                        <div>
                          <h3 className="font-semibold text-white">{selectedConversation.name}</h3>
                          <p className="text-sm text-gray-400">{selectedConversation.project}</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Button variant="secondary" size="sm" className="btn-secondary-mobile">
                          <Phone className="h-4 w-4" />
                        </Button>
                        <Button variant="secondary" size="sm" className="btn-secondary-mobile">
                          <Video className="h-4 w-4" />
                        </Button>
                        <Button variant="secondary" size="sm" className="btn-secondary-mobile">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    {/* Messages */}
                    <div className="flex-1 overflow-y-auto p-4 space-y-4">
                      {messages.map((message) => (
                        <div
                          key={message.id}
                          className={`flex ${message.sender === 'me' ? 'justify-end' : 'justify-start'}`}
                        >
                          <div
                            className={`max-w-xs lg:max-w-md px-4 py-2 rounded-2xl ${
                              message.sender === 'me'
                                ? 'bg-purple-500 text-white'
                                : 'bg-white/10 text-gray-200'
                            }`}
                          >
                            {message.type === 'file' ? (
                              <div className="flex items-center gap-2">
                                <File className="h-4 w-4" />
                                <span className="text-sm">{message.content}</span>
                              </div>
                            ) : (
                              <p className="text-sm">{message.content}</p>
                            )}
                            <p className={`text-xs mt-1 ${
                              message.sender === 'me' ? 'text-purple-200' : 'text-gray-400'
                            }`}>
                              {message.timestamp}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>

                    {/* Message Input */}
                    <div className="p-4 border-t border-gray-600">
                      <div className="flex items-end gap-3">
                        <Button variant="secondary" size="sm" className="btn-secondary-mobile">
                          <Paperclip className="h-4 w-4" />
                        </Button>
                        
                        <div className="flex-1">
                          <textarea
                            value={newMessage}
                            onChange={(e) => setNewMessage(e.target.value)}
                            onKeyPress={handleKeyPress}
                            placeholder="اكتب رسالتك..."
                            className="w-full bg-white/10 border border-gray-600 rounded-xl px-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 resize-none"
                            rows={1}
                          />
                        </div>
                        
                        <Button variant="secondary" size="sm" className="btn-secondary-mobile">
                          <Smile className="h-4 w-4" />
                        </Button>
                        
                        <Button 
                          onClick={sendMessage}
                          disabled={!newMessage.trim()}
                          className="btn-primary-mobile"
                        >
                          <Send className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </>
                ) : (
                  <div className="flex-1 flex items-center justify-center">
                    <div className="text-center">
                      <MessageCircle className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-semibold text-white mb-2">اختر محادثة</h3>
                      <p className="text-gray-400">اختر محادثة من القائمة لبدء التواصل</p>
                    </div>
                  </div>
                )}
              </Card>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="mt-8">
            <Card className="card-mobile">
              <div className="text-center">
                <h3 className="text-lg font-bold text-white mb-4">إجراءات سريعة</h3>
                <div className="flex flex-wrap justify-center gap-4">
                  <Link href="/ugc-creators">
                    <Button variant="secondary" className="btn-secondary-mobile">
                      🎬 تصفح المبدعين
                    </Button>
                  </Link>
                  <Link href="/dashboard/ugc-creator">
                    <Button variant="secondary" className="btn-secondary-mobile">
                      📊 لوحة التحكم
                    </Button>
                  </Link>
                  <Button variant="secondary" className="btn-secondary-mobile">
                    📞 الدعم الفني
                  </Button>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>

      <Footer />
      <MobileNavigation />
    </div>
  )
}

export default MessagesPage
