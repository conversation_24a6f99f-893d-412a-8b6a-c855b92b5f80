/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/ugc-creator/pricing/page";
exports.ids = ["app/dashboard/ugc-creator/pricing/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fugc-creator%2Fpricing%2Fpage&page=%2Fdashboard%2Fugc-creator%2Fpricing%2Fpage&appPaths=%2Fdashboard%2Fugc-creator%2Fpricing%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fugc-creator%2Fpricing%2Fpage.tsx&appDir=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fugc-creator%2Fpricing%2Fpage&page=%2Fdashboard%2Fugc-creator%2Fpricing%2Fpage&appPaths=%2Fdashboard%2Fugc-creator%2Fpricing%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fugc-creator%2Fpricing%2Fpage.tsx&appDir=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'ugc-creator',\n        {\n        children: [\n        'pricing',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/ugc-creator/pricing/page.tsx */ \"(rsc)/./src/app/dashboard/ugc-creator/pricing/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/ugc-creator/pricing/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/ugc-creator/pricing/page\",\n        pathname: \"/dashboard/ugc-creator/pricing\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fugc-creator%2Fpricing%2Fpage&page=%2Fdashboard%2Fugc-creator%2Fpricing%2Fpage&appPaths=%2Fdashboard%2Fugc-creator%2Fpricing%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fugc-creator%2Fpricing%2Fpage.tsx&appDir=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Csrc%5Capp%5Cdashboard%5Cugc-creator%5Cpricing%5Cpage.tsx&server=true!":
/*!******************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Csrc%5Capp%5Cdashboard%5Cugc-creator%5Cpricing%5Cpage.tsx&server=true! ***!
  \******************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/ugc-creator/pricing/page.tsx */ \"(ssr)/./src/app/dashboard/ugc-creator/pricing/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDc2htNzElNUNEZXNrdG9wJTVDYW1zaG9yMiU1Q3NyYyU1Q2FwcCU1Q2Rhc2hib2FyZCU1Q3VnYy1jcmVhdG9yJTVDcHJpY2luZyU1Q3BhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2luZmx1ZW5jZXItcGxhdGZvcm0tc2F1ZGkvPzU3NmQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxzaG03MVxcXFxEZXNrdG9wXFxcXGFtc2hvcjJcXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxcdWdjLWNyZWF0b3JcXFxccHJpY2luZ1xcXFxwYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Csrc%5Capp%5Cdashboard%5Cugc-creator%5Cpricing%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Csrc%5Capp%5Cglobals.css&server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Csrc%5Capp%5Cglobals.css&server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/dashboard/ugc-creator/pricing/page.tsx":
/*!********************************************************!*\
  !*** ./src/app/dashboard/ugc-creator/pricing/page.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_Copy_DollarSign_Plus_Save_Star_Target_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,Copy,DollarSign,Plus,Save,Star,Target,Trash2,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_Copy_DollarSign_Plus_Save_Star_Target_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,Copy,DollarSign,Plus,Save,Star,Target,Trash2,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_Copy_DollarSign_Plus_Save_Star_Target_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,Copy,DollarSign,Plus,Save,Star,Target,Trash2,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_Copy_DollarSign_Plus_Save_Star_Target_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,Copy,DollarSign,Plus,Save,Star,Target,Trash2,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_Copy_DollarSign_Plus_Save_Star_Target_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,Copy,DollarSign,Plus,Save,Star,Target,Trash2,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_Copy_DollarSign_Plus_Save_Star_Target_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,Copy,DollarSign,Plus,Save,Star,Target,Trash2,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_Copy_DollarSign_Plus_Save_Star_Target_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,Copy,DollarSign,Plus,Save,Star,Target,Trash2,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_Copy_DollarSign_Plus_Save_Star_Target_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,Copy,DollarSign,Plus,Save,Star,Target,Trash2,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_Copy_DollarSign_Plus_Save_Star_Target_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,Copy,DollarSign,Plus,Save,Star,Target,Trash2,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircle_Copy_DollarSign_Plus_Save_Star_Target_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircle,Copy,DollarSign,Plus,Save,Star,Target,Trash2,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Card */ \"(ssr)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Input */ \"(ssr)/./src/components/ui/Input.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst PricingSettingsPage = ()=>{\n    const [services, setServices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            title: \"فيديو تيك توك احترافي\",\n            description: \"فيديو تيك توك مبدع ومؤثر مع مونتاج احترافي\",\n            category: \"فيديو\",\n            durations: [\n                {\n                    duration: \"15 ثانية\",\n                    price: 200,\n                    active: true\n                },\n                {\n                    duration: \"30 ثانية\",\n                    price: 350,\n                    active: true\n                },\n                {\n                    duration: \"60 ثانية\",\n                    price: 500,\n                    active: true\n                }\n            ],\n            deliveryTime: \"24-48 ساعة\",\n            revisions: 2,\n            icon: \"\\uD83C\\uDFB5\",\n            active: true\n        },\n        {\n            id: 2,\n            title: \"ستوري انستقرام تفاعلي\",\n            description: \"ستوريز انستقرام جذابة مع عناصر تفاعلية\",\n            category: \"ستوري\",\n            durations: [\n                {\n                    duration: \"15 ثانية\",\n                    price: 150,\n                    active: true\n                },\n                {\n                    duration: \"30 ثانية\",\n                    price: 250,\n                    active: true\n                }\n            ],\n            deliveryTime: \"24 ساعة\",\n            revisions: 1,\n            icon: \"\\uD83D\\uDCF1\",\n            active: true\n        },\n        {\n            id: 3,\n            title: \"تصوير منتج احترافي\",\n            description: \"تصوير منتج بجودة عالية مع إضاءة احترافية\",\n            category: \"تصوير\",\n            durations: [\n                {\n                    duration: \"5 صور\",\n                    price: 200,\n                    active: true\n                },\n                {\n                    duration: \"10 صور\",\n                    price: 350,\n                    active: true\n                },\n                {\n                    duration: \"20 صور\",\n                    price: 600,\n                    active: true\n                }\n            ],\n            deliveryTime: \"1-2 أيام\",\n            revisions: 2,\n            icon: \"\\uD83D\\uDCF8\",\n            active: true\n        }\n    ]);\n    const [newService, setNewService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        description: \"\",\n        category: \"\",\n        durations: [\n            {\n                duration: \"\",\n                price: 0,\n                active: true\n            }\n        ],\n        deliveryTime: \"\",\n        revisions: 1,\n        icon: \"⚡\"\n    });\n    const [showAddForm, setShowAddForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingService, setEditingService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const categories = [\n        {\n            id: \"video\",\n            label: \"فيديو\",\n            icon: \"\\uD83C\\uDFAC\"\n        },\n        {\n            id: \"photo\",\n            label: \"تصوير\",\n            icon: \"\\uD83D\\uDCF8\"\n        },\n        {\n            id: \"design\",\n            label: \"تصميم\",\n            icon: \"\\uD83C\\uDFA8\"\n        },\n        {\n            id: \"editing\",\n            label: \"مونتاج\",\n            icon: \"✂️\"\n        },\n        {\n            id: \"story\",\n            label: \"ستوري\",\n            icon: \"\\uD83D\\uDCF1\"\n        },\n        {\n            id: \"review\",\n            label: \"مراجعة\",\n            icon: \"⭐\"\n        }\n    ];\n    const icons = [\n        \"\\uD83C\\uDFB5\",\n        \"\\uD83D\\uDCF1\",\n        \"\\uD83D\\uDCF8\",\n        \"⭐\",\n        \"\\uD83C\\uDFA8\",\n        \"✂️\",\n        \"\\uD83C\\uDFAC\",\n        \"\\uD83D\\uDCFA\",\n        \"\\uD83D\\uDC84\",\n        \"\\uD83D\\uDC57\",\n        \"\\uD83C\\uDF54\",\n        \"\\uD83D\\uDE97\",\n        \"\\uD83D\\uDCF1\",\n        \"\\uD83D\\uDCBB\",\n        \"\\uD83C\\uDFA7\"\n    ];\n    const updateServicePrice = (serviceId, durationIndex, newPrice)=>{\n        setServices((prev)=>prev.map((service)=>service.id === serviceId ? {\n                    ...service,\n                    durations: service.durations.map((dur, index)=>index === durationIndex ? {\n                            ...dur,\n                            price: newPrice\n                        } : dur)\n                } : service));\n    };\n    const toggleServiceActive = (serviceId)=>{\n        setServices((prev)=>prev.map((service)=>service.id === serviceId ? {\n                    ...service,\n                    active: !service.active\n                } : service));\n    };\n    const addDuration = (serviceId)=>{\n        setServices((prev)=>prev.map((service)=>service.id === serviceId ? {\n                    ...service,\n                    durations: [\n                        ...service.durations,\n                        {\n                            duration: \"\",\n                            price: 0,\n                            active: true\n                        }\n                    ]\n                } : service));\n    };\n    const removeDuration = (serviceId, durationIndex)=>{\n        setServices((prev)=>prev.map((service)=>service.id === serviceId ? {\n                    ...service,\n                    durations: service.durations.filter((_, index)=>index !== durationIndex)\n                } : service));\n    };\n    const duplicateService = (serviceId)=>{\n        const serviceToDuplicate = services.find((s)=>s.id === serviceId);\n        if (serviceToDuplicate) {\n            const newService = {\n                ...serviceToDuplicate,\n                id: Math.max(...services.map((s)=>s.id)) + 1,\n                title: serviceToDuplicate.title + \" (نسخة)\",\n                active: false\n            };\n            setServices((prev)=>[\n                    ...prev,\n                    newService\n                ]);\n        }\n    };\n    const deleteService = (serviceId)=>{\n        if (confirm(\"هل أنت متأكد من حذف هذه الخدمة؟\")) {\n            setServices((prev)=>prev.filter((service)=>service.id !== serviceId));\n        }\n    };\n    const addNewService = ()=>{\n        if (newService.title && newService.category) {\n            const service = {\n                ...newService,\n                id: Math.max(...services.map((s)=>s.id)) + 1,\n                active: true\n            };\n            setServices((prev)=>[\n                    ...prev,\n                    service\n                ]);\n            setNewService({\n                title: \"\",\n                description: \"\",\n                category: \"\",\n                durations: [\n                    {\n                        duration: \"\",\n                        price: 0,\n                        active: true\n                    }\n                ],\n                deliveryTime: \"\",\n                revisions: 1,\n                icon: \"⚡\"\n            });\n            setShowAddForm(false);\n        }\n    };\n    const saveAllChanges = ()=>{\n        console.log(\"Saving pricing changes:\", services);\n        alert(\"تم حفظ التغييرات بنجاح! \\uD83C\\uDF89\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-slate-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-slate-800/80 backdrop-blur-xl shadow-2xl sticky top-0 z-50 border-b border-purple-500/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto mobile-container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/dashboard/ugc-creator\",\n                                    className: \"flex items-center text-purple-400 hover:text-purple-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Copy_DollarSign_Plus_Save_Star_Target_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-5 w-5 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"العودة للوحة التحكم\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 space-x-reverse\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    onClick: saveAllChanges,\n                                    className: \"btn-primary-mobile sparkle-container\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Copy_DollarSign_Plus_Save_Star_Target_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-5 w-5 ml-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"حفظ التغييرات\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto mobile-container py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-white mb-2\",\n                                children: \"إعدادات الأسعار\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-300\",\n                                children: \"إدارة خدماتك وأسعارك بمرونة كاملة\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"card-mobile text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Copy_DollarSign_Plus_Save_Star_Target_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-8 w-8 text-green-500 mx-auto mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-white\",\n                                        children: services.filter((s)=>s.active).length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: \"خدمة نشطة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"card-mobile text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Copy_DollarSign_Plus_Save_Star_Target_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-500 mx-auto mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-white\",\n                                        children: [\n                                            Math.round(services.reduce((acc, service)=>acc + service.durations.reduce((durAcc, dur)=>durAcc + dur.price, 0) / service.durations.length, 0) / services.length),\n                                            \" ريال\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: \"متوسط السعر\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"card-mobile text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Copy_DollarSign_Plus_Save_Star_Target_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-8 w-8 text-purple-500 mx-auto mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-white\",\n                                        children: services.reduce((acc, service)=>acc + service.durations.length, 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: \"خيار سعر\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"card-mobile text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Copy_DollarSign_Plus_Save_Star_Target_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-8 w-8 text-yellow-500 mx-auto mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-white\",\n                                        children: \"4.9\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-400\",\n                                        children: \"تقييم الخدمات\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: services.map((service)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"card-mobile\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl ml-4\",\n                                                        children: service.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-xl font-bold text-white\",\n                                                                        children: service.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                                        lineNumber: 254,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: `px-2 py-1 rounded-full text-xs ${service.active ? \"bg-green-500/20 text-green-400\" : \"bg-gray-500/20 text-gray-400\"}`,\n                                                                        children: service.active ? \"نشط\" : \"معطل\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                                        lineNumber: 255,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 253,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-400\",\n                                                                children: service.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 261,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-4 mt-2 text-sm text-gray-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"\\uD83D\\uDCC5 \",\n                                                                            service.deliveryTime\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                                        lineNumber: 263,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"\\uD83D\\uDD04 \",\n                                                                            service.revisions,\n                                                                            \" مراجعة\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                                        lineNumber: 264,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"\\uD83D\\uDCC2 \",\n                                                                            service.category\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                                        lineNumber: 265,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 262,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        variant: \"secondary\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>toggleServiceActive(service.id),\n                                                        className: service.active ? \"btn-secondary-mobile\" : \"btn-primary-mobile\",\n                                                        children: service.active ? \"إيقاف\" : \"تفعيل\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        variant: \"secondary\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>duplicateService(service.id),\n                                                        className: \"btn-secondary-mobile\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Copy_DollarSign_Plus_Save_Star_Target_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        variant: \"secondary\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>deleteService(service.id),\n                                                        className: \"text-red-400 hover:text-red-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Copy_DollarSign_Plus_Save_Star_Target_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-semibold text-white\",\n                                                children: \"الأسعار حسب المدة/الكمية:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            service.durations.map((duration, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-4 p-3 bg-white/5 rounded-xl\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: duration.duration,\n                                                                onChange: (e)=>{\n                                                                    const newDurations = [\n                                                                        ...service.durations\n                                                                    ];\n                                                                    newDurations[index].duration = e.target.value;\n                                                                    setServices((prev)=>prev.map((s)=>s.id === service.id ? {\n                                                                                ...s,\n                                                                                durations: newDurations\n                                                                            } : s));\n                                                                },\n                                                                className: \"w-full bg-transparent border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500\",\n                                                                placeholder: \"مثال: 30 ثانية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 306,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                            lineNumber: 305,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    value: duration.price,\n                                                                    onChange: (e)=>updateServicePrice(service.id, index, parseInt(e.target.value) || 0),\n                                                                    className: \"w-24 bg-transparent border border-gray-600 rounded-lg px-3 py-2 text-white text-center focus:outline-none focus:border-purple-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                                    lineNumber: 322,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-400\",\n                                                                    children: \"ريال\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                                    lineNumber: 328,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                            lineNumber: 321,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        service.durations.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            variant: \"secondary\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>removeDuration(service.id, index),\n                                                            className: \"text-red-400 hover:text-red-300\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Copy_DollarSign_Plus_Save_Star_Target_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 338,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                            lineNumber: 332,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 19\n                                                }, undefined)),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                variant: \"secondary\",\n                                                size: \"sm\",\n                                                onClick: ()=>addDuration(service.id),\n                                                className: \"btn-secondary-mobile\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Copy_DollarSign_Plus_Save_Star_Target_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4 ml-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"إضافة خيار سعر\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, service.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                        lineNumber: 246,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"card-mobile mt-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: !showAddForm ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-bold text-white mb-4\",\n                                        children: \"إضافة خدمة جديدة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        onClick: ()=>setShowAddForm(true),\n                                        className: \"btn-primary-mobile sparkle-container\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Copy_DollarSign_Plus_Save_Star_Target_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-5 w-5 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"إضافة خدمة جديدة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-bold text-white\",\n                                        children: \"خدمة جديدة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                label: \"اسم الخدمة\",\n                                                placeholder: \"مثال: فيديو يوتيوب احترافي\",\n                                                value: newService.title,\n                                                onChange: (e)=>setNewService((prev)=>({\n                                                            ...prev,\n                                                            title: e.target.value\n                                                        }))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                        children: \"الفئة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: newService.category,\n                                                        onChange: (e)=>setNewService((prev)=>({\n                                                                    ...prev,\n                                                                    category: e.target.value\n                                                                })),\n                                                        className: \"input-mobile\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: \"اختر الفئة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 391,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            categories.map((cat)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: cat.label,\n                                                                    children: [\n                                                                        cat.icon,\n                                                                        \" \",\n                                                                        cat.label\n                                                                    ]\n                                                                }, cat.id, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                                    lineNumber: 393,\n                                                                    columnNumber: 25\n                                                                }, undefined))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                children: \"وصف الخدمة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                className: \"input-mobile min-h-[80px]\",\n                                                placeholder: \"وصف مفصل للخدمة...\",\n                                                value: newService.description,\n                                                onChange: (e)=>setNewService((prev)=>({\n                                                            ...prev,\n                                                            description: e.target.value\n                                                        }))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                label: \"وقت التسليم\",\n                                                placeholder: \"مثال: 2-3 أيام\",\n                                                value: newService.deliveryTime,\n                                                onChange: (e)=>setNewService((prev)=>({\n                                                            ...prev,\n                                                            deliveryTime: e.target.value\n                                                        }))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                        children: \"عدد المراجعات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 418,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: newService.revisions,\n                                                        onChange: (e)=>setNewService((prev)=>({\n                                                                    ...prev,\n                                                                    revisions: parseInt(e.target.value)\n                                                                })),\n                                                        className: \"input-mobile\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 1,\n                                                                children: \"مراجعة واحدة\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 424,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 2,\n                                                                children: \"مراجعتان\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 425,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: 3,\n                                                                children: \"3 مراجعات\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 426,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: -1,\n                                                                children: \"غير محدود\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 427,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-300 mb-2\",\n                                                        children: \"الأيقونة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 432,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: newService.icon,\n                                                        onChange: (e)=>setNewService((prev)=>({\n                                                                    ...prev,\n                                                                    icon: e.target.value\n                                                                })),\n                                                        className: \"input-mobile\",\n                                                        children: icons.map((icon)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: icon,\n                                                                children: icon\n                                                            }, icon, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                                lineNumber: 439,\n                                                                columnNumber: 25\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 433,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-semibold text-white mb-3\",\n                                                children: \"الأسعار:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                lineNumber: 446,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            newService.durations.map((duration, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-4 mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"مثال: 30 ثانية\",\n                                                            value: duration.duration,\n                                                            onChange: (e)=>{\n                                                                const newDurations = [\n                                                                    ...newService.durations\n                                                                ];\n                                                                newDurations[index].duration = e.target.value;\n                                                                setNewService((prev)=>({\n                                                                        ...prev,\n                                                                        durations: newDurations\n                                                                    }));\n                                                            },\n                                                            className: \"flex-1 input-mobile\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                            lineNumber: 449,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            placeholder: \"السعر\",\n                                                            value: duration.price || \"\",\n                                                            onChange: (e)=>{\n                                                                const newDurations = [\n                                                                    ...newService.durations\n                                                                ];\n                                                                newDurations[index].price = parseInt(e.target.value) || 0;\n                                                                setNewService((prev)=>({\n                                                                        ...prev,\n                                                                        durations: newDurations\n                                                                    }));\n                                                            },\n                                                            className: \"w-32 input-mobile\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-400\",\n                                                            children: \"ريال\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                            lineNumber: 471,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 448,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-4 justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                onClick: addNewService,\n                                                className: \"btn-primary-mobile\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircle_Copy_DollarSign_Plus_Save_Star_Target_Trash2_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-5 w-5 ml-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                        lineNumber: 478,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \"إضافة الخدمة\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                lineNumber: 477,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                variant: \"secondary\",\n                                                onClick: ()=>setShowAddForm(false),\n                                                className: \"btn-secondary-mobile\",\n                                                children: \"إلغاء\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                            lineNumber: 360,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"card-mobile mt-8 bg-gradient-to-r from-purple-500/20 to-pink-500/20 border-purple-500/30\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-bold text-white mb-4\",\n                                    children: \"\\uD83D\\uDCA1 نصائح لتحديد الأسعار\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl mb-2\",\n                                                    children: \"\\uD83D\\uDCCA\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium mb-1\",\n                                                    children: \"ادرس السوق\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: \"راجع أسعار المنافسين وحدد أسعارك بناءً على جودة عملك\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 502,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl mb-2\",\n                                                    children: \"⏰\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 505,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium mb-1\",\n                                                    children: \"احسب الوقت\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 506,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: \"احسب الوقت المطلوب لكل مشروع واضرب في سعر ساعتك\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 507,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                            lineNumber: 504,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl mb-2\",\n                                                    children: \"\\uD83D\\uDCC8\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium mb-1\",\n                                                    children: \"ارفع تدريجياً\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 511,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: \"ابدأ بأسعار معقولة وارفعها تدريجياً مع زيادة خبرتك\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                            lineNumber: 509,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                                    lineNumber: 498,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                            lineNumber: 496,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                        lineNumber: 495,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n                lineNumber: 205,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\pricing\\\\page.tsx\",\n        lineNumber: 183,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PricingSettingsPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/ugc-creator/pricing/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/Button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst Button = ({ variant = \"primary\", size = \"md\", isLoading = false, children, className, disabled, ...props })=>{\n    const baseClasses = \"inline-flex items-center justify-center font-semibold rounded-2xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed touch-target relative overflow-hidden gpu-accelerated\";\n    const variantClasses = {\n        primary: \"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white focus:ring-green-500 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\",\n        secondary: \"bg-white hover:bg-gray-50 text-gray-700 hover:text-green-600 border-2 border-gray-200 hover:border-green-300 focus:ring-green-500 shadow-sm hover:shadow-md\",\n        outline: \"border-2 border-green-500 hover:border-green-600 text-green-600 hover:text-white hover:bg-green-500 bg-transparent focus:ring-green-500 shadow-sm hover:shadow-md\",\n        ghost: \"text-gray-700 hover:text-green-600 hover:bg-green-50 focus:ring-green-500\",\n        danger: \"bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white focus:ring-red-500 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\"\n    };\n    const sizeClasses = {\n        sm: \"px-4 py-3 text-sm min-h-[44px]\",\n        md: \"px-6 py-3 text-base min-h-[48px]\",\n        lg: \"px-8 py-4 text-lg min-h-[52px]\",\n        xl: \"px-10 py-5 text-xl min-h-[56px]\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(baseClasses, variantClasses[variant], sizeClasses[size], className),\n        disabled: disabled || isLoading,\n        ...props,\n        children: [\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-white\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                        className: \"opacity-25\",\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"10\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        className: \"opacity-75\",\n                        fill: \"currentColor\",\n                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Button);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/Card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst Card = ({ children, className, hover = false, padding = \"md\", shadow = \"md\" })=>{\n    const baseClasses = \"bg-white rounded-2xl border border-gray-100 transition-all duration-300 gpu-accelerated relative overflow-hidden\";\n    const paddingClasses = {\n        none: \"\",\n        sm: \"p-4\",\n        md: \"p-6\",\n        lg: \"p-8\",\n        xl: \"p-10\"\n    };\n    const shadowClasses = {\n        none: \"\",\n        sm: \"shadow-sm\",\n        md: \"shadow-lg\",\n        lg: \"shadow-xl\",\n        xl: \"shadow-2xl\"\n    };\n    const hoverClasses = hover ? \"hover:shadow-xl hover:-translate-y-2 cursor-pointer will-change-transform\" : \"\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(baseClasses, paddingClasses[padding], shadowClasses[shadow], hoverClasses, className),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Card);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/Input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst Input = ({ label, error, helperText, leftIcon, rightIcon, className, id, ...props })=>{\n    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;\n    const baseClasses = \"w-full px-4 py-3 border rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent\";\n    const stateClasses = error ? \"border-error-500 bg-error-50\" : \"border-gray-300 bg-white hover:border-gray-400 focus:border-primary-500\";\n    const iconPadding = leftIcon ? \"pr-12\" : rightIcon ? \"pl-12\" : \"\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full\",\n        children: [\n            label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                htmlFor: inputId,\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: label\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                lineNumber: 37,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    leftIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\",\n                        children: leftIcon\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        id: inputId,\n                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(baseClasses, stateClasses, iconPadding, className),\n                        ...props\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, undefined),\n                    rightIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400\",\n                        children: rightIcon\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-2 text-sm text-error-600\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                lineNumber: 68,\n                columnNumber: 9\n            }, undefined),\n            helperText && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-2 text-sm text-gray-500\",\n                children: helperText\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n                lineNumber: 72,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\ui\\\\Input.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Input);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Input.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"ef463d2d032b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW5mbHVlbmNlci1wbGF0Zm9ybS1zYXVkaS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/MzViMyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImVmNDYzZDJkMDMyYlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/ugc-creator/pricing/page.tsx":
/*!********************************************************!*\
  !*** ./src/app/dashboard/ugc-creator/pricing/page.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\amshor2\src\app\dashboard\ugc-creator\pricing\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\nconst metadata = {\n    title: \"منصة المؤثرين السعودية - ربط المؤثرين بالتجار\",\n    description: \"منصة احترافية لربط المؤثرين بالتجار في المملكة العربية السعودية مع ضمان الأموال ونظام الدفع الآمن\",\n    keywords: \"مؤثرين, تجار, السعودية, تسويق, إعلانات, سوشيال ميديا\",\n    authors: [\n        {\n            name: \"منصة المؤثرين السعودية\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\",\n    robots: \"index, follow\",\n    openGraph: {\n        title: \"منصة المؤثرين السعودية\",\n        description: \"منصة احترافية لربط المؤثرين بالتجار في المملكة العربية السعودية\",\n        type: \"website\",\n        locale: \"ar_SA\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 font-arabic\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    id: \"root\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fugc-creator%2Fpricing%2Fpage&page=%2Fdashboard%2Fugc-creator%2Fpricing%2Fpage&appPaths=%2Fdashboard%2Fugc-creator%2Fpricing%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fugc-creator%2Fpricing%2Fpage.tsx&appDir=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();