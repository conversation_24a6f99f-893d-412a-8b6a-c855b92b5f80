"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/register/merchant/page",{

/***/ "(app-pages-browser)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/facebook.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/instagram.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/youtube.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst Footer = ()=>{\n    const currentYear = new Date().getFullYear();\n    const footerLinks = {\n        platform: [\n            {\n                name: \"كيف يعمل\",\n                href: \"/how-it-works\"\n            },\n            {\n                name: \"الأسعار\",\n                href: \"/pricing\"\n            },\n            {\n                name: \"المؤثرين\",\n                href: \"/influencers\"\n            },\n            {\n                name: \"للتجار\",\n                href: \"/merchants\"\n            }\n        ],\n        support: [\n            {\n                name: \"مركز المساعدة\",\n                href: \"/help\"\n            },\n            {\n                name: \"اتصل بنا\",\n                href: \"/contact\"\n            },\n            {\n                name: \"الأسئلة الشائعة\",\n                href: \"/faq\"\n            },\n            {\n                name: \"الدعم الفني\",\n                href: \"/support\"\n            }\n        ],\n        legal: [\n            {\n                name: \"شروط الاستخدام\",\n                href: \"/terms\"\n            },\n            {\n                name: \"سياسة الخصوصية\",\n                href: \"/privacy\"\n            },\n            {\n                name: \"سياسة الاسترداد\",\n                href: \"/refund\"\n            },\n            {\n                name: \"اتفاقية المستخدم\",\n                href: \"/agreement\"\n            }\n        ],\n        company: [\n            {\n                name: \"من نحن\",\n                href: \"/about\"\n            },\n            {\n                name: \"فريق العمل\",\n                href: \"/team\"\n            },\n            {\n                name: \"الوظائف\",\n                href: \"/careers\"\n            },\n            {\n                name: \"الأخبار\",\n                href: \"/news\"\n            }\n        ]\n    };\n    const socialLinks = [\n        {\n            name: \"Facebook\",\n            icon: _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            href: \"#\"\n        },\n        {\n            name: \"Twitter\",\n            icon: _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            href: \"#\"\n        },\n        {\n            name: \"Instagram\",\n            icon: _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            href: \"#\"\n        },\n        {\n            name: \"YouTube\",\n            icon: _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            href: \"#\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-slate-900 text-white border-t border-purple-500/20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto mobile-container py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center glow-effect\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl\",\n                                                children: \"\\uD83D\\uDE80\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 53,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 52,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-3 text-xl font-bold\",\n                                            children: \"منصة المؤثرين السعودية\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 mb-6 leading-relaxed\",\n                                    children: \"منصة احترافية تربط المؤثرين بالتجار في المملكة العربية السعودية. نوفر بيئة آمنة وموثوقة للتعاون التجاري مع ضمان الأموال ونظام الدفع الآمن.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-gray-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-5 w-5 ml-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 65,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"<EMAIL>\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 66,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-gray-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-5 w-5 ml-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"+966 11 123 4567\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 70,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-gray-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-5 w-5 ml-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"الرياض، المملكة العربية السعودية\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 74,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"المنصة\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-3\",\n                                    children: footerLinks.platform.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: link.href,\n                                                className: \"text-gray-300 hover:text-white transition-colors duration-200\",\n                                                children: link.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, link.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"الدعم\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-3\",\n                                    children: footerLinks.support.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: link.href,\n                                                className: \"text-gray-300 hover:text-white transition-colors duration-200\",\n                                                children: link.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, link.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"قانوني\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-3\",\n                                    children: footerLinks.legal.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: link.href,\n                                                className: \"text-gray-300 hover:text-white transition-colors duration-200\",\n                                                children: link.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, link.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-gray-800 mt-12 pt-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-6 space-x-reverse mb-4 md:mb-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-300 font-medium\",\n                                        children: \"تابعنا على:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    socialLinks.map((social)=>{\n                                        const Icon = social.icon;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: social.href,\n                                            className: \"text-gray-400 hover:text-white transition-colors duration-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                className: \"h-6 w-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, social.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 19\n                                        }, undefined);\n                                    })\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-300 font-medium\",\n                                        children: \"اشترك في النشرة الإخبارية:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"email\",\n                                                placeholder: \"البريد الإلكتروني\",\n                                                className: \"px-4 py-2 bg-gray-800 border border-gray-700 rounded-r-lg focus:outline-none focus:ring-2 focus:ring-primary-500 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"px-4 py-2 bg-primary-600 hover:bg-primary-700 rounded-l-lg transition-colors duration-200\",\n                                                children: \"اشتراك\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-gray-800 mt-8 pt-8 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: [\n                            \"\\xa9 \",\n                            currentYear,\n                            \" منصة المؤثرين السعودية. جميع الحقوق محفوظة.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n            lineNumber: 46,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Footer;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Footer);\nvar _c;\n$RefreshReg$(_c, \"Footer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/Footer.tsx\n"));

/***/ })

});