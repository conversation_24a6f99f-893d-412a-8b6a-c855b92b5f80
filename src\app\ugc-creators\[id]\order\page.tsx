'use client'

import React, { useState } from 'react'
import { 
  Star, 
  Shield, 
  CheckCircle, 
  CreditCard, 
  MessageCircle,
  Video
} from 'lucide-react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import MobileNavigation from '@/components/layout/MobileNavigation'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import Breadcrumb from '@/components/ui/Breadcrumb'
import Container from '@/components/ui/Container'
import Badge from '@/components/ui/Badge'

interface OrderServicePageProps {
  params: {
    id: string
  }
}

const OrderServicePage: React.FC<OrderServicePageProps> = ({ params }) => {
  const [selectedService, setSelectedService] = useState('')
  const [customBudget, setCustomBudget] = useState('')
  const [orderData, setOrderData] = useState({
    serviceType: '',
    productDetails: '',
    deliveryDate: ''
  })

  const creator = {
    id: parseInt(params.id),
    name: 'سارة المبدعة',
    avatar: '👩‍🎨',
    rating: 4.9,
    reviewCount: 127,
    responseTime: '2 ساعة',
    services: [
      {
        id: 'tiktok-video',
        title: 'فيديو تيك توك',
        description: 'فيديو إبداعي 30-60 ثانية',
        icon: '📱',
        basePrice: 500
      },
      {
        id: 'product-review',
        title: 'مراجعة منتج',
        description: 'مراجعة شاملة مع تجربة',
        icon: '⭐',
        basePrice: 800
      },
      {
        id: 'custom',
        title: 'خدمة مخصصة',
        description: 'حدد متطلباتك الخاصة',
        icon: '🎯',
        basePrice: 0
      }
    ]
  }

  const selectedServiceData = creator.services.find(s => s.id === selectedService)

  const handleInputChange = (field: string, value: any) => {
    setOrderData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const calculatePrice = () => {
    if (selectedService === 'custom') {
      return parseInt(customBudget) || 0
    }
    return selectedServiceData?.basePrice || 0
  }

  const handleSubmitOrder = () => {
    alert('تم إرسال طلبك بنجاح! سيتم التواصل معك قريباً لتأكيد التفاصيل.')
  }

  return (
    <div className="min-h-screen bg-slate-900">
      <Header />
      
      <div className="py-8">
        <Container size="lg">
          <div className="mb-6">
            <Breadcrumb items={[
              { label: 'الرئيسية', href: '/' },
              { label: 'مبدعي UGC', href: '/ugc-creators' },
              { label: creator.name, href: `/ugc-creators/${params.id}` },
              { label: 'طلب خدمة' }
            ]} />
          </div>

          <Card className="mb-8">
            <div className="flex items-center gap-4">
              <div className="w-16 h-16 bg-indigo-600 rounded-full flex items-center justify-center text-2xl">
                {creator.avatar}
              </div>
              <div className="flex-1">
                <h1 className="text-xl font-bold text-white mb-2">طلب خدمة من {creator.name}</h1>
                <div className="flex items-center gap-4 text-sm text-slate-400">
                  <div className="flex items-center">
                    <Star className="h-4 w-4 text-amber-400 fill-current ml-1" />
                    <span>{creator.rating} ({creator.reviewCount} تقييم)</span>
                  </div>
                  <div>يرد خلال {creator.responseTime}</div>
                </div>
              </div>
              <div className="flex gap-2">
                <Badge variant="success" size="sm">
                  <Shield className="w-3 h-3 ml-1" />
                  دفع آمن
                </Badge>
                <Badge variant="primary" size="sm">
                  <CheckCircle className="w-3 h-3 ml-1" />
                  ضمان الجودة
                </Badge>
              </div>
            </div>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2 space-y-6">
              <Card>
                <h2 className="text-lg font-semibold text-white mb-4">اختر نوع الخدمة</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {creator.services.map((service) => (
                    <div
                      key={service.id}
                      onClick={() => setSelectedService(service.id)}
                      className={`p-4 rounded-lg border cursor-pointer transition-colors ${
                        selectedService === service.id
                          ? 'border-indigo-500 bg-indigo-500/10'
                          : 'border-slate-600 bg-slate-800/50 hover:border-slate-500'
                      }`}
                    >
                      <div className="text-center">
                        <div className="text-2xl mb-2">{service.icon}</div>
                        <h3 className="font-medium text-white mb-1">{service.title}</h3>
                        <p className="text-slate-400 text-sm mb-2">{service.description}</p>
                        <div className="text-indigo-400 font-semibold">
                          {service.id === 'custom' ? 'سعر مخصص' : `من ${service.basePrice} ريال`}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </Card>

              {selectedService === 'custom' && (
                <Card>
                  <h3 className="text-lg font-bold text-white mb-4">تفاصيل الخدمة المخصصة</h3>
                  <div className="space-y-4">
                    <Input
                      label="اكتب تفاصيل الخدمة المطلوبة"
                      placeholder="مثال: فيديو تعليمي 5 دقائق مع مونتاج متقدم..."
                      value={orderData.serviceType}
                      onChange={(e) => handleInputChange('serviceType', e.target.value)}
                    />
                    <Input
                      label="الميزانية المقترحة (ريال)"
                      type="number"
                      placeholder="1000"
                      value={customBudget}
                      onChange={(e) => setCustomBudget(e.target.value)}
                    />
                  </div>
                </Card>
              )}

              <Card>
                <h3 className="text-lg font-bold text-white mb-4">تفاصيل المنتج/الخدمة</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-2">
                      وصف المنتج أو الخدمة
                    </label>
                    <textarea
                      className="input min-h-[120px]"
                      placeholder="اكتب وصفاً مفصلاً للمنتج أو الخدمة التي تريد الترويج لها..."
                      value={orderData.productDetails}
                      onChange={(e) => handleInputChange('productDetails', e.target.value)}
                    />
                  </div>

                  <Input
                    label="تاريخ التسليم المطلوب"
                    type="date"
                    value={orderData.deliveryDate}
                    onChange={(e) => handleInputChange('deliveryDate', e.target.value)}
                  />
                </div>
              </Card>
            </div>

            <div className="lg:col-span-1">
              <Card className="sticky top-8">
                <h3 className="text-lg font-bold text-white mb-4">ملخص الطلب</h3>

                {selectedServiceData && (
                  <div className="space-y-4">
                    <div className="flex items-center">
                      <span className="text-2xl ml-3">{selectedServiceData.icon}</span>
                      <div>
                        <div className="text-white font-medium">{selectedServiceData.title}</div>
                        <div className="text-slate-400 text-sm">{selectedServiceData.description}</div>
                      </div>
                    </div>

                    <div className="border-t border-slate-600 pt-4">
                      <div className="flex justify-between items-center">
                        <span className="text-lg font-bold text-white">الإجمالي:</span>
                        <span className="text-2xl font-bold text-indigo-400">{calculatePrice()} ريال</span>
                      </div>
                    </div>

                    <div className="space-y-3 pt-4">
                      <Button
                        variant="primary"
                        className="w-full"
                        onClick={handleSubmitOrder}
                        disabled={!selectedService || (selectedService === 'custom' && !customBudget)}
                      >
                        <CreditCard className="h-5 w-5 ml-2" />
                        إرسال الطلب والدفع
                      </Button>

                      <Button variant="secondary" className="w-full">
                        <MessageCircle className="h-5 w-5 ml-2" />
                        تواصل أولاً
                      </Button>
                    </div>

                    <div className="text-xs text-slate-400 text-center pt-2">
                      دفع آمن مع ضمان استرداد الأموال
                    </div>
                  </div>
                )}

                {!selectedService && (
                  <div className="text-center text-slate-400 py-8">
                    <Video className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>اختر نوع الخدمة لرؤية ملخص الطلب</p>
                  </div>
                )}
              </Card>
            </div>
          </div>
        </Container>
      </div>

      <Footer />
      <MobileNavigation />
    </div>
  )
}

export default OrderServicePage
