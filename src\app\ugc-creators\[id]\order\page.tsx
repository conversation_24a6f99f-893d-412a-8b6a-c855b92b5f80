'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import {
  ArrowRight,
  Upload,
  DollarSign,
  Calendar,
  MessageCircle,
  CheckCircle,
  Star,
  Shield,
  Clock,
  Video,
  Camera,
  Edit3,
  Zap,
  CreditCard,
  Wallet
} from 'lucide-react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import MobileNavigation from '@/components/layout/MobileNavigation'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'

interface OrderServicePageProps {
  params: {
    id: string
  }
}

const OrderServicePage: React.FC<OrderServicePageProps> = ({ params }) => {
  const [selectedService, setSelectedService] = useState('')
  const [customBudget, setCustomBudget] = useState('')

  // Get service from URL params
  React.useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search)
    const serviceParam = urlParams.get('service')
    if (serviceParam) {
      setSelectedService(serviceParam)
    }
  }, [])
  const [orderData, setOrderData] = useState({
    serviceType: '',
    productDetails: '',
    videoDuration: '',
    deliveryDate: '',
    budget: '',
    specialInstructions: '',
    productImages: [] as File[],
    urgentDelivery: false,
    revisions: '2'
  })

  // Mock creator data
  const creator = {
    id: parseInt(params.id),
    name: 'سارة المبدعة',
    avatar: '👩‍🎨',
    rating: 4.9,
    reviewCount: 127,
    responseTime: '2 ساعات',
    services: [
      {
        id: 'tiktok-video',
        title: 'فيديو تيك توك احترافي',
        description: 'فيديو تيك توك مبدع ومؤثر مع مونتاج احترافي',
        duration: '15-60 ثانية',
        basePrice: 400,
        deliveryTime: '24-48 ساعة',
        icon: '🎵'
      },
      {
        id: 'instagram-story',
        title: 'ستوري انستقرام تفاعلي',
        description: 'ستوريز انستقرام جذابة مع عناصر تفاعلية',
        duration: '15 ثانية',
        basePrice: 200,
        deliveryTime: '24 ساعة',
        icon: '📱'
      },
      {
        id: 'product-review',
        title: 'مراجعة منتج شاملة',
        description: 'مراجعة صادقة ومفصلة للمنتج مع عرض المميزات',
        duration: '30-90 ثانية',
        basePrice: 600,
        deliveryTime: '2-3 أيام',
        icon: '⭐'
      },
      {
        id: 'product-photography',
        title: 'تصوير منتج احترافي',
        description: 'تصوير منتج بجودة عالية مع إضاءة احترافية',
        duration: 'صور ثابتة',
        basePrice: 300,
        deliveryTime: '1-2 أيام',
        icon: '📸'
      },
      {
        id: 'custom',
        title: 'خدمة مخصصة',
        description: 'خدمة مخصصة حسب احتياجاتك',
        duration: 'حسب الطلب',
        basePrice: 0,
        deliveryTime: 'حسب الطلب',
        icon: '⚡'
      }
    ]
  }

  const videoDurations = [
    { id: '15s', label: '15 ثانية', multiplier: 1 },
    { id: '30s', label: '30 ثانية', multiplier: 1.5 },
    { id: '60s', label: '60 ثانية', multiplier: 2 },
    { id: '90s', label: '90 ثانية', multiplier: 2.5 },
    { id: '120s', label: '120 ثانية', multiplier: 3 }
  ]

  const urgentOptions = [
    { id: 'normal', label: 'تسليم عادي', multiplier: 1, icon: '📅' },
    { id: 'urgent', label: 'تسليم سريع (24 ساعة)', multiplier: 1.5, icon: '⚡' },
    { id: 'express', label: 'تسليم فوري (12 ساعة)', multiplier: 2, icon: '🚀' }
  ]

  const revisionOptions = [
    { id: '1', label: 'مراجعة واحدة', price: 0 },
    { id: '2', label: 'مراجعتان', price: 50 },
    { id: '3', label: '3 مراجعات', price: 100 },
    { id: 'unlimited', label: 'مراجعات غير محدودة', price: 200 }
  ]

  const selectedServiceData = creator.services.find(s => s.id === selectedService)
  const selectedDuration = videoDurations.find(d => d.id === orderData.videoDuration)
  const selectedUrgency = urgentOptions.find(u => u.id === (orderData.urgentDelivery ? 'urgent' : 'normal'))
  const selectedRevisions = revisionOptions.find(r => r.id === orderData.revisions)

  const calculatePrice = () => {
    if (!selectedServiceData) return 0

    if (selectedService === 'custom') {
      return parseInt(customBudget) || 0
    }

    let basePrice = selectedServiceData.basePrice

    if (selectedDuration) {
      basePrice *= selectedDuration.multiplier
    }

    if (selectedUrgency) {
      basePrice *= selectedUrgency.multiplier
    }

    if (selectedRevisions) {
      basePrice += selectedRevisions.price
    }

    return Math.round(basePrice)
  }

  const handleInputChange = (field: string, value: any) => {
    setOrderData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleFileUpload = (files: FileList | null) => {
    if (files) {
      setOrderData(prev => ({
        ...prev,
        productImages: Array.from(files)
      }))
    }
  }

  const handleSubmitOrder = () => {
    const finalPrice = calculatePrice()
    const orderSummary = {
      creator: creator.name,
      service: selectedServiceData?.title,
      price: finalPrice,
      ...orderData
    }

    console.log('Order submitted:', orderSummary)
    alert(`تم إرسال طلبك بنجاح!

الخدمة: ${selectedServiceData?.title}
السعر الإجمالي: ${finalPrice} ريال

سيتم التواصل معك قريباً لتأكيد التفاصيل.`)
  }

  return (
    <div className="min-h-screen bg-slate-900">
      <Header />

      <div className="py-12">
        <div className="max-w-4xl mx-auto mobile-container">
          {/* Header */}
          <div className="mb-8">
            <Link href={`/ugc-creators/${params.id}`} className="inline-flex items-center text-purple-400 hover:text-purple-300 mb-4">
              <ArrowRight className="h-5 w-5 ml-2" />
              العودة للملف الشخصي
            </Link>

            <div className="flex items-center gap-4 mb-6">
              <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-2xl">
                {creator.avatar}
              </div>
              <div>
                <h1 className="text-2xl font-bold text-white">طلب خدمة من {creator.name}</h1>
                <div className="flex items-center text-gray-300">
                  <Star className="h-4 w-4 text-yellow-400 fill-current ml-1" />
                  <span>{creator.rating} ({creator.reviewCount} تقييم)</span>
                  <span className="mx-2">•</span>
                  <span>يرد خلال {creator.responseTime}</span>
                </div>
              </div>
            </div>

            {/* Trust Badges */}
            <div className="flex flex-wrap gap-2">
              <div className="trust-indicator">
                <Shield className="w-3 h-3" />
                🛡️ دفع آمن ومضمون
              </div>
              <div className="trust-indicator">
                <CheckCircle className="w-3 h-3" />
                ✅ ضمان الجودة
              </div>
              <div className="trust-indicator">
                <Clock className="w-3 h-3" />
                ⏰ تسليم في الوقت المحدد
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Order Form */}
            <div className="lg:col-span-2 space-y-6">
              {/* Service Selection */}
              <Card className="card-mobile">
                <h2 className="text-xl font-bold text-white mb-4">اختر نوع الخدمة</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {creator.services.map((service) => (
                    <div
                      key={service.id}
                      onClick={() => setSelectedService(service.id)}
                      className={`p-4 rounded-2xl border-2 cursor-pointer transition-all duration-300 ${
                        selectedService === service.id
                          ? 'border-purple-500 bg-purple-500/20'
                          : 'border-gray-600 bg-white/5 hover:border-purple-400'
                      }`}
                    >
                      <div className="text-center">
                        <div className="text-3xl mb-2">{service.icon}</div>
                        <h3 className="font-semibold text-white mb-1">{service.title}</h3>
                        <p className="text-gray-400 text-sm mb-2">{service.description}</p>
                        <div className="text-purple-400 font-bold">
                          {service.id === 'custom' ? 'سعر مخصص' : `من ${service.basePrice} ريال`}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </Card>

              {selectedService === 'custom' && (
                <Card className="card-mobile">
                  <h3 className="text-lg font-bold text-white mb-4">تفاصيل الخدمة المخصصة</h3>
                  <div className="space-y-4">
                    <Input
                      label="اكتب تفاصيل الخدمة المطلوبة"
                      placeholder="مثال: فيديو تعليمي 5 دقائق مع مونتاج متقدم..."
                      value={orderData.serviceType}
                      onChange={(e) => handleInputChange('serviceType', e.target.value)}
                    />
                    <Input
                      label="الميزانية المقترحة (ريال)"
                      type="number"
                      placeholder="1000"
                      value={customBudget}
                      onChange={(e) => setCustomBudget(e.target.value)}
                    />
                  </div>
                </Card>
              )}

              {selectedService && selectedService !== 'custom' && (
                <>
                  {/* Video Duration */}
                  {(selectedService === 'tiktok-video' || selectedService === 'product-review') && (
                    <Card className="card-mobile">
                      <h3 className="text-lg font-bold text-white mb-4">مدة الفيديو</h3>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                        {videoDurations.map((duration) => (
                          <div
                            key={duration.id}
                            onClick={() => handleInputChange('videoDuration', duration.id)}
                            className={`p-3 rounded-xl border cursor-pointer text-center transition-all ${
                              orderData.videoDuration === duration.id
                                ? 'border-cyan-500 bg-cyan-500/20 text-cyan-400'
                                : 'border-gray-600 bg-white/5 text-gray-300 hover:border-cyan-400'
                            }`}
                          >
                            <div className="font-medium">{duration.label}</div>
                            <div className="text-xs opacity-75">×{duration.multiplier}</div>
                          </div>
                        ))}
                      </div>
                    </Card>
                  )}

                  {/* Urgency */}
                  <Card className="card-mobile">
                    <h3 className="text-lg font-bold text-white mb-4">سرعة التسليم</h3>
                    <div className="space-y-3">
                      {urgentOptions.map((option) => (
                        <div
                          key={option.id}
                          onClick={() => handleInputChange('urgentDelivery', option.id !== 'normal')}
                          className={`p-4 rounded-xl border cursor-pointer transition-all ${
                            (option.id === 'normal' && !orderData.urgentDelivery) ||
                            (option.id !== 'normal' && orderData.urgentDelivery)
                              ? 'border-pink-500 bg-pink-500/20'
                              : 'border-gray-600 bg-white/5 hover:border-pink-400'
                          }`}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center">
                              <span className="text-2xl ml-3">{option.icon}</span>
                              <div>
                                <div className="text-white font-medium">{option.label}</div>
                                <div className="text-gray-400 text-sm">مضاعف السعر: ×{option.multiplier}</div>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </Card>

                  {/* Revisions */}
                  <Card className="card-mobile">
                    <h3 className="text-lg font-bold text-white mb-4">عدد المراجعات</h3>
                    <div className="grid grid-cols-2 gap-3">
                      {revisionOptions.map((option) => (
                        <div
                          key={option.id}
                          onClick={() => handleInputChange('revisions', option.id)}
                          className={`p-3 rounded-xl border cursor-pointer text-center transition-all ${
                            orderData.revisions === option.id
                              ? 'border-green-500 bg-green-500/20 text-green-400'
                              : 'border-gray-600 bg-white/5 text-gray-300 hover:border-green-400'
                          }`}
                        >
                          <div className="font-medium">{option.label}</div>
                          <div className="text-xs opacity-75">
                            {option.price === 0 ? 'مجاناً' : `+${option.price} ريال`}
                          </div>
                        </div>
                      ))}
                    </div>
                  </Card>
                </>
              )}

              {/* Product Details */}
              <Card className="card-mobile">
                <h3 className="text-lg font-bold text-white mb-4">تفاصيل المنتج/الخدمة</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      وصف المنتج أو الخدمة
                    </label>
                    <textarea
                      className="input-mobile min-h-[120px]"
                      placeholder="اكتب وصفاً مفصلاً للمنتج أو الخدمة التي تريد الترويج لها..."
                      value={orderData.productDetails}
                      onChange={(e) => handleInputChange('productDetails', e.target.value)}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      صور المنتج (اختياري)
                    </label>
                    <div className="border-2 border-dashed border-purple-500/50 rounded-2xl p-6 text-center hover:border-purple-500 transition-colors">
                      <Upload className="h-8 w-8 text-purple-400 mx-auto mb-2" />
                      <div className="text-white mb-1">اسحب الصور هنا أو اضغط للاختيار</div>
                      <div className="text-gray-400 text-sm">JPG, PNG (حد أقصى 5MB لكل صورة)</div>
                      <input
                        type="file"
                        multiple
                        accept="image/*"
                        className="hidden"
                        onChange={(e) => handleFileUpload(e.target.files)}
                      />
                    </div>
                    {orderData.productImages.length > 0 && (
                      <div className="mt-2 text-sm text-green-400">
                        تم اختيار {orderData.productImages.length} صورة
                      </div>
                    )}
                  </div>

                  <Input
                    label="تاريخ التسليم المطلوب"
                    type="date"
                    value={orderData.deliveryDate}
                    onChange={(e) => handleInputChange('deliveryDate', e.target.value)}
                  />

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      تعليمات خاصة (اختياري)
                    </label>
                    <textarea
                      className="input-mobile"
                      placeholder="أي تعليمات أو ملاحظات خاصة..."
                      value={orderData.specialInstructions}
                      onChange={(e) => handleInputChange('specialInstructions', e.target.value)}
                    />
                  </div>
                </div>
              </Card>
            </div>

            {/* Order Summary */}
            <div className="lg:col-span-1">
              <Card className="card-mobile sticky top-8">
                <h3 className="text-lg font-bold text-white mb-4">ملخص الطلب</h3>

                {selectedServiceData && (
                  <div className="space-y-4">
                    <div className="flex items-center">
                      <span className="text-2xl ml-3">{selectedServiceData.icon}</span>
                      <div>
                        <div className="text-white font-medium">{selectedServiceData.title}</div>
                        <div className="text-gray-400 text-sm">{selectedServiceData.description}</div>
                      </div>
                    </div>

                    <div className="border-t border-gray-600 pt-4 space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-400">السعر الأساسي:</span>
                        <span className="text-white">
                          {selectedService === 'custom' ? customBudget || '0' : selectedServiceData.basePrice} ريال
                        </span>
                      </div>

                      {selectedDuration && selectedService !== 'custom' && (
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-400">مدة الفيديو ({selectedDuration.label}):</span>
                          <span className="text-cyan-400">×{selectedDuration.multiplier}</span>
                        </div>
                      )}

                      {orderData.urgentDelivery && selectedService !== 'custom' && (
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-400">تسليم سريع:</span>
                          <span className="text-pink-400">×1.5</span>
                        </div>
                      )}

                      {selectedRevisions && selectedRevisions.price > 0 && selectedService !== 'custom' && (
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-400">مراجعات إضافية:</span>
                          <span className="text-green-400">+{selectedRevisions.price} ريال</span>
                        </div>
                      )}
                    </div>

                    <div className="border-t border-gray-600 pt-4">
                      <div className="flex justify-between items-center">
                        <span className="text-lg font-bold text-white">الإجمالي:</span>
                        <span className="text-2xl font-bold text-gradient-primary">{calculatePrice()} ريال</span>
                      </div>
                    </div>

                    <div className="space-y-3 pt-4">
                      <Button
                        className="btn-primary-mobile w-full sparkle-container"
                        onClick={handleSubmitOrder}
                        disabled={!selectedService || (selectedService === 'custom' && !customBudget)}
                      >
                        <CreditCard className="h-5 w-5 ml-2" />
                        إرسال الطلب والدفع
                      </Button>

                      <Button variant="secondary" className="btn-secondary-mobile w-full">
                        <MessageCircle className="h-5 w-5 ml-2" />
                        تواصل أولاً
                      </Button>
                    </div>

                    <div className="text-xs text-gray-400 text-center pt-2">
                      🛡️ دفع آمن مع ضمان استرداد الأموال
                    </div>
                  </div>
                )}

                {!selectedService && (
                  <div className="text-center text-gray-400 py-8">
                    <Video className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>اختر نوع الخدمة لرؤية ملخص الطلب</p>
                  </div>
                )}
              </Card>
            </div>
          </div>
        </div>
      </div>

      <Footer />
      <MobileNavigation />
    </div>
  )
}

export default OrderServicePage
