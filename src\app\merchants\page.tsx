'use client'

import React from 'react'
import Link from 'next/link'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { 
  Store, 
  TrendingUp, 
  Users,
  Shield,
  BarChart3,
  Target,
  Zap,
  CheckCircle,
  ArrowLeft,
  Play,
  Star,
  DollarSign
} from 'lucide-react'

const MerchantsPage: React.FC = () => {
  const benefits = [
    {
      icon: Users,
      title: 'وصول لآلاف المؤثرين',
      description: 'اختر من بين أكثر من 10,000 مؤثر معتمد في جميع التخصصات'
    },
    {
      icon: Shield,
      title: 'ضمان الأموال',
      description: 'نحتفظ بأموالك في نظام ضمان آمن حتى إكمال الحملة بنجاح'
    },
    {
      icon: BarChart3,
      title: 'تقارير مفصلة',
      description: 'احصل على إحصائيات شاملة عن أداء حملاتك ومعدل التفاعل'
    },
    {
      icon: Target,
      title: 'استهداف دقيق',
      description: 'ابحث عن المؤثرين حسب المدينة والتخصص وعدد المتابعين'
    },
    {
      icon: Zap,
      title: 'تنفيذ سريع',
      description: 'ابدأ حملتك خلال دقائق واحصل على النتائج بسرعة'
    },
    {
      icon: DollarSign,
      title: 'أسعار شفافة',
      description: 'لا توجد رسوم خفية، أسعار واضحة ومحددة مسبقاً'
    }
  ]

  const howItWorks = [
    {
      step: 1,
      title: 'ابحث واختر',
      description: 'تصفح المؤثرين واختر الأنسب لعلامتك التجارية',
      icon: Users
    },
    {
      step: 2,
      title: 'اتفق وادفع',
      description: 'حدد تفاصيل الحملة وادفع بأمان عبر نظام الضمان',
      icon: Shield
    },
    {
      step: 3,
      title: 'راقب النتائج',
      description: 'تابع تقدم الحملة واحصل على تقارير مفصلة',
      icon: BarChart3
    }
  ]

  const successStories = [
    {
      company: 'متجر الأناقة',
      industry: 'الموضة',
      result: '300% زيادة في المبيعات',
      description: 'حققنا نتائج مذهلة من خلال التعاون مع مؤثرات الموضة المناسبات لعلامتنا التجارية',
      rating: 5
    },
    {
      company: 'مطعم الذواقة',
      industry: 'المطاعم',
      result: '150% زيادة في الزيارات',
      description: 'المنصة ساعدتنا في الوصول لعملاء جدد وزيادة شهرة المطعم بشكل كبير',
      rating: 5
    },
    {
      company: 'تطبيق التقنية',
      industry: 'التقنية',
      result: '50,000 تحميل جديد',
      description: 'تعاوننا مع مؤثرين تقنيين حقق لنا انتشاراً واسعاً وتحميلات كثيرة',
      rating: 5
    }
  ]

  const features = [
    'بحث متقدم بفلاتر ذكية',
    'مراجعة ملفات المؤثرين وأعمالهم السابقة',
    'نظام تقييم ومراجعات شفاف',
    'إدارة حملات متعددة',
    'تقارير أداء في الوقت الفعلي',
    'دعم فني متخصص 24/7',
    'أدوات تحليل متقدمة',
    'إشعارات فورية للتحديثات'
  ]

  const industries = [
    'الموضة والجمال',
    'الطعام والمطاعم',
    'التقنية والتطبيقات',
    'السفر والسياحة',
    'الصحة واللياقة',
    'التعليم والتدريب',
    'العقارات',
    'السيارات',
    'الألعاب والترفيه',
    'الخدمات المالية',
    'التجارة الإلكترونية',
    'الأطفال والعائلة'
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
      <Header />
      
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl md:text-5xl font-bold mb-6">
                نمّي أعمالك مع
                <span className="block text-yellow-300">أفضل المؤثرين</span>
              </h1>
              <p className="text-xl mb-8 opacity-90 leading-relaxed">
                اكتشف قوة التسويق بالمؤثرين وحقق نتائج استثنائية لعلامتك التجارية 
                مع ضمان الأموال ونظام الدفع الآمن
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 mb-8">
                <Link href="/register/merchant">
                  <Button size="xl" className="w-full sm:w-auto bg-white text-blue-600 hover:bg-gray-100">
                    ابدأ حملتك الآن
                    <ArrowLeft className="h-6 w-6 mr-3" />
                  </Button>
                </Link>
                <Button size="xl" variant="outline" className="w-full sm:w-auto border-white text-white hover:bg-white/10">
                  <Play className="h-6 w-6 ml-3" />
                  شاهد العرض التوضيحي
                </Button>
              </div>

              <div className="flex items-center gap-8 text-sm opacity-90">
                <div className="flex items-center">
                  <Shield className="h-5 w-5 ml-2" />
                  <span>ضمان الأموال</span>
                </div>
                <div className="flex items-center">
                  <CheckCircle className="h-5 w-5 ml-2" />
                  <span>مؤثرين معتمدين</span>
                </div>
                <div className="flex items-center">
                  <Star className="h-5 w-5 ml-2" />
                  <span>نتائج مضمونة</span>
                </div>
              </div>
            </div>

            <div className="relative">
              <div className="w-full h-96 bg-white/10 rounded-2xl backdrop-blur-sm border border-white/20 flex items-center justify-center">
                <Store className="h-32 w-32 text-white/50" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              لماذا تختار منصتنا؟
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              نوفر لك كل ما تحتاجه لإطلاق حملات تسويقية ناجحة مع أفضل المؤثرين
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {benefits.map((benefit, index) => {
              const Icon = benefit.icon
              return (
                <Card key={index} hover className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Icon className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">
                    {benefit.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {benefit.description}
                  </p>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              كيف تعمل المنصة؟
            </h2>
            <p className="text-xl text-gray-600">
              ثلاث خطوات بسيطة لإطلاق حملتك التسويقية
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {howItWorks.map((step, index) => {
              const Icon = step.icon
              return (
                <Card key={index} className="text-center relative">
                  <div className="w-16 h-16 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-2xl font-bold">
                    {step.step}
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">
                    {step.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {step.description}
                  </p>
                  {index < howItWorks.length - 1 && (
                    <div className="hidden md:block absolute top-8 -left-4 w-8 h-0.5 bg-gray-300"></div>
                  )}
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      {/* Success Stories */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              قصص نجاح عملائنا
            </h2>
            <p className="text-xl text-gray-600">
              اكتشف كيف حقق التجار نتائج مذهلة من خلال منصتنا
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {successStories.map((story, index) => (
              <Card key={index}>
                <div className="flex items-center mb-4">
                  {[...Array(story.rating)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <h3 className="text-lg font-bold text-gray-900 mb-2">
                  {story.company}
                </h3>
                <p className="text-sm text-gray-500 mb-3">{story.industry}</p>
                <div className="text-2xl font-bold text-primary-600 mb-4">
                  {story.result}
                </div>
                <p className="text-gray-700 italic">
                  "{story.description}"
                </p>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Features */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                ميزات متقدمة لإدارة حملاتك
              </h2>
              <p className="text-xl text-gray-600 mb-8">
                نوفر لك أدوات احترافية لإدارة ومتابعة حملاتك التسويقية بكفاءة عالية
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {features.map((feature, index) => (
                  <div key={index} className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 ml-3 flex-shrink-0" />
                    <span className="text-gray-700">{feature}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="relative">
              <Card className="p-8">
                <div className="text-center">
                  <BarChart3 className="h-24 w-24 text-primary-600 mx-auto mb-6" />
                  <h3 className="text-xl font-bold text-gray-900 mb-4">
                    لوحة تحكم متقدمة
                  </h3>
                  <p className="text-gray-600">
                    راقب أداء حملاتك في الوقت الفعلي مع تقارير مفصلة وإحصائيات شاملة
                  </p>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Industries */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              نخدم جميع الصناعات
            </h2>
            <p className="text-xl text-gray-600">
              مهما كان مجال عملك، لدينا المؤثرين المناسبين لك
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {industries.map((industry, index) => (
              <Card key={index} hover className="text-center p-4">
                <span className="text-gray-700 font-medium">{industry}</span>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            جاهز لبدء رحلة النجاح؟
          </h2>
          <p className="text-xl mb-8 opacity-90">
            انضم إلى آلاف التجار الذين يحققون نتائج استثنائية من خلال منصتنا
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
            <Link href="/register/merchant">
              <Button size="xl" className="w-full sm:w-auto bg-white text-blue-600 hover:bg-gray-100">
                ابدأ حملتك الأولى
                <ArrowLeft className="h-6 w-6 mr-3" />
              </Button>
            </Link>
            <Link href="/influencers">
              <Button size="xl" variant="outline" className="w-full sm:w-auto border-white text-white hover:bg-white/10">
                تصفح المؤثرين
                <Users className="h-6 w-6 mr-3" />
              </Button>
            </Link>
          </div>

          <div className="flex items-center justify-center gap-8 text-sm opacity-90">
            <div className="flex items-center">
              <Shield className="h-5 w-5 ml-2" />
              <span>ضمان استرداد الأموال</span>
            </div>
            <div className="flex items-center">
              <CheckCircle className="h-5 w-5 ml-2" />
              <span>دعم فني 24/7</span>
            </div>
            <div className="flex items-center">
              <TrendingUp className="h-5 w-5 ml-2" />
              <span>نتائج مضمونة</span>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}

export default MerchantsPage
