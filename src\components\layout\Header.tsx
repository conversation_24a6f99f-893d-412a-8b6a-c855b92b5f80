'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import Button from '../ui/Button'
import { Menu, X, User, Search, Bell, Shield, Star } from 'lucide-react'

const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const pathname = usePathname()

  const navigation = [
    { name: 'الرئيسية', href: '/' },
    { name: 'المؤثرين', href: '/influencers' },
    { name: 'مبدعي UGC', href: '/ugc-creators' },
    { name: 'الرسائل', href: '/messages' },
    { name: 'كيف يعمل', href: '/how-it-works' },
    { name: 'الأسعار', href: '/pricing' },
    { name: 'اتصل بنا', href: '/contact' },
  ]

  const isActive = (href: string) => pathname === href

  return (
    <header className="bg-slate-900/80 backdrop-blur-xl shadow-2xl sticky top-0 z-50 safe-area-top border-b border-purple-500/20">
      <div className="max-w-7xl mx-auto mobile-container">
        <div className="flex justify-between items-center h-16 md:h-20">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link href="/" className="flex items-center touch-target">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center shadow-lg glow-effect">
                <span className="text-white font-bold text-xl">🚀</span>
              </div>
              <div className="mr-3 hidden sm:block">
                <span className="text-xl font-bold text-white">منصة المؤثرين</span>
                <div className="flex items-center gap-2 mt-1">
                  <span className="trust-indicator">
                    <Shield className="w-3 h-3" />
                    منصة سعودية موثوقة
                  </span>
                  <span className="trust-indicator">
                    <Star className="w-3 h-3" />
                    معتمدة ومرخصة
                  </span>
                </div>
              </div>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-8 space-x-reverse">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className={`px-4 py-2 rounded-xl text-sm font-semibold transition-all duration-300 ${
                  isActive(item.href)
                    ? 'text-purple-400 bg-purple-500/20 border border-purple-500/30'
                    : 'text-gray-300 hover:text-white hover:bg-white/10 border border-transparent'
                }`}
              >
                {item.name}
              </Link>
            ))}
          </nav>

          {/* Desktop Actions */}
          <div className="hidden md:flex items-center space-x-4 space-x-reverse">
            <button className="touch-target p-3 text-gray-400 hover:text-white hover:bg-white/10 rounded-xl transition-all">
              <Search className="h-5 w-5" />
            </button>
            <button className="touch-target p-3 text-gray-400 hover:text-white hover:bg-white/10 rounded-xl transition-all relative pulse-ring-effect">
              <Bell className="h-5 w-5" />
              <span className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-pink-500 to-red-500 rounded-full animate-pulse"></span>
            </button>
            <Link href="/login">
              <button className="btn-secondary-mobile">
                <User className="h-4 w-4 ml-2" />
                تسجيل الدخول
              </button>
            </Link>
            <Link href="/register">
              <button className="btn-primary-mobile sparkle-container">
                ✨ إنشاء حساب
              </button>
            </Link>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="touch-target p-3 rounded-2xl text-gray-600 hover:text-gray-800 hover:bg-gray-100 focus:outline-none transition-all"
            >
              {isMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden slide-up">
            <div className="px-4 pt-4 pb-6 space-y-3 bg-slate-800/95 backdrop-blur-xl border-t border-purple-500/20 shadow-2xl">
              {/* شارة الثقة للجوال */}
              <div className="flex items-center justify-center gap-4 py-3 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-2xl mb-4 sparkle-container">
                <span className="trust-indicator">
                  <Shield className="w-4 h-4" />
                  منصة سعودية موثوقة
                </span>
                <span className="trust-indicator">
                  <Star className="w-4 h-4" />
                  معتمدة ومرخصة
                </span>
              </div>

              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`block px-6 py-4 rounded-2xl text-base font-medium transition-all touch-target ${
                    isActive(item.href)
                      ? 'text-purple-400 bg-purple-500/20 border-2 border-purple-500/30'
                      : 'text-gray-300 hover:text-white hover:bg-white/10 border-2 border-transparent'
                  }`}
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}

              <div className="pt-4 border-t border-purple-500/20">
                <div className="space-y-3">
                  <Link href="/login" onClick={() => setIsMenuOpen(false)}>
                    <button className="btn-secondary-mobile w-full">
                      <User className="h-4 w-4 ml-2" />
                      تسجيل الدخول
                    </button>
                  </Link>
                  <Link href="/register" onClick={() => setIsMenuOpen(false)}>
                    <button className="btn-primary-mobile w-full sparkle-container">
                      🚀 إنشاء حساب
                    </button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  )
}

export default Header
