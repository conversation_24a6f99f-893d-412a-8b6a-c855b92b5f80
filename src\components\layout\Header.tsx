'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import Button from '../ui/Button'
import { Menu, X, User, Search, Bell, Shield, Star } from 'lucide-react'

const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const pathname = usePathname()

  const navigation = [
    { name: 'الرئيسية', href: '/' },
    { name: 'المؤثرين', href: '/influencers' },
    { name: 'كيف يعمل', href: '/how-it-works' },
    { name: 'الأسعار', href: '/pricing' },
    { name: 'اتصل بنا', href: '/contact' },
  ]

  const isActive = (href: string) => pathname === href

  return (
    <header className="bg-white shadow-lg sticky top-0 z-50 safe-area-top">
      <div className="max-w-7xl mx-auto mobile-container">
        <div className="flex justify-between items-center h-16 md:h-20">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link href="/" className="flex items-center touch-target">
              <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-2xl flex items-center justify-center shadow-lg">
                <span className="text-white font-bold text-xl">م</span>
              </div>
              <div className="mr-3 hidden sm:block">
                <span className="text-xl font-bold text-gray-900">منصة المؤثرين</span>
                <div className="flex items-center gap-2 mt-1">
                  <span className="trust-indicator">
                    <Shield className="w-3 h-3" />
                    منصة سعودية موثوقة
                  </span>
                  <span className="trust-indicator">
                    <Star className="w-3 h-3" />
                    معتمدة ومرخصة
                  </span>
                </div>
              </div>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-8 space-x-reverse">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
                  isActive(item.href)
                    ? 'text-primary-600 bg-primary-50'
                    : 'text-gray-700 hover:text-primary-600 hover:bg-gray-50'
                }`}
              >
                {item.name}
              </Link>
            ))}
          </nav>

          {/* Desktop Actions */}
          <div className="hidden md:flex items-center space-x-4 space-x-reverse">
            <button className="touch-target p-3 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-xl transition-all">
              <Search className="h-5 w-5" />
            </button>
            <button className="touch-target p-3 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-xl transition-all relative">
              <Bell className="h-5 w-5" />
              <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
            </button>
            <Link href="/login">
              <button className="btn-secondary-mobile">
                <User className="h-4 w-4 ml-2" />
                تسجيل الدخول
              </button>
            </Link>
            <Link href="/register">
              <button className="btn-primary-mobile">
                إنشاء حساب
              </button>
            </Link>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="touch-target p-3 rounded-2xl text-gray-600 hover:text-gray-800 hover:bg-gray-100 focus:outline-none transition-all"
            >
              {isMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden slide-up">
            <div className="px-4 pt-4 pb-6 space-y-3 bg-white border-t border-gray-100 shadow-lg">
              {/* شارة الثقة للجوال */}
              <div className="flex items-center justify-center gap-4 py-3 bg-green-50 rounded-2xl mb-4">
                <span className="trust-indicator">
                  <Shield className="w-4 h-4" />
                  منصة سعودية موثوقة
                </span>
                <span className="trust-indicator">
                  <Star className="w-4 h-4" />
                  معتمدة ومرخصة
                </span>
              </div>

              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`block px-6 py-4 rounded-2xl text-base font-medium transition-all touch-target ${
                    isActive(item.href)
                      ? 'text-green-600 bg-green-50 border-2 border-green-200'
                      : 'text-gray-700 hover:text-green-600 hover:bg-gray-50 border-2 border-transparent'
                  }`}
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}

              <div className="pt-4 border-t border-gray-100">
                <div className="space-y-3">
                  <Link href="/login" onClick={() => setIsMenuOpen(false)}>
                    <button className="btn-secondary-mobile w-full">
                      <User className="h-4 w-4 ml-2" />
                      تسجيل الدخول
                    </button>
                  </Link>
                  <Link href="/register" onClick={() => setIsMenuOpen(false)}>
                    <button className="btn-primary-mobile w-full">
                      إنشاء حساب
                    </button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  )
}

export default Header
