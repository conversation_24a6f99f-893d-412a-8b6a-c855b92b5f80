'use client'

import React from 'react'
import Link from 'next/link'
import { 
  Users, 
  Mail, 
  Linkedin, 
  Twitter, 
  Github,
  Award,
  Star,
  Heart,
  Code,
  Palette,
  BarChart3,
  Shield,
  MessageCircle,
  Zap
} from 'lucide-react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import MobileNavigation from '@/components/layout/MobileNavigation'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Badge from '@/components/ui/Badge'
import Container from '@/components/ui/Container'

const TeamPage: React.FC = () => {
  const teamMembers = [
    {
      id: 1,
      name: 'أحمد محمد',
      role: 'المؤسس والرئيس التنفيذي',
      avatar: '👨‍💼',
      bio: 'خبرة 10 سنوات في مجال التسويق الرقمي وريادة الأعمال. قاد عدة مشاريع ناجحة في مجال التكنولوجيا.',
      specialties: ['القيادة', 'الاستراتيجية', 'ريادة الأعمال'],
      social: {
        email: '<EMAIL>',
        linkedin: '#',
        twitter: '#'
      }
    },
    {
      id: 2,
      name: 'فاطمة العلي',
      role: 'مديرة التطوير التقني',
      avatar: '👩‍💻',
      bio: 'مطورة برمجيات متخصصة في تطوير المنصات الرقمية. خبرة 8 سنوات في تطوير التطبيقات والمواقع.',
      specialties: ['تطوير البرمجيات', 'الأمان السيبراني', 'إدارة الفرق'],
      social: {
        email: '<EMAIL>',
        linkedin: '#',
        github: '#'
      }
    },
    {
      id: 3,
      name: 'سارة الزهراني',
      role: 'مديرة التسويق والمحتوى',
      avatar: '👩‍🎨',
      bio: 'خبيرة في التسويق الرقمي وإدارة المحتوى. تخصصت في بناء العلامات التجارية والتسويق عبر وسائل التواصل.',
      specialties: ['التسويق الرقمي', 'إدارة المحتوى', 'وسائل التواصل'],
      social: {
        email: '<EMAIL>',
        linkedin: '#',
        twitter: '#'
      }
    },
    {
      id: 4,
      name: 'محمد الشهري',
      role: 'مدير تجربة المستخدم',
      avatar: '👨‍🎨',
      bio: 'مصمم UX/UI متخصص في تصميم التجارب الرقمية. يركز على إنشاء واجهات سهلة الاستخدام وجذابة.',
      specialties: ['تصميم UX/UI', 'تجربة المستخدم', 'التصميم الجرافيكي'],
      social: {
        email: '<EMAIL>',
        linkedin: '#'
      }
    },
    {
      id: 5,
      name: 'نورا القحطاني',
      role: 'مديرة علاقات المؤثرين',
      avatar: '👩‍💼',
      bio: 'متخصصة في إدارة علاقات المؤثرين وبناء الشراكات الاستراتيجية. خبرة واسعة في صناعة التأثير الرقمي.',
      specialties: ['إدارة المؤثرين', 'الشراكات', 'التفاوض'],
      social: {
        email: '<EMAIL>',
        linkedin: '#'
      }
    },
    {
      id: 6,
      name: 'خالد الدوسري',
      role: 'مدير الدعم الفني',
      avatar: '👨‍🔧',
      bio: 'متخصص في الدعم الفني وخدمة العملاء. يضمن تقديم أفضل تجربة دعم للمستخدمين على مدار الساعة.',
      specialties: ['الدعم الفني', 'خدمة العملاء', 'حل المشاكل'],
      social: {
        email: '<EMAIL>',
        linkedin: '#'
      }
    }
  ]

  const departments = [
    {
      name: 'التطوير التقني',
      icon: Code,
      color: 'from-blue-500 to-blue-600',
      description: 'فريق متخصص في تطوير وصيانة المنصة التقنية',
      members: 8
    },
    {
      name: 'التسويق والمحتوى',
      icon: Palette,
      color: 'from-purple-500 to-purple-600',
      description: 'فريق إبداعي متخصص في التسويق وإنتاج المحتوى',
      members: 6
    },
    {
      name: 'علاقات المؤثرين',
      icon: Users,
      color: 'from-green-500 to-green-600',
      description: 'فريق متخصص في إدارة علاقات المؤثرين والشراكات',
      members: 5
    },
    {
      name: 'الدعم الفني',
      icon: MessageCircle,
      color: 'from-orange-500 to-orange-600',
      description: 'فريق دعم فني متاح 24/7 لمساعدة المستخدمين',
      members: 4
    },
    {
      name: 'الأمان والجودة',
      icon: Shield,
      color: 'from-red-500 to-red-600',
      description: 'فريق متخصص في ضمان أمان المنصة وجودة الخدمات',
      members: 3
    },
    {
      name: 'التحليل والبيانات',
      icon: BarChart3,
      color: 'from-cyan-500 to-cyan-600',
      description: 'فريق متخصص في تحليل البيانات وتحسين الأداء',
      members: 4
    }
  ]

  const values = [
    {
      icon: Heart,
      title: 'الشغف بالتميز',
      description: 'نؤمن بأن الشغف هو المحرك الأساسي للإبداع والتميز'
    },
    {
      icon: Users,
      title: 'العمل الجماعي',
      description: 'نحقق أفضل النتائج من خلال التعاون والعمل كفريق واحد'
    },
    {
      icon: Zap,
      title: 'الابتكار المستمر',
      description: 'نسعى دائماً لإيجاد حلول مبتكرة وتطوير خدماتنا'
    },
    {
      icon: Star,
      title: 'الجودة العالية',
      description: 'نلتزم بتقديم أعلى مستويات الجودة في جميع خدماتنا'
    }
  ]

  const getSpecialtyIcon = (specialty: string) => {
    switch (specialty) {
      case 'القيادة': return '👑'
      case 'تطوير البرمجيات': return '💻'
      case 'التسويق الرقمي': return '📱'
      case 'تصميم UX/UI': return '🎨'
      case 'إدارة المؤثرين': return '⭐'
      case 'الدعم الفني': return '🛠️'
      default: return '🔧'
    }
  }

  return (
    <div className="min-h-screen bg-slate-900">
      <Header />
      
      <div className="py-16">
        <Container size="lg">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
              فريق العمل
            </h1>
            <p className="text-xl text-slate-300 max-w-3xl mx-auto mb-8">
              نحن فريق متنوع من المتخصصين المتحمسين لبناء أفضل منصة للتسويق بالمؤثرين في المملكة العربية السعودية
            </p>
            <div className="flex items-center justify-center gap-8 text-slate-400">
              <div className="text-center">
                <div className="text-2xl font-bold text-white">30+</div>
                <div className="text-sm">عضو فريق</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-white">6</div>
                <div className="text-sm">أقسام متخصصة</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-white">5+</div>
                <div className="text-sm">سنوات خبرة</div>
              </div>
            </div>
          </div>

          {/* Leadership Team */}
          <div className="mb-16">
            <h2 className="text-3xl font-bold text-white text-center mb-12">الفريق القيادي</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {teamMembers.map((member) => (
                <Card key={member.id}>
                  <div className="text-center mb-6">
                    <div className="w-20 h-20 bg-indigo-600 rounded-full flex items-center justify-center text-3xl mx-auto mb-4">
                      {member.avatar}
                    </div>
                    <h3 className="text-xl font-bold text-white mb-1">{member.name}</h3>
                    <p className="text-indigo-400 font-medium">{member.role}</p>
                  </div>

                  <p className="text-slate-300 text-sm mb-4 leading-relaxed">
                    {member.bio}
                  </p>

                  <div className="mb-4">
                    <h4 className="text-white font-medium mb-2">التخصصات:</h4>
                    <div className="flex flex-wrap gap-2">
                      {member.specialties.map((specialty, index) => (
                        <div key={index} className="flex items-center gap-1">
                          <span className="text-sm">{getSpecialtyIcon(specialty)}</span>
                          <Badge variant="secondary" size="sm">
                            {specialty}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="flex justify-center gap-3 pt-4 border-t border-slate-700">
                    {member.social.email && (
                      <a href={`mailto:${member.social.email}`} className="text-slate-400 hover:text-white transition-colors">
                        <Mail className="h-5 w-5" />
                      </a>
                    )}
                    {member.social.linkedin && (
                      <a href={member.social.linkedin} className="text-slate-400 hover:text-white transition-colors">
                        <Linkedin className="h-5 w-5" />
                      </a>
                    )}
                    {member.social.twitter && (
                      <a href={member.social.twitter} className="text-slate-400 hover:text-white transition-colors">
                        <Twitter className="h-5 w-5" />
                      </a>
                    )}
                    {member.social.github && (
                      <a href={member.social.github} className="text-slate-400 hover:text-white transition-colors">
                        <Github className="h-5 w-5" />
                      </a>
                    )}
                  </div>
                </Card>
              ))}
            </div>
          </div>

          {/* Departments */}
          <div className="mb-16">
            <h2 className="text-3xl font-bold text-white text-center mb-12">الأقسام والفرق</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {departments.map((dept, index) => (
                <Card key={index}>
                  <div className="text-center">
                    <div className={`w-16 h-16 bg-gradient-to-r ${dept.color} rounded-full flex items-center justify-center mx-auto mb-4`}>
                      <dept.icon className="h-8 w-8 text-white" />
                    </div>
                    <h3 className="text-lg font-bold text-white mb-2">{dept.name}</h3>
                    <p className="text-slate-300 text-sm mb-4">{dept.description}</p>
                    <div className="flex items-center justify-center gap-2">
                      <Users className="h-4 w-4 text-slate-400" />
                      <span className="text-slate-400 text-sm">{dept.members} أعضاء</span>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>

          {/* Company Values */}
          <div className="mb-16">
            <h2 className="text-3xl font-bold text-white text-center mb-12">قيمنا المؤسسية</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {values.map((value, index) => (
                <Card key={index}>
                  <div className="text-center">
                    <div className="w-12 h-12 bg-indigo-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <value.icon className="h-6 w-6 text-white" />
                    </div>
                    <h3 className="text-lg font-bold text-white mb-2">{value.title}</h3>
                    <p className="text-slate-300 text-sm">{value.description}</p>
                  </div>
                </Card>
              ))}
            </div>
          </div>

          {/* Join Us CTA */}
          <div className="text-center">
            <Card className="bg-gradient-to-r from-indigo-600 to-purple-600">
              <div className="text-center">
                <h2 className="text-2xl font-bold text-white mb-4">انضم إلى فريقنا</h2>
                <p className="text-indigo-100 mb-6">
                  نبحث دائماً عن المواهب المتميزة للانضمام إلى فريقنا المتنامي
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Link href="/careers">
                    <Button variant="secondary" className="bg-white text-indigo-600 hover:bg-gray-100">
                      تصفح الوظائف المتاحة
                    </Button>
                  </Link>
                  <Link href="/contact">
                    <Button variant="outline" className="border-white text-white hover:bg-white/10">
                      تواصل معنا
                    </Button>
                  </Link>
                </div>
              </div>
            </Card>
          </div>
        </Container>
      </div>

      <Footer />
      <MobileNavigation />
    </div>
  )
}

export default TeamPage
