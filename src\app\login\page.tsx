'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import { 
  Mail, 
  Lock, 
  Eye, 
  EyeOff,
  ArrowLeft,
  Shield,
  Users,
  Store
} from 'lucide-react'

const LoginPage: React.FC = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    rememberMe: false
  })
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    
    // Simulate login process
    setTimeout(() => {
      setIsLoading(false)
      alert('تم تسجيل الدخول بنجاح!')
      // Redirect to dashboard based on user type
    }, 2000)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
      <Header />
      
      <div className="py-12">
        <div className="max-w-md mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
              <Shield className="h-8 w-8 text-white" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              تسجيل الدخول
            </h1>
            <p className="text-gray-600">
              ادخل إلى حسابك في منصة المؤثرين السعودية
            </p>
          </div>

          {/* Login Form */}
          <Card>
            <form onSubmit={handleSubmit} className="space-y-6">
              <Input
                label="البريد الإلكتروني"
                type="email"
                placeholder="<EMAIL>"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                leftIcon={<Mail className="h-5 w-5" />}
                required
              />
              
              <Input
                label="كلمة المرور"
                type={showPassword ? 'text' : 'password'}
                placeholder="أدخل كلمة المرور"
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                leftIcon={<Lock className="h-5 w-5" />}
                rightIcon={
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                  </button>
                }
                required
              />

              {/* Remember Me & Forgot Password */}
              <div className="flex items-center justify-between">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.rememberMe}
                    onChange={(e) => handleInputChange('rememberMe', e.target.checked)}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <span className="mr-2 text-sm text-gray-700">تذكرني</span>
                </label>
                
                <Link href="/forgot-password" className="text-sm text-primary-600 hover:text-primary-700">
                  نسيت كلمة المرور؟
                </Link>
              </div>

              {/* Submit Button */}
              <Button
                type="submit"
                size="lg"
                className="w-full"
                isLoading={isLoading}
              >
                {isLoading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}
                {!isLoading && <ArrowLeft className="h-5 w-5 mr-3" />}
              </Button>
            </form>
          </Card>

          {/* Register Links */}
          <div className="mt-8">
            <div className="text-center mb-6">
              <p className="text-gray-600">ليس لديك حساب؟</p>
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <Link href="/register/influencer">
                <Card hover className="text-center cursor-pointer">
                  <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <Users className="h-6 w-6 text-purple-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-1">مؤثر</h3>
                  <p className="text-sm text-gray-600">انضم كمؤثر</p>
                </Card>
              </Link>
              
              <Link href="/register/merchant">
                <Card hover className="text-center cursor-pointer">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <Store className="h-6 w-6 text-blue-600" />
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-1">تاجر</h3>
                  <p className="text-sm text-gray-600">انضم كتاجر</p>
                </Card>
              </Link>
            </div>
          </div>

          {/* Security Notice */}
          <div className="mt-8">
            <Card className="bg-blue-50 border-blue-200">
              <div className="flex items-start">
                <Shield className="h-5 w-5 text-blue-600 mt-0.5 ml-3 flex-shrink-0" />
                <div>
                  <h3 className="text-sm font-semibold text-blue-900 mb-1">
                    حماية معلوماتك
                  </h3>
                  <p className="text-sm text-blue-800">
                    نحن نستخدم أحدث تقنيات الحماية لضمان أمان معلوماتك الشخصية والمالية.
                  </p>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  )
}

export default LoginPage
