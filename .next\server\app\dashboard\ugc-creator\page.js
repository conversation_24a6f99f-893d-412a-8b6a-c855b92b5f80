/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/ugc-creator/page";
exports.ids = ["app/dashboard/ugc-creator/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fugc-creator%2Fpage&page=%2Fdashboard%2Fugc-creator%2Fpage&appPaths=%2Fdashboard%2Fugc-creator%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fugc-creator%2Fpage.tsx&appDir=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fugc-creator%2Fpage&page=%2Fdashboard%2Fugc-creator%2Fpage&appPaths=%2Fdashboard%2Fugc-creator%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fugc-creator%2Fpage.tsx&appDir=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'ugc-creator',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/ugc-creator/page.tsx */ \"(rsc)/./src/app/dashboard/ugc-creator/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/ugc-creator/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/ugc-creator/page\",\n        pathname: \"/dashboard/ugc-creator\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fugc-creator%2Fpage&page=%2Fdashboard%2Fugc-creator%2Fpage&appPaths=%2Fdashboard%2Fugc-creator%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fugc-creator%2Fpage.tsx&appDir=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Csrc%5Capp%5Cdashboard%5Cugc-creator%5Cpage.tsx&server=true!":
/*!********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Csrc%5Capp%5Cdashboard%5Cugc-creator%5Cpage.tsx&server=true! ***!
  \********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/ugc-creator/page.tsx */ \"(ssr)/./src/app/dashboard/ugc-creator/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDc2htNzElNUNEZXNrdG9wJTVDYW1zaG9yMiU1Q3NyYyU1Q2FwcCU1Q2Rhc2hib2FyZCU1Q3VnYy1jcmVhdG9yJTVDcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW5mbHVlbmNlci1wbGF0Zm9ybS1zYXVkaS8/ODI2MCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHNobTcxXFxcXERlc2t0b3BcXFxcYW1zaG9yMlxcXFxzcmNcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFx1Z2MtY3JlYXRvclxcXFxwYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Csrc%5Capp%5Cdashboard%5Cugc-creator%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Csrc%5Capp%5Cglobals.css&server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Csrc%5Capp%5Cglobals.css&server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/dashboard/ugc-creator/page.tsx":
/*!************************************************!*\
  !*** ./src/app/dashboard/ugc-creator/page.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Bell_Camera_CheckCircle_Clock_DollarSign_Download_Edit_Eye_Filter_MessageCircle_Settings_Target_TrendingUp_Video_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Bell,Camera,CheckCircle,Clock,DollarSign,Download,Edit,Eye,Filter,MessageCircle,Settings,Target,TrendingUp,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Bell_Camera_CheckCircle_Clock_DollarSign_Download_Edit_Eye_Filter_MessageCircle_Settings_Target_TrendingUp_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Bell,Camera,CheckCircle,Clock,DollarSign,Download,Edit,Eye,Filter,MessageCircle,Settings,Target,TrendingUp,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/video.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Bell_Camera_CheckCircle_Clock_DollarSign_Download_Edit_Eye_Filter_MessageCircle_Settings_Target_TrendingUp_Video_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Bell,Camera,CheckCircle,Clock,DollarSign,Download,Edit,Eye,Filter,MessageCircle,Settings,Target,TrendingUp,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/camera.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Bell_Camera_CheckCircle_Clock_DollarSign_Download_Edit_Eye_Filter_MessageCircle_Settings_Target_TrendingUp_Video_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Bell,Camera,CheckCircle,Clock,DollarSign,Download,Edit,Eye,Filter,MessageCircle,Settings,Target,TrendingUp,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Bell_Camera_CheckCircle_Clock_DollarSign_Download_Edit_Eye_Filter_MessageCircle_Settings_Target_TrendingUp_Video_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Bell,Camera,CheckCircle,Clock,DollarSign,Download,Edit,Eye,Filter,MessageCircle,Settings,Target,TrendingUp,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Bell_Camera_CheckCircle_Clock_DollarSign_Download_Edit_Eye_Filter_MessageCircle_Settings_Target_TrendingUp_Video_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Bell,Camera,CheckCircle,Clock,DollarSign,Download,Edit,Eye,Filter,MessageCircle,Settings,Target,TrendingUp,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Bell_Camera_CheckCircle_Clock_DollarSign_Download_Edit_Eye_Filter_MessageCircle_Settings_Target_TrendingUp_Video_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Bell,Camera,CheckCircle,Clock,DollarSign,Download,Edit,Eye,Filter,MessageCircle,Settings,Target,TrendingUp,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Bell_Camera_CheckCircle_Clock_DollarSign_Download_Edit_Eye_Filter_MessageCircle_Settings_Target_TrendingUp_Video_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Bell,Camera,CheckCircle,Clock,DollarSign,Download,Edit,Eye,Filter,MessageCircle,Settings,Target,TrendingUp,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Bell_Camera_CheckCircle_Clock_DollarSign_Download_Edit_Eye_Filter_MessageCircle_Settings_Target_TrendingUp_Video_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Bell,Camera,CheckCircle,Clock,DollarSign,Download,Edit,Eye,Filter,MessageCircle,Settings,Target,TrendingUp,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Bell_Camera_CheckCircle_Clock_DollarSign_Download_Edit_Eye_Filter_MessageCircle_Settings_Target_TrendingUp_Video_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Bell,Camera,CheckCircle,Clock,DollarSign,Download,Edit,Eye,Filter,MessageCircle,Settings,Target,TrendingUp,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Bell_Camera_CheckCircle_Clock_DollarSign_Download_Edit_Eye_Filter_MessageCircle_Settings_Target_TrendingUp_Video_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Bell,Camera,CheckCircle,Clock,DollarSign,Download,Edit,Eye,Filter,MessageCircle,Settings,Target,TrendingUp,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Bell_Camera_CheckCircle_Clock_DollarSign_Download_Edit_Eye_Filter_MessageCircle_Settings_Target_TrendingUp_Video_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Bell,Camera,CheckCircle,Clock,DollarSign,Download,Edit,Eye,Filter,MessageCircle,Settings,Target,TrendingUp,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Bell_Camera_CheckCircle_Clock_DollarSign_Download_Edit_Eye_Filter_MessageCircle_Settings_Target_TrendingUp_Video_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Bell,Camera,CheckCircle,Clock,DollarSign,Download,Edit,Eye,Filter,MessageCircle,Settings,Target,TrendingUp,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Bell_Camera_CheckCircle_Clock_DollarSign_Download_Edit_Eye_Filter_MessageCircle_Settings_Target_TrendingUp_Video_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Bell,Camera,CheckCircle,Clock,DollarSign,Download,Edit,Eye,Filter,MessageCircle,Settings,Target,TrendingUp,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Bell_Camera_CheckCircle_Clock_DollarSign_Download_Edit_Eye_Filter_MessageCircle_Settings_Target_TrendingUp_Video_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Bell,Camera,CheckCircle,Clock,DollarSign,Download,Edit,Eye,Filter,MessageCircle,Settings,Target,TrendingUp,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_Bell_Camera_CheckCircle_Clock_DollarSign_Download_Edit_Eye_Filter_MessageCircle_Settings_Target_TrendingUp_Video_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,Bell,Camera,CheckCircle,Clock,DollarSign,Download,Edit,Eye,Filter,MessageCircle,Settings,Target,TrendingUp,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Card */ \"(ssr)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst UGCCreatorDashboard = ()=>{\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"overview\");\n    // Mock data\n    const stats = {\n        totalEarnings: 15600,\n        activeProjects: 8,\n        completedProjects: 47,\n        averageRating: 4.9,\n        totalViews: 850000,\n        totalLikes: 42500,\n        responseTime: \"2 ساعات\",\n        completionRate: 98\n    };\n    const activeProjects = [\n        {\n            id: 1,\n            client: \"براند الجمال\",\n            title: \"فيديو تيك توك لمنتج جديد\",\n            type: \"فيديو تيك توك\",\n            budget: 800,\n            status: \"قيد التنفيذ\",\n            progress: 60,\n            deadline: \"2024-01-25\",\n            description: \"إنتاج فيديو تيك توك إبداعي لإطلاق منتج العناية الجديد\",\n            requirements: [\n                \"مدة 30 ثانية\",\n                \"مونتاج احترافي\",\n                \"موسيقى ترندنج\"\n            ],\n            clientAvatar: \"\\uD83D\\uDC84\"\n        },\n        {\n            id: 2,\n            client: \"متجر الأزياء\",\n            title: \"ستوريز انستقرام للمجموعة الشتوية\",\n            type: \"ستوري انستقرام\",\n            budget: 600,\n            status: \"في المراجعة\",\n            progress: 90,\n            deadline: \"2024-01-22\",\n            description: \"سلسلة ستوريز لعرض المجموعة الشتوية الجديدة\",\n            requirements: [\n                \"5 ستوريز\",\n                \"عناصر تفاعلية\",\n                \"تصوير عالي الجودة\"\n            ],\n            clientAvatar: \"\\uD83D\\uDC57\"\n        },\n        {\n            id: 3,\n            client: \"شركة التقنية\",\n            title: \"مراجعة تطبيق جديد\",\n            type: \"مراجعة منتج\",\n            budget: 1200,\n            status: \"جديد\",\n            progress: 10,\n            deadline: \"2024-01-30\",\n            description: \"مراجعة شاملة لتطبيق الذكي الجديد مع عرض المميزات\",\n            requirements: [\n                \"فيديو 60 ثانية\",\n                \"تجربة شخصية\",\n                \"مقارنات\"\n            ],\n            clientAvatar: \"\\uD83D\\uDCF1\"\n        }\n    ];\n    const recentEarnings = [\n        {\n            month: \"ديسمبر\",\n            amount: 4200,\n            projects: 12\n        },\n        {\n            month: \"نوفمبر\",\n            amount: 3800,\n            projects: 10\n        },\n        {\n            month: \"أكتوبر\",\n            amount: 5100,\n            projects: 15\n        },\n        {\n            month: \"سبتمبر\",\n            amount: 3600,\n            projects: 9\n        }\n    ];\n    const portfolioItems = [\n        {\n            id: 1,\n            title: \"مراجعة كريم الوجه\",\n            type: \"فيديو تيك توك\",\n            views: 25000,\n            likes: 1200,\n            thumbnail: \"\\uD83E\\uDDF4\",\n            platform: \"تيك توك\",\n            date: \"منذ 3 أيام\"\n        },\n        {\n            id: 2,\n            title: \"ستايلنج فستان سهرة\",\n            type: \"ستوري انستقرام\",\n            views: 18000,\n            likes: 890,\n            thumbnail: \"\\uD83D\\uDC57\",\n            platform: \"انستقرام\",\n            date: \"منذ أسبوع\"\n        },\n        {\n            id: 3,\n            title: \"روتين العناية اليومي\",\n            type: \"ريلز انستقرام\",\n            views: 32000,\n            likes: 1800,\n            thumbnail: \"✨\",\n            platform: \"انستقرام\",\n            date: \"منذ أسبوعين\"\n        }\n    ];\n    const notifications = [\n        {\n            id: 1,\n            type: \"new_project\",\n            title: \"طلب مشروع جديد\",\n            message: \"شركة التقنية أرسلت طلب مشروع جديد\",\n            time: \"منذ ساعة\",\n            icon: \"\\uD83D\\uDCF1\"\n        },\n        {\n            id: 2,\n            type: \"payment\",\n            title: \"تم استلام الدفعة\",\n            message: \"تم تحويل 800 ريال لحسابك\",\n            time: \"منذ 3 ساعات\",\n            icon: \"\\uD83D\\uDCB0\"\n        },\n        {\n            id: 3,\n            type: \"review\",\n            title: \"تقييم جديد\",\n            message: \"براند الجمال قيّمك بـ 5 نجوم\",\n            time: \"منذ يوم\",\n            icon: \"⭐\"\n        }\n    ];\n    const tabs = [\n        {\n            id: \"overview\",\n            label: \"نظرة عامة\",\n            icon: _barrel_optimize_names_AlertCircle_BarChart3_Bell_Camera_CheckCircle_Clock_DollarSign_Download_Edit_Eye_Filter_MessageCircle_Settings_Target_TrendingUp_Video_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            id: \"projects\",\n            label: \"المشاريع\",\n            icon: _barrel_optimize_names_AlertCircle_BarChart3_Bell_Camera_CheckCircle_Clock_DollarSign_Download_Edit_Eye_Filter_MessageCircle_Settings_Target_TrendingUp_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        },\n        {\n            id: \"portfolio\",\n            label: \"معرض الأعمال\",\n            icon: _barrel_optimize_names_AlertCircle_BarChart3_Bell_Camera_CheckCircle_Clock_DollarSign_Download_Edit_Eye_Filter_MessageCircle_Settings_Target_TrendingUp_Video_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        },\n        {\n            id: \"pricing\",\n            label: \"إعدادات الأسعار\",\n            icon: _barrel_optimize_names_AlertCircle_BarChart3_Bell_Camera_CheckCircle_Clock_DollarSign_Download_Edit_Eye_Filter_MessageCircle_Settings_Target_TrendingUp_Video_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        },\n        {\n            id: \"earnings\",\n            label: \"الأرباح\",\n            icon: _barrel_optimize_names_AlertCircle_BarChart3_Bell_Camera_CheckCircle_Clock_DollarSign_Download_Edit_Eye_Filter_MessageCircle_Settings_Target_TrendingUp_Video_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        },\n        {\n            id: \"messages\",\n            label: \"الرسائل\",\n            icon: _barrel_optimize_names_AlertCircle_BarChart3_Bell_Camera_CheckCircle_Clock_DollarSign_Download_Edit_Eye_Filter_MessageCircle_Settings_Target_TrendingUp_Video_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        },\n        {\n            id: \"settings\",\n            label: \"الإعدادات\",\n            icon: _barrel_optimize_names_AlertCircle_BarChart3_Bell_Camera_CheckCircle_Clock_DollarSign_Download_Edit_Eye_Filter_MessageCircle_Settings_Target_TrendingUp_Video_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n        }\n    ];\n    const formatNumber = (num)=>{\n        if (num >= 1000000) {\n            return (num / 1000000).toFixed(1) + \"M\";\n        } else if (num >= 1000) {\n            return (num / 1000).toFixed(1) + \"K\";\n        }\n        return num.toString();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-slate-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-slate-800/80 backdrop-blur-xl shadow-2xl sticky top-0 z-50 border-b border-purple-500/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto mobile-container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-lg\",\n                                                children: \"\\uD83C\\uDFAC\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-3 text-lg font-bold text-white\",\n                                            children: \"لوحة تحكم المبدع\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"relative p-2 text-gray-400 hover:text-white transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_Bell_Camera_CheckCircle_Clock_DollarSign_Download_Edit_Eye_Filter_MessageCircle_Settings_Target_TrendingUp_Video_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-6 w-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"absolute top-0 right-0 h-2 w-2 bg-red-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-gradient-to-br from-purple-400 to-pink-400 rounded-full flex items-center justify-center text-white font-bold\",\n                                                children: \"\\uD83D\\uDC69‍\\uD83C\\uDFA8\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"mr-3 text-sm font-medium text-white\",\n                                                children: \"سارة المبدعة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                lineNumber: 182,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto mobile-container py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col lg:flex-row gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:w-64 flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"card-mobile\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"space-y-2\",\n                                    children: tabs.map((tab)=>{\n                                        const Icon = tab.icon;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab(tab.id),\n                                            className: `w-full flex items-center px-3 py-3 rounded-xl text-right transition-all duration-300 ${activeTab === tab.id ? \"bg-purple-500/20 text-purple-400 border border-purple-500/30\" : \"text-gray-300 hover:bg-white/10 hover:text-white\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"h-5 w-5 ml-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: tab.label\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, tab.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 21\n                                        }, undefined);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                activeTab === \"overview\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl font-bold text-white mb-2\",\n                                                    children: \"مرحباً، سارة! \\uD83C\\uDFAC\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-300\",\n                                                    children: \"إليك نظرة عامة على أداءك وأرباحك كمبدعة محتوى\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"card-mobile text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-4xl mb-3\",\n                                                            children: \"\\uD83D\\uDCB0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-white mb-1\",\n                                                            children: [\n                                                                stats.totalEarnings.toLocaleString(),\n                                                                \" ريال\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                            lineNumber: 255,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: \"إجمالي الأرباح\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                            lineNumber: 256,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"card-mobile text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-4xl mb-3\",\n                                                            children: \"\\uD83C\\uDFAF\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                            lineNumber: 260,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-white mb-1\",\n                                                            children: stats.activeProjects\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                            lineNumber: 261,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: \"المشاريع النشطة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"card-mobile text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-4xl mb-3\",\n                                                            children: \"⭐\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                            lineNumber: 266,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-white mb-1\",\n                                                            children: stats.averageRating\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                            lineNumber: 267,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: \"متوسط التقييم\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"card-mobile text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-4xl mb-3\",\n                                                            children: \"\\uD83D\\uDC41️\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                            lineNumber: 272,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-white mb-1\",\n                                                            children: formatNumber(stats.totalViews)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: \"إجمالي المشاهدات\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                            lineNumber: 274,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"card-mobile\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-xl font-bold text-white\",\n                                                            children: \"المشاريع النشطة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            variant: \"secondary\",\n                                                            size: \"sm\",\n                                                            className: \"btn-secondary-mobile\",\n                                                            children: \"عرض الكل\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: activeProjects.slice(0, 3).map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border border-purple-500/20 rounded-2xl p-4 bg-white/5\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between mb-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-2xl ml-3\",\n                                                                                    children: project.clientAvatar\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                                    lineNumber: 292,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                            className: \"font-semibold text-white\",\n                                                                                            children: project.title\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                                            lineNumber: 294,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                            className: \"text-sm text-gray-400\",\n                                                                                            children: [\n                                                                                                project.client,\n                                                                                                \" • \",\n                                                                                                project.type\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                                            lineNumber: 295,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                                    lineNumber: 293,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                            lineNumber: 291,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-right\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-lg font-bold text-white\",\n                                                                                    children: [\n                                                                                        project.budget,\n                                                                                        \" ريال\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                                    lineNumber: 299,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-gray-400\",\n                                                                                    children: project.deadline\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                                    lineNumber: 300,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                            lineNumber: 298,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                    lineNumber: 290,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between mb-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1 bg-gray-700 rounded-full h-2 ml-4\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full transition-all duration-500\",\n                                                                                style: {\n                                                                                    width: `${project.progress}%`\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                                lineNumber: 306,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                            lineNumber: 305,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm font-medium text-white\",\n                                                                            children: [\n                                                                                project.progress,\n                                                                                \"%\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                            lineNumber: 311,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                    lineNumber: 304,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: `px-3 py-1 rounded-full text-xs font-medium ${project.status === \"قيد التنفيذ\" ? \"bg-blue-500/20 text-blue-400\" : project.status === \"في المراجعة\" ? \"bg-yellow-500/20 text-yellow-400\" : \"bg-green-500/20 text-green-400\"}`,\n                                                                            children: project.status\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                            lineNumber: 315,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                    size: \"sm\",\n                                                                                    variant: \"secondary\",\n                                                                                    className: \"btn-secondary-mobile\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_Bell_Camera_CheckCircle_Clock_DollarSign_Download_Edit_Eye_Filter_MessageCircle_Settings_Target_TrendingUp_Video_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                            className: \"h-4 w-4 ml-1\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                                            lineNumber: 324,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        \"عرض\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                                    lineNumber: 323,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                    size: \"sm\",\n                                                                                    className: \"btn-primary-mobile\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_Bell_Camera_CheckCircle_Clock_DollarSign_Download_Edit_Eye_Filter_MessageCircle_Settings_Target_TrendingUp_Video_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                            className: \"h-4 w-4 ml-1\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                                            lineNumber: 328,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        \"تحديث\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                                    lineNumber: 327,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                            lineNumber: 322,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                    lineNumber: 314,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, project.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"card-mobile\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-xl font-bold text-white\",\n                                                            children: \"أحدث الأعمال\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                            lineNumber: 341,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            variant: \"secondary\",\n                                                            size: \"sm\",\n                                                            className: \"btn-secondary-mobile\",\n                                                            children: \"إدارة المعرض\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                    children: portfolioItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-white/5 rounded-2xl p-4 hover:bg-white/10 transition-colors cursor-pointer\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center mb-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-4xl mb-2\",\n                                                                            children: item.thumbnail\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                            lineNumber: 351,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm text-gray-400\",\n                                                                            children: item.platform\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                            lineNumber: 352,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                    lineNumber: 350,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-semibold text-white mb-2 text-center\",\n                                                                    children: item.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                    lineNumber: 354,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between text-sm text-gray-400\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                formatNumber(item.views),\n                                                                                \" مشاهدة\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                            lineNumber: 356,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                formatNumber(item.likes),\n                                                                                \" إعجاب\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                            lineNumber: 357,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                    lineNumber: 355,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, item.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"card-mobile\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-bold text-white mb-6\",\n                                                    children: \"الإشعارات الحديثة\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center p-3 bg-white/5 rounded-xl hover:bg-white/10 transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-2xl ml-3\",\n                                                                    children: notification.icon\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                    lineNumber: 370,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-white font-medium\",\n                                                                            children: notification.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                            lineNumber: 372,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-gray-400 text-sm\",\n                                                                            children: notification.message\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                            lineNumber: 373,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                    lineNumber: 371,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-gray-400 text-xs\",\n                                                                    children: notification.time\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                    lineNumber: 375,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, notification.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                            lineNumber: 369,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 15\n                                }, undefined),\n                                activeTab === \"projects\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl font-bold text-white\",\n                                                    children: \"إدارة المشاريع\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                    lineNumber: 386,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            variant: \"secondary\",\n                                                            size: \"sm\",\n                                                            className: \"btn-secondary-mobile\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_Bell_Camera_CheckCircle_Clock_DollarSign_Download_Edit_Eye_Filter_MessageCircle_Settings_Target_TrendingUp_Video_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-4 w-4 ml-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                    lineNumber: 389,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                \"فلترة\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            variant: \"secondary\",\n                                                            size: \"sm\",\n                                                            className: \"btn-secondary-mobile\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_Bell_Camera_CheckCircle_Clock_DollarSign_Download_Edit_Eye_Filter_MessageCircle_Settings_Target_TrendingUp_Video_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-4 w-4 ml-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                    lineNumber: 393,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                \"تصدير\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                            lineNumber: 392,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"card-mobile text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_Bell_Camera_CheckCircle_Clock_DollarSign_Download_Edit_Eye_Filter_MessageCircle_Settings_Target_TrendingUp_Video_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-8 w-8 text-yellow-500 mx-auto mb-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                            lineNumber: 401,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-white\",\n                                                            children: \"8\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                            lineNumber: 402,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: \"قيد التنفيذ\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                            lineNumber: 403,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                    lineNumber: 400,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"card-mobile text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_Bell_Camera_CheckCircle_Clock_DollarSign_Download_Edit_Eye_Filter_MessageCircle_Settings_Target_TrendingUp_Video_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-8 w-8 text-green-500 mx-auto mb-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-white\",\n                                                            children: \"47\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                            lineNumber: 407,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: \"مكتملة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                            lineNumber: 408,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"card-mobile text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_Bell_Camera_CheckCircle_Clock_DollarSign_Download_Edit_Eye_Filter_MessageCircle_Settings_Target_TrendingUp_Video_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"h-8 w-8 text-blue-500 mx-auto mb-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                            lineNumber: 411,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-white\",\n                                                            children: \"3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                            lineNumber: 412,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: \"في المراجعة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                            lineNumber: 413,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"card-mobile text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_Bell_Camera_CheckCircle_Clock_DollarSign_Download_Edit_Eye_Filter_MessageCircle_Settings_Target_TrendingUp_Video_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            className: \"h-8 w-8 text-purple-500 mx-auto mb-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                            lineNumber: 416,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-white\",\n                                                            children: \"58\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                            lineNumber: 417,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-400\",\n                                                            children: \"إجمالي المشاريع\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                            lineNumber: 418,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"card-mobile\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: activeProjects.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border border-purple-500/20 rounded-2xl p-6 bg-white/5\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-3xl ml-4\",\n                                                                                children: project.clientAvatar\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                                lineNumber: 428,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                        className: \"font-bold text-white text-lg\",\n                                                                                        children: project.title\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                                        lineNumber: 430,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-gray-400\",\n                                                                                        children: [\n                                                                                            project.client,\n                                                                                            \" • \",\n                                                                                            project.type\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                                        lineNumber: 431,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                                lineNumber: 429,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                        lineNumber: 427,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                variant: \"secondary\",\n                                                                                size: \"sm\",\n                                                                                className: \"btn-secondary-mobile\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_Bell_Camera_CheckCircle_Clock_DollarSign_Download_Edit_Eye_Filter_MessageCircle_Settings_Target_TrendingUp_Video_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                        className: \"h-4 w-4 ml-2\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                                        lineNumber: 436,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    \"عرض التفاصيل\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                                lineNumber: 435,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                size: \"sm\",\n                                                                                className: \"btn-primary-mobile\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_Bell_Camera_CheckCircle_Clock_DollarSign_Download_Edit_Eye_Filter_MessageCircle_Settings_Target_TrendingUp_Video_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                        className: \"h-4 w-4 ml-2\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                                        lineNumber: 440,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    \"تحديث الحالة\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                                lineNumber: 439,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                        lineNumber: 434,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                lineNumber: 426,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300 mb-4\",\n                                                                children: project.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                lineNumber: 446,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-400 text-sm\",\n                                                                                children: \"الميزانية:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                                lineNumber: 450,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-bold text-white mr-2\",\n                                                                                children: [\n                                                                                    project.budget,\n                                                                                    \" ريال\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                                lineNumber: 451,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                        lineNumber: 449,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-400 text-sm\",\n                                                                                children: \"الموعد النهائي:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                                lineNumber: 454,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium text-white mr-2\",\n                                                                                children: project.deadline\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                                lineNumber: 455,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                        lineNumber: 453,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-400 text-sm\",\n                                                                                children: \"التقدم:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                                lineNumber: 458,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium text-white mr-2\",\n                                                                                children: [\n                                                                                    project.progress,\n                                                                                    \"%\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                                lineNumber: 459,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                        lineNumber: 457,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                lineNumber: 448,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-400 text-sm\",\n                                                                        children: \"المتطلبات:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                        lineNumber: 464,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex flex-wrap gap-2 mt-2\",\n                                                                        children: project.requirements.map((req, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"px-3 py-1 bg-cyan-500/20 text-cyan-300 text-sm rounded-full\",\n                                                                                children: req\n                                                                            }, i, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                                lineNumber: 467,\n                                                                                columnNumber: 31\n                                                                            }, undefined))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                        lineNumber: 465,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: `px-4 py-2 rounded-full text-sm font-medium ${project.status === \"قيد التنفيذ\" ? \"bg-blue-500/20 text-blue-400\" : project.status === \"في المراجعة\" ? \"bg-yellow-500/20 text-yellow-400\" : \"bg-green-500/20 text-green-400\"}`,\n                                                                        children: project.status\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                        lineNumber: 475,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1 bg-gray-700 rounded-full h-2 mx-4\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full transition-all duration-500\",\n                                                                            style: {\n                                                                                width: `${project.progress}%`\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                            lineNumber: 483,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                        lineNumber: 482,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                lineNumber: 474,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, project.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                            lineNumber: 422,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 15\n                                }, undefined),\n                                activeTab === \"pricing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl font-bold text-white\",\n                                                    children: \"إعدادات الأسعار\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/dashboard/ugc-creator/pricing\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"btn-primary-mobile\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_Bell_Camera_CheckCircle_Clock_DollarSign_Download_Edit_Eye_Filter_MessageCircle_Settings_Target_TrendingUp_Video_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-4 w-4 ml-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                lineNumber: 502,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            \"إدارة الأسعار المتقدمة\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                        lineNumber: 501,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"card-mobile\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-bold text-white mb-4\",\n                                                    children: \"خدماتك الحالية\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between p-4 bg-white/5 rounded-xl\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-2xl ml-3\",\n                                                                            children: \"\\uD83C\\uDFB5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                            lineNumber: 513,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-white font-medium\",\n                                                                                    children: \"فيديو تيك توك احترافي\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                                    lineNumber: 515,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-gray-400 text-sm\",\n                                                                                    children: \"من 200 - 500 ريال\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                                    lineNumber: 516,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                            lineNumber: 514,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                    lineNumber: 512,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-green-400 font-semibold\",\n                                                                    children: \"نشط\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                    lineNumber: 519,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                            lineNumber: 511,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between p-4 bg-white/5 rounded-xl\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-2xl ml-3\",\n                                                                            children: \"\\uD83D\\uDCF1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                            lineNumber: 524,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-white font-medium\",\n                                                                                    children: \"ستوري انستقرام\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                                    lineNumber: 526,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-gray-400 text-sm\",\n                                                                                    children: \"من 150 - 250 ريال\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                                    lineNumber: 527,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                            lineNumber: 525,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                    lineNumber: 523,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-green-400 font-semibold\",\n                                                                    children: \"نشط\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                    lineNumber: 530,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                            lineNumber: 522,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between p-4 bg-white/5 rounded-xl\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-2xl ml-3\",\n                                                                            children: \"\\uD83D\\uDCF8\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                            lineNumber: 535,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-white font-medium\",\n                                                                                    children: \"تصوير منتج احترافي\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                                    lineNumber: 537,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-gray-400 text-sm\",\n                                                                                    children: \"من 200 - 600 ريال\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                                    lineNumber: 538,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                            lineNumber: 536,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                    lineNumber: 534,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-green-400 font-semibold\",\n                                                                    children: \"نشط\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                    lineNumber: 541,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                            lineNumber: 533,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                            lineNumber: 508,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"card-mobile bg-gradient-to-r from-purple-500/20 to-pink-500/20 border-purple-500/30\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-bold text-white mb-4\",\n                                                        children: \"\\uD83D\\uDCA1 نصائح لزيادة الأرباح\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                        lineNumber: 548,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-2xl mb-2\",\n                                                                        children: \"\\uD83D\\uDCC8\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                        lineNumber: 551,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium mb-1\",\n                                                                        children: \"حدث أسعارك\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                        lineNumber: 552,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: \"راجع أسعارك شهرياً وحدثها حسب الطلب\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                        lineNumber: 553,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                lineNumber: 550,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-2xl mb-2\",\n                                                                        children: \"⭐\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                        lineNumber: 556,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium mb-1\",\n                                                                        children: \"حسن جودتك\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                        lineNumber: 557,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: \"الجودة العالية تبرر الأسعار المرتفعة\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                        lineNumber: 558,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                lineNumber: 555,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-2xl mb-2\",\n                                                                        children: \"\\uD83C\\uDFAF\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                        lineNumber: 561,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium mb-1\",\n                                                                        children: \"تخصص أكثر\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                        lineNumber: 562,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: \"التخصص في مجال معين يزيد قيمتك\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                        lineNumber: 563,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                                lineNumber: 560,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                                lineNumber: 547,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                            lineNumber: 546,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 15\n                                }, undefined),\n                                activeTab !== \"overview\" && activeTab !== \"projects\" && activeTab !== \"pricing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"card-mobile text-center py-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-6xl mb-4\",\n                                            children: \"\\uD83D\\uDEA7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                            lineNumber: 574,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-bold text-white mb-4\",\n                                            children: tabs.find((tab)=>tab.id === activeTab)?.label\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                            lineNumber: 575,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400\",\n                                            children: \"هذا القسم قيد التطوير وسيكون متاحاً قريباً\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                            lineNumber: 578,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                                    lineNumber: 573,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n                lineNumber: 210,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\ugc-creator\\\\page.tsx\",\n        lineNumber: 180,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (UGCCreatorDashboard);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/ugc-creator/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/Button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst Button = ({ variant = \"primary\", size = \"md\", isLoading = false, children, className, disabled, ...props })=>{\n    const baseClasses = \"inline-flex items-center justify-center font-semibold rounded-2xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed touch-target relative overflow-hidden gpu-accelerated\";\n    const variantClasses = {\n        primary: \"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white focus:ring-green-500 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\",\n        secondary: \"bg-white hover:bg-gray-50 text-gray-700 hover:text-green-600 border-2 border-gray-200 hover:border-green-300 focus:ring-green-500 shadow-sm hover:shadow-md\",\n        outline: \"border-2 border-green-500 hover:border-green-600 text-green-600 hover:text-white hover:bg-green-500 bg-transparent focus:ring-green-500 shadow-sm hover:shadow-md\",\n        ghost: \"text-gray-700 hover:text-green-600 hover:bg-green-50 focus:ring-green-500\",\n        danger: \"bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white focus:ring-red-500 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\"\n    };\n    const sizeClasses = {\n        sm: \"px-4 py-3 text-sm min-h-[44px]\",\n        md: \"px-6 py-3 text-base min-h-[48px]\",\n        lg: \"px-8 py-4 text-lg min-h-[52px]\",\n        xl: \"px-10 py-5 text-xl min-h-[56px]\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(baseClasses, variantClasses[variant], sizeClasses[size], className),\n        disabled: disabled || isLoading,\n        ...props,\n        children: [\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-white\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                        className: \"opacity-25\",\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"10\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        className: \"opacity-75\",\n                        fill: \"currentColor\",\n                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Button);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/Card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst Card = ({ children, className, hover = false, padding = \"md\", shadow = \"md\" })=>{\n    const baseClasses = \"bg-white rounded-2xl border border-gray-100 transition-all duration-300 gpu-accelerated relative overflow-hidden\";\n    const paddingClasses = {\n        none: \"\",\n        sm: \"p-4\",\n        md: \"p-6\",\n        lg: \"p-8\",\n        xl: \"p-10\"\n    };\n    const shadowClasses = {\n        none: \"\",\n        sm: \"shadow-sm\",\n        md: \"shadow-lg\",\n        lg: \"shadow-xl\",\n        xl: \"shadow-2xl\"\n    };\n    const hoverClasses = hover ? \"hover:shadow-xl hover:-translate-y-2 cursor-pointer will-change-transform\" : \"\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(baseClasses, paddingClasses[padding], shadowClasses[shadow], hoverClasses, className),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Card);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Card.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"ef463d2d032b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW5mbHVlbmNlci1wbGF0Zm9ybS1zYXVkaS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/MzViMyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImVmNDYzZDJkMDMyYlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/ugc-creator/page.tsx":
/*!************************************************!*\
  !*** ./src/app/dashboard/ugc-creator/page.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\amshor2\src\app\dashboard\ugc-creator\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\nconst metadata = {\n    title: \"منصة المؤثرين السعودية - ربط المؤثرين بالتجار\",\n    description: \"منصة احترافية لربط المؤثرين بالتجار في المملكة العربية السعودية مع ضمان الأموال ونظام الدفع الآمن\",\n    keywords: \"مؤثرين, تجار, السعودية, تسويق, إعلانات, سوشيال ميديا\",\n    authors: [\n        {\n            name: \"منصة المؤثرين السعودية\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\",\n    robots: \"index, follow\",\n    openGraph: {\n        title: \"منصة المؤثرين السعودية\",\n        description: \"منصة احترافية لربط المؤثرين بالتجار في المملكة العربية السعودية\",\n        type: \"website\",\n        locale: \"ar_SA\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 font-arabic\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    id: \"root\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fugc-creator%2Fpage&page=%2Fdashboard%2Fugc-creator%2Fpage&appPaths=%2Fdashboard%2Fugc-creator%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fugc-creator%2Fpage.tsx&appDir=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();