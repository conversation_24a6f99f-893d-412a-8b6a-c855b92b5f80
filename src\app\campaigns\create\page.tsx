'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { 
  ArrowRight, 
  ArrowLeft, 
  CheckCircle, 
  Upload, 
  Calendar, 
  DollarSign,
  Users,
  Target,
  Image,
  Video,
  FileText,
  MapPin,
  Clock,
  Star,
  Zap
} from 'lucide-react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import MobileNavigation from '@/components/layout/MobileNavigation'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import Breadcrumb from '@/components/ui/Breadcrumb'
import Container from '@/components/ui/Container'
import Badge from '@/components/ui/Badge'

const CreateCampaignPage: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(1)
  const [campaignData, setCampaignData] = useState({
    // Basic Info
    title: '',
    description: '',
    category: '',
    brand: '',
    
    // Target Audience
    targetAudience: '',
    ageRange: '',
    gender: '',
    location: '',
    interests: [] as string[],
    
    // Campaign Details
    campaignType: '',
    contentType: '',
    platforms: [] as string[],
    duration: '',
    startDate: '',
    endDate: '',
    
    // Budget & Requirements
    budget: '',
    influencerCount: '',
    minFollowers: '',
    requirements: '',
    deliverables: [] as string[],
    
    // Media
    brandAssets: [] as File[],
    referenceContent: [] as File[]
  })

  const categories = [
    { id: 'fashion', name: 'الموضة والأزياء', icon: '👗' },
    { id: 'beauty', name: 'الجمال والعناية', icon: '💄' },
    { id: 'tech', name: 'التقنية والإلكترونيات', icon: '📱' },
    { id: 'food', name: 'الطعام والمشروبات', icon: '🍔' },
    { id: 'travel', name: 'السفر والسياحة', icon: '✈️' },
    { id: 'fitness', name: 'الرياضة واللياقة', icon: '💪' },
    { id: 'lifestyle', name: 'نمط الحياة', icon: '🏠' },
    { id: 'automotive', name: 'السيارات', icon: '🚗' }
  ]

  const platforms = [
    { id: 'instagram', name: 'انستقرام', icon: '📷' },
    { id: 'tiktok', name: 'تيك توك', icon: '🎵' },
    { id: 'snapchat', name: 'سناب شات', icon: '👻' },
    { id: 'youtube', name: 'يوتيوب', icon: '📺' },
    { id: 'twitter', name: 'تويتر', icon: '🐦' },
    { id: 'linkedin', name: 'لينكد إن', icon: '💼' }
  ]

  const contentTypes = [
    { id: 'post', name: 'منشور عادي', icon: '📝' },
    { id: 'story', name: 'ستوري', icon: '📱' },
    { id: 'reel', name: 'ريلز', icon: '🎬' },
    { id: 'video', name: 'فيديو', icon: '📹' },
    { id: 'live', name: 'بث مباشر', icon: '🔴' },
    { id: 'ugc', name: 'محتوى UGC', icon: '🎨' }
  ]

  const deliverables = [
    'منشور في الفيد',
    'ستوري 24 ساعة',
    'ستوري هايلايت',
    'فيديو ريلز',
    'فيديو يوتيوب',
    'بث مباشر',
    'مراجعة مكتوبة',
    'صور المنتج'
  ]

  const handleInputChange = (field: string, value: any) => {
    setCampaignData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleArrayToggle = (field: string, value: string) => {
    setCampaignData(prev => {
      const currentArray = prev[field as keyof typeof prev] as string[]
      const isIncluded = Array.isArray(currentArray) && currentArray.includes(value)
      
      return {
        ...prev,
        [field]: isIncluded
          ? currentArray.filter(item => item !== value)
          : [...(currentArray || []), value]
      }
    })
  }

  const nextStep = () => {
    if (currentStep < 5) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleSubmit = () => {
    console.log('Campaign created:', campaignData)
    alert('تم إنشاء الحملة بنجاح! سيتم مراجعتها والموافقة عليها قريباً.')
  }

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <div className="text-6xl mb-4">🚀</div>
              <h2 className="text-2xl font-bold text-white mb-2">معلومات الحملة الأساسية</h2>
              <p className="text-gray-300">أخبرنا عن حملتك وأهدافها</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="عنوان الحملة"
                placeholder="مثال: إطلاق مجموعة الصيف الجديدة"
                value={campaignData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                required
              />
              
              <Input
                label="اسم العلامة التجارية"
                placeholder="اسم شركتك أو علامتك التجارية"
                value={campaignData.brand}
                onChange={(e) => handleInputChange('brand', e.target.value)}
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                وصف الحملة
              </label>
              <textarea
                className="input min-h-[120px]"
                placeholder="اكتب وصفاً مفصلاً عن حملتك وأهدافها..."
                value={campaignData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
              />
            </div>

            <div>
              <h3 className="text-lg font-semibold text-white mb-4">اختر فئة الحملة:</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {categories.map((category) => (
                  <div
                    key={category.id}
                    onClick={() => handleInputChange('category', category.id)}
                    className={`p-4 rounded-lg border cursor-pointer text-center transition-colors ${
                      campaignData.category === category.id
                        ? 'border-indigo-500 bg-indigo-500/10'
                        : 'border-slate-600 bg-slate-800/50 hover:border-slate-500'
                    }`}
                  >
                    <div className="text-2xl mb-2">{category.icon}</div>
                    <div className="text-white text-sm">{category.name}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )

      case 2:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <div className="text-6xl mb-4">🎯</div>
              <h2 className="text-2xl font-bold text-white mb-2">الجمهور المستهدف</h2>
              <p className="text-gray-300">حدد الجمهور الذي تريد الوصول إليه</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  الفئة العمرية
                </label>
                <select
                  value={campaignData.ageRange}
                  onChange={(e) => handleInputChange('ageRange', e.target.value)}
                  className="input"
                >
                  <option value="">اختر الفئة العمرية</option>
                  <option value="13-17">13-17 سنة</option>
                  <option value="18-24">18-24 سنة</option>
                  <option value="25-34">25-34 سنة</option>
                  <option value="35-44">35-44 سنة</option>
                  <option value="45+">45+ سنة</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  الجنس
                </label>
                <select
                  value={campaignData.gender}
                  onChange={(e) => handleInputChange('gender', e.target.value)}
                  className="input"
                >
                  <option value="">اختر الجنس</option>
                  <option value="all">الجميع</option>
                  <option value="male">ذكور</option>
                  <option value="female">إناث</option>
                </select>
              </div>

              <Input
                label="الموقع الجغرافي"
                placeholder="مثال: الرياض، جدة، الدمام..."
                value={campaignData.location}
                onChange={(e) => handleInputChange('location', e.target.value)}
              />

              <Input
                label="وصف الجمهور المستهدف"
                placeholder="مثال: الشباب المهتمين بالتقنية..."
                value={campaignData.targetAudience}
                onChange={(e) => handleInputChange('targetAudience', e.target.value)}
              />
            </div>
          </div>
        )

      case 3:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <div className="text-6xl mb-4">📱</div>
              <h2 className="text-2xl font-bold text-white mb-2">تفاصيل الحملة</h2>
              <p className="text-gray-300">حدد نوع المحتوى والمنصات</p>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-white mb-4">اختر المنصات:</h3>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {platforms.map((platform) => (
                  <div
                    key={platform.id}
                    onClick={() => handleArrayToggle('platforms', platform.id)}
                    className={`p-4 rounded-lg border cursor-pointer text-center transition-colors ${
                      campaignData.platforms.includes(platform.id)
                        ? 'border-emerald-500 bg-emerald-500/10'
                        : 'border-slate-600 bg-slate-800/50 hover:border-slate-500'
                    }`}
                  >
                    <div className="text-2xl mb-2">{platform.icon}</div>
                    <div className="text-white text-sm">{platform.name}</div>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-white mb-4">نوع المحتوى:</h3>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {contentTypes.map((type) => (
                  <div
                    key={type.id}
                    onClick={() => handleInputChange('contentType', type.id)}
                    className={`p-4 rounded-lg border cursor-pointer text-center transition-colors ${
                      campaignData.contentType === type.id
                        ? 'border-purple-500 bg-purple-500/10'
                        : 'border-slate-600 bg-slate-800/50 hover:border-slate-500'
                    }`}
                  >
                    <div className="text-2xl mb-2">{type.icon}</div>
                    <div className="text-white text-sm">{type.name}</div>
                  </div>
                ))}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="تاريخ البداية"
                type="date"
                value={campaignData.startDate}
                onChange={(e) => handleInputChange('startDate', e.target.value)}
              />

              <Input
                label="تاريخ النهاية"
                type="date"
                value={campaignData.endDate}
                onChange={(e) => handleInputChange('endDate', e.target.value)}
              />
            </div>
          </div>
        )

      case 4:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <div className="text-6xl mb-4">💰</div>
              <h2 className="text-2xl font-bold text-white mb-2">الميزانية والمتطلبات</h2>
              <p className="text-gray-300">حدد ميزانيتك ومتطلبات المؤثرين</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Input
                label="الميزانية الإجمالية (ريال)"
                type="number"
                placeholder="10000"
                value={campaignData.budget}
                onChange={(e) => handleInputChange('budget', e.target.value)}
              />

              <Input
                label="عدد المؤثرين المطلوب"
                type="number"
                placeholder="5"
                value={campaignData.influencerCount}
                onChange={(e) => handleInputChange('influencerCount', e.target.value)}
              />

              <Input
                label="الحد الأدنى للمتابعين"
                type="number"
                placeholder="10000"
                value={campaignData.minFollowers}
                onChange={(e) => handleInputChange('minFollowers', e.target.value)}
              />
            </div>

            <div>
              <h3 className="text-lg font-semibold text-white mb-4">المطلوب تسليمه:</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {deliverables.map((deliverable) => (
                  <div
                    key={deliverable}
                    onClick={() => handleArrayToggle('deliverables', deliverable)}
                    className={`p-3 rounded-lg border cursor-pointer text-center transition-colors ${
                      campaignData.deliverables.includes(deliverable)
                        ? 'border-amber-500 bg-amber-500/10'
                        : 'border-slate-600 bg-slate-800/50 hover:border-slate-500'
                    }`}
                  >
                    <div className="text-white text-sm">{deliverable}</div>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                متطلبات إضافية
              </label>
              <textarea
                className="input min-h-[100px]"
                placeholder="أي متطلبات أو تعليمات خاصة للمؤثرين..."
                value={campaignData.requirements}
                onChange={(e) => handleInputChange('requirements', e.target.value)}
              />
            </div>
          </div>
        )

      case 5:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <div className="text-6xl mb-4">✨</div>
              <h2 className="text-2xl font-bold text-white mb-2">المراجعة النهائية</h2>
              <p className="text-gray-300">راجع تفاصيل حملتك قبل الإرسال</p>
            </div>

            <Card className="bg-emerald-500/10 border-emerald-500/20">
              <h3 className="text-lg font-bold text-white mb-4">ملخص الحملة</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-slate-400">عنوان الحملة:</span>
                  <span className="text-white">{campaignData.title || 'غير محدد'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-400">العلامة التجارية:</span>
                  <span className="text-white">{campaignData.brand || 'غير محدد'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-400">الفئة:</span>
                  <span className="text-white">
                    {categories.find(c => c.id === campaignData.category)?.name || 'غير محدد'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-400">المنصات:</span>
                  <span className="text-white">
                    {campaignData.platforms.length > 0 ? campaignData.platforms.length + ' منصة' : 'غير محدد'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-400">الميزانية:</span>
                  <span className="text-white">{campaignData.budget ? campaignData.budget + ' ريال' : 'غير محدد'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-400">عدد المؤثرين:</span>
                  <span className="text-white">{campaignData.influencerCount || 'غير محدد'}</span>
                </div>
              </div>
            </Card>

            <Card className="bg-blue-500/10 border-blue-500/20">
              <div className="text-center">
                <div className="flex items-center justify-center gap-4 mb-4">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-green-400" />
                    <span className="text-white text-sm">🇸🇦 منصة سعودية موثوقة</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Star className="w-4 h-4 text-yellow-400" />
                    <span className="text-white text-sm">⭐ ضمان الجودة</span>
                  </div>
                </div>
                <div className="text-white font-semibold mb-2">🎉 حملتك جاهزة للإطلاق!</div>
                <div className="text-gray-300 text-sm">
                  سيتم مراجعة حملتك والموافقة عليها خلال 24 ساعة
                </div>
              </div>
            </Card>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className="min-h-screen bg-slate-900">
      <Header />
      
      <div className="py-12">
        <Container size="lg">
          {/* Breadcrumb */}
          <div className="mb-6">
            <Breadcrumb items={[
              { label: 'الرئيسية', href: '/' },
              { label: 'لوحة التحكم', href: '/dashboard' },
              { label: 'إنشاء حملة جديدة' }
            ]} />
          </div>

          {/* Progress Bar */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h1 className="text-2xl font-bold text-white">إنشاء حملة جديدة</h1>
              <div className="text-gray-400">الخطوة {currentStep} من 5</div>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div 
                className="bg-gradient-to-r from-indigo-500 to-purple-500 h-2 rounded-full transition-all duration-500"
                style={{ width: `${(currentStep / 5) * 100}%` }}
              ></div>
            </div>
          </div>

          {/* Form Content */}
          <Card>
            {renderStep()}
          </Card>

          {/* Navigation Buttons */}
          <div className="flex justify-between mt-8">
            <Button
              variant="secondary"
              onClick={prevStep}
              disabled={currentStep === 1}
            >
              <ArrowRight className="h-5 w-5 ml-2" />
              السابق
            </Button>

            {currentStep === 5 ? (
              <Button
                variant="primary"
                onClick={handleSubmit}
              >
                🚀 إطلاق الحملة
                <CheckCircle className="h-5 w-5 mr-2" />
              </Button>
            ) : (
              <Button
                variant="primary"
                onClick={nextStep}
              >
                التالي
                <ArrowLeft className="h-5 w-5 mr-2" />
              </Button>
            )}
          </div>
        </Container>
      </div>

      <Footer />
      <MobileNavigation />
    </div>
  )
}

export default CreateCampaignPage
