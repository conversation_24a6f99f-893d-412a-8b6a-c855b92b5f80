'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import {
  <PERSON>Right,
  ArrowLeft,
  Upload,
  Video,
  Camera,
  Edit3,
  Clock,
  DollarSign,
  Star,
  CheckCircle,
  Shield,
  Palette,
  Film,
  Zap,
  Target,
  Heart
} from 'lucide-react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import MobileNavigation from '@/components/layout/MobileNavigation'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'

const UGCCreatorRegister: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(1)
  const [formData, setFormData] = useState({
    // الخطوة 1: المعلومات الشخصية
    fullName: '',
    email: '',
    phone: '',
    city: '',
    age: '',

    // الخطوة 2: أنواع المحتوى والتخصص
    contentTypes: [] as string[],
    categories: [] as string[],
    customService: '',

    // الخطوة 3: الأسعار والمدة
    pricing: {} as Record<string, number>,
    videoDurations: [] as string[],
    editingIncluded: false,

    // الخطوة 4: الأعمال السابقة
    portfolioLinks: [] as string[],
    portfolioFiles: [] as File[],
    deliveryTime: '',

    // الخطوة 5: معلومات إضافية
    experience: '',
    equipment: '',
    languages: [] as string[],
    availability: ''
  })

  const contentTypeOptions = [
    { id: 'tiktok', label: 'فيديو تيك توك', icon: '🎵', color: 'from-pink-500 to-red-500' },
    { id: 'product-photo', label: 'تصوير منتج', icon: '📸', color: 'from-blue-500 to-cyan-500' },
    { id: 'reviews', label: 'مراجعات', icon: '⭐', color: 'from-yellow-500 to-orange-500' },
    { id: 'snapchat', label: 'فيديو سناب شات', icon: '👻', color: 'from-yellow-400 to-yellow-600' },
    { id: 'instagram-stories', label: 'ستوريز انستقرام', icon: '📱', color: 'from-purple-500 to-pink-500' },
    { id: 'video-editing', label: 'مونتاج فيديو', icon: '✂️', color: 'from-green-500 to-teal-500' },
    { id: 'graphic-design', label: 'تصميم صورة', icon: '🎨', color: 'from-indigo-500 to-purple-500' },
    { id: 'food-photography', label: 'تصوير أكل', icon: '🍽️', color: 'from-orange-500 to-red-500' },
    { id: 'brand-design', label: 'تصاميم براند', icon: '🏷️', color: 'from-teal-500 to-blue-500' },
    { id: 'custom', label: 'خدمة مخصصة', icon: '⚡', color: 'from-gray-500 to-gray-700' }
  ]

  const categoryOptions = [
    { id: 'beauty', label: 'جمال', icon: '💄' },
    { id: 'cars', label: 'سيارات', icon: '🚗' },
    { id: 'restaurants', label: 'مطاعم', icon: '🍕' },
    { id: 'food', label: 'أكل', icon: '🍔' },
    { id: 'fashion', label: 'موضة', icon: '👗' },
    { id: 'tech', label: 'تقنية', icon: '📱' },
    { id: 'travel', label: 'سفر', icon: '✈️' },
    { id: 'fitness', label: 'رياضة', icon: '💪' },
    { id: 'home', label: 'منزل', icon: '🏠' },
    { id: 'kids', label: 'أطفال', icon: '🧸' }
  ]

  const videoDurationOptions = [
    { id: '10s', label: '10 ثواني', price: 100 },
    { id: '30s', label: '30 ثانية', price: 200 },
    { id: '60s', label: 'دقيقة واحدة', price: 350 },
    { id: '120s', label: 'دقيقتان', price: 500 }
  ]

  const deliveryTimeOptions = [
    { id: '24h', label: '24 ساعة', icon: '⚡' },
    { id: '48h', label: '48 ساعة', icon: '🚀' },
    { id: '3d', label: '3 أيام', icon: '📅' },
    { id: '7d', label: 'أسبوع', icon: '📆' },
    { id: '14d', label: 'أسبوعين', icon: '🗓️' }
  ]

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleArrayToggle = (field: string, value: string) => {
    setFormData(prev => {
      const currentArray = prev[field as keyof typeof prev] as string[]
      const isIncluded = Array.isArray(currentArray) && currentArray.includes(value)

      return {
        ...prev,
        [field]: isIncluded
          ? currentArray.filter(item => item !== value)
          : [...(currentArray || []), value]
      }
    })
  }

  const nextStep = () => {
    if (currentStep < 5) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleSubmit = () => {
    console.log('Form submitted:', formData)
    alert('تم إرسال طلب التسجيل بنجاح! سنتواصل معك قريباً.')
  }

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <div className="text-6xl mb-4">🎬</div>
              <h2 className="text-2xl font-bold text-white mb-2">المعلومات الشخصية</h2>
              <p className="text-gray-300">أخبرنا عن نفسك لنبدأ رحلة الإبداع معاً</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="الاسم الكامل"
                placeholder="اسمك الكامل"
                value={formData.fullName}
                onChange={(e) => handleInputChange('fullName', e.target.value)}
                required
              />

              <Input
                label="البريد الإلكتروني"
                type="email"
                placeholder="<EMAIL>"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                required
              />

              <Input
                label="رقم الجوال"
                placeholder="05xxxxxxxx"
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                required
              />

              <Input
                label="المدينة"
                placeholder="الرياض، جدة، الدمام..."
                value={formData.city}
                onChange={(e) => handleInputChange('city', e.target.value)}
                required
              />

              <Input
                label="العمر"
                type="number"
                placeholder="25"
                value={formData.age}
                onChange={(e) => handleInputChange('age', e.target.value)}
                required
              />
            </div>
          </div>
        )

      case 2:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <div className="text-6xl mb-4">🎨</div>
              <h2 className="text-2xl font-bold text-white mb-2">تخصصك وخدماتك</h2>
              <p className="text-gray-300">اختر أنواع المحتوى التي تبدع فيها</p>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-white mb-4">أنواع المحتوى المتخصص فيها:</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {contentTypeOptions.map((option) => (
                  <div
                    key={option.id}
                    onClick={() => handleArrayToggle('contentTypes', option.id)}
                    className={`p-4 rounded-lg border cursor-pointer transition-colors ${
                      formData.contentTypes.includes(option.id)
                        ? 'border-indigo-500 bg-indigo-500/10'
                        : 'border-slate-600 bg-slate-800/50 hover:border-slate-500'
                    }`}
                  >
                    <div className="text-center">
                      <div className="text-3xl mb-2">{option.icon}</div>
                      <div className="text-white font-medium">{option.label}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {formData.contentTypes.includes('custom') && (
              <div>
                <Input
                  label="اكتب نوع الخدمة المخصصة"
                  placeholder="مثال: تصوير فيديوهات تعليمية، إنتاج محتوى تسويقي..."
                  value={formData.customService}
                  onChange={(e) => handleInputChange('customService', e.target.value)}
                />
              </div>
            )}

            <div>
              <h3 className="text-lg font-semibold text-white mb-4">الفئات المتخصص فيها:</h3>
              <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
                {categoryOptions.map((category) => (
                  <div
                    key={category.id}
                    onClick={() => handleArrayToggle('categories', category.id)}
                    className={`p-3 rounded-lg border cursor-pointer text-center transition-colors ${
                      formData.categories.includes(category.id)
                        ? 'border-emerald-500 bg-emerald-500/10'
                        : 'border-slate-600 bg-slate-800/50 hover:border-slate-500'
                    }`}
                  >
                    <div className="text-2xl mb-1">{category.icon}</div>
                    <div className="text-white text-sm">{category.label}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )

      case 3:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <div className="text-6xl mb-4">💰</div>
              <h2 className="text-2xl font-bold text-white mb-2">الأسعار والمدة</h2>
              <p className="text-gray-300">حدد أسعارك ومدة الفيديوهات المتاحة</p>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-white mb-4">مدة الفيديوهات المتاحة مع الأسعار:</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {videoDurationOptions.map((duration) => (
                  <Card key={duration.id}>
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <div className="text-white font-semibold">{duration.label}</div>
                        <div className="text-gray-400 text-sm">السعر المقترح: {duration.price} ريال</div>
                      </div>
                      <input
                        type="checkbox"
                        checked={formData.videoDurations.includes(duration.id)}
                        onChange={() => handleArrayToggle('videoDurations', duration.id)}
                        className="w-5 h-5 text-purple-600 rounded focus:ring-purple-500"
                      />
                    </div>
                    {formData.videoDurations.includes(duration.id) && (
                      <Input
                        label="سعرك (ريال)"
                        type="number"
                        placeholder={duration.price.toString()}
                        value={formData.pricing[duration.id] || ''}
                        onChange={(e) => handleInputChange('pricing', {
                          ...formData.pricing,
                          [duration.id]: parseInt(e.target.value)
                        })}
                      />
                    )}
                  </Card>
                ))}
              </div>
            </div>

            <Card>
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-white font-semibold mb-2">هل تشمل خدماتك المونتاج والتعديل؟</div>
                  <div className="text-gray-400 text-sm">يمكن إضافة رسوم إضافية للمونتاج المتقدم</div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.editingIncluded}
                    onChange={(e) => handleInputChange('editingIncluded', e.target.checked)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                </label>
              </div>
            </Card>
          </div>
        )

      case 4:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <div className="text-6xl mb-4">🎯</div>
              <h2 className="text-2xl font-bold text-white mb-2">معرض أعمالك</h2>
              <p className="text-gray-300">أضف أمثلة من أعمالك السابقة لتعرض مهاراتك</p>
            </div>

            <Card>
              <h3 className="text-lg font-semibold text-white mb-4">روابط أعمالك (يوتيوب، انستقرام، تيك توك):</h3>
              <div className="space-y-3">
                {[0, 1, 2].map((index) => (
                  <Input
                    key={index}
                    placeholder={`رابط العمل ${index + 1} (اختياري)`}
                    value={formData.portfolioLinks[index] || ''}
                    onChange={(e) => {
                      const newLinks = [...formData.portfolioLinks]
                      newLinks[index] = e.target.value
                      handleInputChange('portfolioLinks', newLinks)
                    }}
                  />
                ))}
              </div>
            </Card>

            <Card>
              <h3 className="text-lg font-semibold text-white mb-4">رفع ملفات فيديو (حد أقصى 3 ملفات):</h3>
              <div className="border-2 border-dashed border-purple-500/50 rounded-2xl p-8 text-center hover:border-purple-500 transition-colors">
                <Upload className="h-12 w-12 text-purple-400 mx-auto mb-4" />
                <div className="text-white mb-2">اسحب الملفات هنا أو اضغط للاختيار</div>
                <div className="text-gray-400 text-sm">MP4, MOV, AVI (حد أقصى 50MB لكل ملف)</div>
                <input
                  type="file"
                  multiple
                  accept="video/*"
                  className="hidden"
                  onChange={(e) => {
                    if (e.target.files) {
                      handleInputChange('portfolioFiles', Array.from(e.target.files).slice(0, 3))
                    }
                  }}
                />
              </div>
            </Card>

            <div>
              <h3 className="text-lg font-semibold text-white mb-4">وقت التسليم المعتاد:</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {deliveryTimeOptions.map((option) => (
                  <div
                    key={option.id}
                    onClick={() => handleInputChange('deliveryTime', option.id)}
                    className={`p-4 rounded-lg border cursor-pointer text-center transition-colors ${
                      formData.deliveryTime === option.id
                        ? 'border-amber-500 bg-amber-500/10'
                        : 'border-slate-600 bg-slate-800/50 hover:border-slate-500'
                    }`}
                  >
                    <div className="text-3xl mb-2">{option.icon}</div>
                    <div className="text-white font-medium">{option.label}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )

      case 5:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <div className="text-6xl mb-4">✨</div>
              <h2 className="text-2xl font-bold text-white mb-2">اللمسة الأخيرة</h2>
              <p className="text-gray-300">معلومات إضافية لإكمال ملفك الشخصي</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  سنوات الخبرة
                </label>
                <select
                  value={formData.experience}
                  onChange={(e) => handleInputChange('experience', e.target.value)}
                  className="input"
                >
                  <option value="">اختر سنوات الخبرة</option>
                  <option value="beginner">مبتدئ (أقل من سنة)</option>
                  <option value="1-2">1-2 سنة</option>
                  <option value="3-5">3-5 سنوات</option>
                  <option value="5+">أكثر من 5 سنوات</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  المعدات المتاحة
                </label>
                <select
                  value={formData.equipment}
                  onChange={(e) => handleInputChange('equipment', e.target.value)}
                  className="input"
                >
                  <option value="">اختر المعدات</option>
                  <option value="phone">جوال احترافي</option>
                  <option value="camera">كاميرا DSLR</option>
                  <option value="professional">معدات احترافية كاملة</option>
                  <option value="studio">استوديو مجهز</option>
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                اللغات المتاحة للمحتوى
              </label>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {['العربية', 'الإنجليزية', 'الفرنسية', 'الأخرى'].map((lang) => (
                  <div
                    key={lang}
                    onClick={() => handleArrayToggle('languages', lang)}
                    className={`p-3 rounded-lg border cursor-pointer text-center transition-colors ${
                      formData.languages.includes(lang)
                        ? 'border-emerald-500 bg-emerald-500/10'
                        : 'border-slate-600 bg-slate-800/50 hover:border-slate-500'
                    }`}
                  >
                    <div className="text-white text-sm">{lang}</div>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                مدى التفرغ
              </label>
              <select
                value={formData.availability}
                onChange={(e) => handleInputChange('availability', e.target.value)}
                className="input"
              >
                <option value="">اختر مدى التفرغ</option>
                <option value="full-time">متفرغ بالكامل</option>
                <option value="part-time">دوام جزئي</option>
                <option value="weekends">نهايات الأسبوع فقط</option>
                <option value="flexible">مرن حسب المشروع</option>
              </select>
            </div>

            {/* شارات الثقة */}
            <Card className="bg-emerald-500/10 border-emerald-500/20">
              <div className="text-center">
                <div className="flex items-center justify-center gap-4 mb-4">
                  <div className="trust-indicator">
                    <Shield className="w-4 h-4" />
                    🇸🇦 منصة سعودية موثوقة
                  </div>
                  <div className="trust-indicator">
                    <Star className="w-4 h-4" />
                    ⭐ ضمان الجودة
                  </div>
                </div>
                <div className="text-white font-semibold mb-2">🎉 مرحباً بك في عائلة المبدعين!</div>
                <div className="text-gray-300 text-sm">
                  ستحصل على فرص عمل حصرية وأدوات احترافية لتطوير مهاراتك
                </div>
              </div>
            </Card>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className="min-h-screen bg-slate-900">
      <Header />

      <div className="py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Progress Bar */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h1 className="text-2xl font-bold text-white">انضم كمبدع UGC</h1>
              <div className="text-gray-400">الخطوة {currentStep} من 5</div>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div
                className="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full transition-all duration-500"
                style={{ width: `${(currentStep / 5) * 100}%` }}
              ></div>
            </div>
          </div>

          {/* Form Content */}
          <Card>
            {renderStep()}
          </Card>

          {/* Navigation Buttons */}
          <div className="flex justify-between mt-8">
            <Button
              variant="secondary"
              onClick={prevStep}
              disabled={currentStep === 1}
            >
              <ArrowRight className="h-5 w-5 ml-2" />
              السابق
            </Button>

            {currentStep === 5 ? (
              <Button
                variant="primary"
                onClick={handleSubmit}
              >
                🚀 إرسال الطلب
                <CheckCircle className="h-5 w-5 mr-2" />
              </Button>
            ) : (
              <Button
                variant="primary"
                onClick={nextStep}
              >
                التالي
                <ArrowLeft className="h-5 w-5 mr-2" />
              </Button>
            )}
          </div>
        </div>
      </div>

      <Footer />
      <MobileNavigation />
    </div>
  )
}

export default UGCCreatorRegister
