'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { 
  BarChart3, 
  Users, 
  DollarSign,
  TrendingUp,
  Calendar,
  MessageCircle,
  Star,
  Eye,
  Settings,
  Bell,
  Plus,
  Filter,
  Download,
  Edit,
  CheckCircle,
  Clock,
  AlertCircle,
  Target,
  Zap
} from 'lucide-react'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'

const MerchantDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview')

  // Mock data
  const stats = {
    totalSpent: 25400,
    activeCampaigns: 5,
    completedCampaigns: 18,
    averageROI: 320,
    totalReach: 450000,
    totalEngagement: 28500
  }

  const activeCampaigns = [
    {
      id: 1,
      influencer: 'سارة أحمد',
      title: 'حملة الأزياء الشتوية',
      type: 'منشور + ستوري',
      budget: 1200,
      status: 'قيد التنفيذ',
      progress: 75,
      deadline: '2024-01-25',
      reach: 45000,
      engagement: 3200
    },
    {
      id: 2,
      influencer: 'محمد العلي',
      title: 'مراجعة المنتج الجديد',
      type: 'فيديو يوتيوب',
      budget: 2000,
      status: 'في المراجعة',
      progress: 90,
      deadline: '2024-01-22',
      reach: 78000,
      engagement: 5400
    },
    {
      id: 3,
      influencer: 'نورا السعد',
      title: 'زيارة المطعم',
      type: 'زيارة + ستوري',
      budget: 800,
      status: 'مكتملة',
      progress: 100,
      deadline: '2024-01-20',
      reach: 32000,
      engagement: 2100
    }
  ]

  const recentInfluencers = [
    {
      id: 1,
      name: 'سارة أحمد',
      category: 'الموضة والجمال',
      followers: 125000,
      engagement: 8.5,
      rating: 4.9,
      price: 300,
      avatar: 'س'
    },
    {
      id: 2,
      name: 'محمد العلي',
      category: 'التقنية',
      followers: 89000,
      engagement: 9.2,
      rating: 4.8,
      price: 250,
      avatar: 'م'
    },
    {
      id: 3,
      name: 'نورا السعد',
      category: 'الطعام',
      followers: 67000,
      engagement: 7.8,
      rating: 4.7,
      price: 200,
      avatar: 'ن'
    }
  ]

  const campaignPerformance = [
    { month: 'ديسمبر', spent: 4200, reach: 120000, engagement: 8500 },
    { month: 'نوفمبر', spent: 3800, reach: 95000, engagement: 6800 },
    { month: 'أكتوبر', spent: 5100, reach: 145000, engagement: 9200 },
    { month: 'سبتمبر', spent: 3600, reach: 88000, engagement: 5900 }
  ]

  const tabs = [
    { id: 'overview', label: 'نظرة عامة', icon: BarChart3 },
    { id: 'campaigns', label: 'الحملات', icon: Calendar },
    { id: 'influencers', label: 'المؤثرين', icon: Users },
    { id: 'analytics', label: 'التحليلات', icon: TrendingUp },
    { id: 'messages', label: 'الرسائل', icon: MessageCircle },
    { id: 'settings', label: 'الإعدادات', icon: Settings }
  ]

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K'
    }
    return num.toString()
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="flex items-center">
                <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">م</span>
                </div>
                <span className="mr-3 text-lg font-bold text-gray-900">منصة المؤثرين</span>
              </Link>
            </div>
            
            <div className="flex items-center space-x-4 space-x-reverse">
              <Link href="/influencers">
                <Button size="sm">
                  <Plus className="h-4 w-4 ml-2" />
                  حملة جديدة
                </Button>
              </Link>
              <button className="relative p-2 text-gray-400 hover:text-gray-600">
                <Bell className="h-6 w-6" />
                <span className="absolute top-0 right-0 h-2 w-2 bg-red-500 rounded-full"></span>
              </button>
              <div className="flex items-center">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-400 to-cyan-400 rounded-full flex items-center justify-center text-white font-bold">
                  أ
                </div>
                <span className="mr-3 text-sm font-medium text-gray-700">أحمد محمد</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar */}
          <div className="lg:w-64 flex-shrink-0">
            <Card className="p-4">
              <nav className="space-y-2">
                {tabs.map((tab) => {
                  const Icon = tab.icon
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full flex items-center px-3 py-2 rounded-lg text-right transition-colors ${
                        activeTab === tab.id
                          ? 'bg-primary-100 text-primary-700'
                          : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      <Icon className="h-5 w-5 ml-3" />
                      <span>{tab.label}</span>
                    </button>
                  )
                })}
              </nav>
            </Card>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            {activeTab === 'overview' && (
              <div className="space-y-8">
                {/* Welcome */}
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 mb-2">
                    مرحباً، أحمد! 👋
                  </h1>
                  <p className="text-gray-600">
                    إليك نظرة عامة على حملاتك وأداء التسويق
                  </p>
                </div>

                {/* Stats Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <Card>
                    <div className="flex items-center">
                      <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <DollarSign className="h-6 w-6 text-blue-600" />
                      </div>
                      <div className="mr-4">
                        <p className="text-sm text-gray-500">إجمالي الإنفاق</p>
                        <p className="text-2xl font-bold text-gray-900">{stats.totalSpent.toLocaleString()} ريال</p>
                      </div>
                    </div>
                  </Card>

                  <Card>
                    <div className="flex items-center">
                      <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <Calendar className="h-6 w-6 text-green-600" />
                      </div>
                      <div className="mr-4">
                        <p className="text-sm text-gray-500">الحملات النشطة</p>
                        <p className="text-2xl font-bold text-gray-900">{stats.activeCampaigns}</p>
                      </div>
                    </div>
                  </Card>

                  <Card>
                    <div className="flex items-center">
                      <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <TrendingUp className="h-6 w-6 text-purple-600" />
                      </div>
                      <div className="mr-4">
                        <p className="text-sm text-gray-500">متوسط العائد</p>
                        <p className="text-2xl font-bold text-gray-900">{stats.averageROI}%</p>
                      </div>
                    </div>
                  </Card>

                  <Card>
                    <div className="flex items-center">
                      <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <Eye className="h-6 w-6 text-yellow-600" />
                      </div>
                      <div className="mr-4">
                        <p className="text-sm text-gray-500">إجمالي الوصول</p>
                        <p className="text-2xl font-bold text-gray-900">{formatNumber(stats.totalReach)}</p>
                      </div>
                    </div>
                  </Card>

                  <Card>
                    <div className="flex items-center">
                      <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                        <Zap className="h-6 w-6 text-red-600" />
                      </div>
                      <div className="mr-4">
                        <p className="text-sm text-gray-500">إجمالي التفاعل</p>
                        <p className="text-2xl font-bold text-gray-900">{formatNumber(stats.totalEngagement)}</p>
                      </div>
                    </div>
                  </Card>

                  <Card>
                    <div className="flex items-center">
                      <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
                        <CheckCircle className="h-6 w-6 text-indigo-600" />
                      </div>
                      <div className="mr-4">
                        <p className="text-sm text-gray-500">الحملات المكتملة</p>
                        <p className="text-2xl font-bold text-gray-900">{stats.completedCampaigns}</p>
                      </div>
                    </div>
                  </Card>
                </div>

                {/* Active Campaigns */}
                <Card>
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-lg font-bold text-gray-900">الحملات النشطة</h2>
                    <Link href="/dashboard/merchant?tab=campaigns">
                      <Button variant="outline" size="sm">
                        عرض الكل
                      </Button>
                    </Link>
                  </div>
                  
                  <div className="space-y-4">
                    {activeCampaigns.slice(0, 3).map((campaign) => (
                      <div key={campaign.id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <div>
                            <h3 className="font-semibold text-gray-900">{campaign.title}</h3>
                            <p className="text-sm text-gray-500">{campaign.influencer} • {campaign.type}</p>
                          </div>
                          <div className="text-right">
                            <p className="text-lg font-bold text-gray-900">{campaign.budget} ريال</p>
                            <p className="text-sm text-gray-500">{campaign.deadline}</p>
                          </div>
                        </div>
                        
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex-1 bg-gray-200 rounded-full h-2 ml-4">
                            <div 
                              className="bg-primary-600 h-2 rounded-full" 
                              style={{ width: `${campaign.progress}%` }}
                            ></div>
                          </div>
                          <span className="text-sm font-medium text-gray-700">{campaign.progress}%</span>
                        </div>
                        
                        <div className="flex items-center justify-between text-sm text-gray-600">
                          <span>الوصول: {formatNumber(campaign.reach)}</span>
                          <span>التفاعل: {formatNumber(campaign.engagement)}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </Card>

                {/* Recommended Influencers */}
                <Card>
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-lg font-bold text-gray-900">مؤثرين مقترحين</h2>
                    <Link href="/influencers">
                      <Button variant="outline" size="sm">
                        تصفح المزيد
                      </Button>
                    </Link>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {recentInfluencers.map((influencer) => (
                      <div key={influencer.id} className="border border-gray-200 rounded-lg p-4 text-center">
                        <div className="w-16 h-16 bg-gradient-to-br from-purple-400 to-pink-400 rounded-full flex items-center justify-center text-white font-bold text-xl mx-auto mb-3">
                          {influencer.avatar}
                        </div>
                        <h3 className="font-semibold text-gray-900 mb-1">{influencer.name}</h3>
                        <p className="text-sm text-gray-500 mb-2">{influencer.category}</p>
                        <div className="flex items-center justify-center mb-2">
                          <Star className="h-4 w-4 text-yellow-400 fill-current ml-1" />
                          <span className="text-sm font-medium">{influencer.rating}</span>
                        </div>
                        <p className="text-sm text-gray-600 mb-3">
                          {formatNumber(influencer.followers)} متابع • {influencer.engagement}% تفاعل
                        </p>
                        <p className="text-lg font-bold text-primary-600 mb-3">
                          من {influencer.price} ريال
                        </p>
                        <Button size="sm" className="w-full">
                          تواصل
                        </Button>
                      </div>
                    ))}
                  </div>
                </Card>
              </div>
            )}

            {activeTab === 'campaigns' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h1 className="text-2xl font-bold text-gray-900">إدارة الحملات</h1>
                  <div className="flex gap-3">
                    <Button variant="outline" size="sm">
                      <Filter className="h-4 w-4 ml-2" />
                      فلترة
                    </Button>
                    <Button variant="outline" size="sm">
                      <Download className="h-4 w-4 ml-2" />
                      تصدير
                    </Button>
                    <Link href="/influencers">
                      <Button size="sm">
                        <Plus className="h-4 w-4 ml-2" />
                        حملة جديدة
                      </Button>
                    </Link>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                  <Card className="text-center">
                    <Clock className="h-8 w-8 text-yellow-500 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-gray-900">5</div>
                    <div className="text-sm text-gray-500">قيد التنفيذ</div>
                  </Card>
                  <Card className="text-center">
                    <CheckCircle className="h-8 w-8 text-green-500 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-gray-900">18</div>
                    <div className="text-sm text-gray-500">مكتملة</div>
                  </Card>
                  <Card className="text-center">
                    <AlertCircle className="h-8 w-8 text-blue-500 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-gray-900">3</div>
                    <div className="text-sm text-gray-500">في المراجعة</div>
                  </Card>
                  <Card className="text-center">
                    <Target className="h-8 w-8 text-purple-500 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-gray-900">26</div>
                    <div className="text-sm text-gray-500">إجمالي الحملات</div>
                  </Card>
                </div>

                <Card>
                  <div className="space-y-4">
                    {activeCampaigns.map((campaign) => (
                      <div key={campaign.id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <div>
                            <h3 className="font-semibold text-gray-900">{campaign.title}</h3>
                            <p className="text-sm text-gray-500">{campaign.influencer} • {campaign.type}</p>
                          </div>
                          <div className="flex items-center gap-2">
                            <Button variant="outline" size="sm">
                              <Eye className="h-4 w-4 ml-2" />
                              عرض
                            </Button>
                            <Button variant="outline" size="sm">
                              <Edit className="h-4 w-4 ml-2" />
                              تحرير
                            </Button>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <span className="text-gray-500">الميزانية:</span>
                            <span className="font-medium mr-2">{campaign.budget} ريال</span>
                          </div>
                          <div>
                            <span className="text-gray-500">الوصول:</span>
                            <span className="font-medium mr-2">{formatNumber(campaign.reach)}</span>
                          </div>
                          <div>
                            <span className="text-gray-500">التفاعل:</span>
                            <span className="font-medium mr-2">{formatNumber(campaign.engagement)}</span>
                          </div>
                          <div>
                            <span className="text-gray-500">التقدم:</span>
                            <span className="font-medium mr-2">{campaign.progress}%</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </Card>
              </div>
            )}

            {/* Add other tab contents here */}
            {activeTab !== 'overview' && activeTab !== 'campaigns' && (
              <Card className="text-center py-12">
                <h2 className="text-xl font-bold text-gray-900 mb-4">
                  {tabs.find(tab => tab.id === activeTab)?.label}
                </h2>
                <p className="text-gray-600">
                  هذا القسم قيد التطوير وسيكون متاحاً قريباً
                </p>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default MerchantDashboard
