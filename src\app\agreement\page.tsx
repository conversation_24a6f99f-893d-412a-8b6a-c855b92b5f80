'use client'

import React from 'react'
import Link from 'next/link'
import { 
  FileText, 
  Shield, 
  Users, 
  DollarSign, 
  AlertTriangle, 
  CheckCircle, 
  Scale, 
  Clock,
  Mail,
  Phone,
  MapPin,
  Calendar
} from 'lucide-react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import MobileNavigation from '@/components/layout/MobileNavigation'
import Card from '@/components/ui/Card'
import Badge from '@/components/ui/Badge'
import Container from '@/components/ui/Container'

const AgreementPage: React.FC = () => {
  const lastUpdated = '15 يناير 2024'

  const sections = [
    {
      id: 'definitions',
      title: 'التعريفات والمصطلحات',
      icon: FileText,
      content: [
        'المنصة: تشير إلى موقع وتطبيق منصة المؤثرين السعودية',
        'المستخدم: أي شخص يستخدم المنصة سواء كان مؤثراً أو تاجراً أو مبدع محتوى',
        'المؤثر: الشخص الذي يقدم خدمات التسويق والترويج عبر وسائل التواصل الاجتماعي',
        'التاجر: الشخص أو الشركة التي تطلب خدمات التسويق والترويج',
        'مبدع UGC: الشخص المتخصص في إنتاج المحتوى المولد من المستخدمين',
        'الخدمة: أي خدمة تسويقية أو إعلانية تقدم عبر المنصة'
      ]
    },
    {
      id: 'user-obligations',
      title: 'التزامات المستخدم',
      icon: Users,
      content: [
        'تقديم معلومات صحيحة ودقيقة عند التسجيل',
        'الحفاظ على سرية بيانات الحساب وكلمة المرور',
        'عدم استخدام المنصة لأغراض غير قانونية أو ضارة',
        'احترام حقوق الملكية الفكرية للآخرين',
        'الالتزام بالقوانين واللوائح المعمول بها في المملكة العربية السعودية',
        'عدم نشر محتوى مسيء أو غير لائق أو مخالف للآداب العامة'
      ]
    },
    {
      id: 'platform-obligations',
      title: 'التزامات المنصة',
      icon: Shield,
      content: [
        'توفير خدمة آمنة وموثوقة للمستخدمين',
        'حماية البيانات الشخصية وفقاً لسياسة الخصوصية',
        'توفير نظام دفع آمن ومضمون',
        'تقديم الدعم الفني والمساعدة عند الحاجة',
        'ضمان جودة الخدمات المقدمة عبر المنصة',
        'التحديث المستمر للمنصة وتحسين الخدمات'
      ]
    },
    {
      id: 'payment-terms',
      title: 'شروط الدفع والمالية',
      icon: DollarSign,
      content: [
        'جميع المدفوعات تتم بالريال السعودي',
        'يتم الاحتفاظ بالأموال في نظام ضمان آمن حتى اكتمال الخدمة',
        'يحق للمنصة خصم عمولة من كل معاملة مالية',
        'يتم تحويل الأموال للمؤثر خلال 24-48 ساعة من موافقة التاجر',
        'في حالة النزاع، تحتفظ المنصة بالحق في تجميد الأموال حتى حل النزاع',
        'جميع الرسوم والضرائب المطبقة واضحة ومعلنة مسبقاً'
      ]
    },
    {
      id: 'content-rights',
      title: 'حقوق المحتوى والملكية الفكرية',
      icon: Scale,
      content: [
        'يحتفظ المؤثر بحقوق المحتوى الذي ينتجه',
        'يحق للتاجر استخدام المحتوى المتفق عليه وفقاً للشروط المحددة',
        'لا يحق للمنصة استخدام المحتوى دون إذن صريح من المنتج',
        'يجب احترام حقوق الطبع والنشر والعلامات التجارية',
        'في حالة انتهاك حقوق الملكية الفكرية، يتحمل المنتهك المسؤولية كاملة',
        'تحتفظ المنصة بالحق في إزالة أي محتوى ينتهك حقوق الآخرين'
      ]
    },
    {
      id: 'dispute-resolution',
      title: 'حل النزاعات',
      icon: AlertTriangle,
      content: [
        'في حالة النزاع، يجب التواصل أولاً مع فريق الدعم الفني',
        'تقدم المنصة خدمة وساطة مجانية لحل النزاعات',
        'يحق لأي طرف طلب التحكيم في حالة فشل الوساطة',
        'تطبق القوانين السعودية في حل جميع النزاعات',
        'المحاكم السعودية هي المختصة بالنظر في أي نزاع قانوني',
        'يتحمل الطرف المخطئ تكاليف التحكيم والرسوم القانونية'
      ]
    }
  ]

  const keyPoints = [
    {
      icon: CheckCircle,
      title: 'ضمان الجودة',
      description: 'نضمن جودة الخدمات المقدمة عبر المنصة'
    },
    {
      icon: Shield,
      title: 'الأمان والحماية',
      description: 'نوفر أعلى مستويات الأمان لحماية بياناتك'
    },
    {
      icon: DollarSign,
      title: 'الدفع الآمن',
      description: 'نظام دفع آمن مع ضمان استرداد الأموال'
    },
    {
      icon: Users,
      title: 'الدعم المستمر',
      description: 'فريق دعم متاح 24/7 لمساعدتك'
    }
  ]

  const contactInfo = [
    {
      icon: Mail,
      label: 'البريد الإلكتروني',
      value: '<EMAIL>'
    },
    {
      icon: Phone,
      label: 'الهاتف',
      value: '+966 11 123 4567'
    },
    {
      icon: MapPin,
      label: 'العنوان',
      value: 'الرياض، المملكة العربية السعودية'
    }
  ]

  return (
    <div className="min-h-screen bg-slate-900">
      <Header />
      
      <div className="py-16">
        <Container size="lg">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
              اتفاقية المستخدم
            </h1>
            <p className="text-xl text-slate-300 max-w-3xl mx-auto mb-6">
              تحدد هذه الاتفاقية الشروط والأحكام التي تحكم استخدامك لمنصة المؤثرين السعودية
            </p>
            <div className="flex items-center justify-center gap-2 text-slate-400">
              <Calendar className="h-5 w-5" />
              <span>آخر تحديث: {lastUpdated}</span>
            </div>
          </div>

          {/* Key Points */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            {keyPoints.map((point, index) => (
              <Card key={index}>
                <div className="text-center">
                  <div className="w-12 h-12 bg-indigo-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <point.icon className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="text-lg font-bold text-white mb-2">{point.title}</h3>
                  <p className="text-slate-300 text-sm">{point.description}</p>
                </div>
              </Card>
            ))}
          </div>

          {/* Agreement Sections */}
          <div className="space-y-8 mb-12">
            {sections.map((section, index) => (
              <Card key={section.id}>
                <div className="flex items-center gap-3 mb-6">
                  <div className="w-10 h-10 bg-indigo-600 rounded-lg flex items-center justify-center">
                    <section.icon className="h-5 w-5 text-white" />
                  </div>
                  <h2 className="text-xl font-bold text-white">{section.title}</h2>
                  <Badge variant="primary" size="sm">
                    {index + 1}
                  </Badge>
                </div>
                
                <div className="space-y-3">
                  {section.content.map((item, itemIndex) => (
                    <div key={itemIndex} className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-indigo-400 rounded-full mt-2 flex-shrink-0"></div>
                      <p className="text-slate-300 leading-relaxed">{item}</p>
                    </div>
                  ))}
                </div>
              </Card>
            ))}
          </div>

          {/* Important Notice */}
          <Card className="bg-amber-500/10 border-amber-500/20 mb-12">
            <div className="flex items-start gap-4">
              <div className="w-8 h-8 bg-amber-500 rounded-full flex items-center justify-center flex-shrink-0">
                <AlertTriangle className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-amber-400 mb-2">تنبيه مهم</h3>
                <p className="text-slate-300 leading-relaxed">
                  باستخدامك لهذه المنصة، فإنك توافق على جميع الشروط والأحكام المذكورة في هذه الاتفاقية. 
                  إذا كنت لا توافق على أي من هذه الشروط، يرجى عدم استخدام المنصة. 
                  نحتفظ بالحق في تعديل هذه الاتفاقية في أي وقت، وسيتم إشعارك بأي تغييرات مهمة.
                </p>
              </div>
            </div>
          </Card>

          {/* Contact Information */}
          <Card>
            <h2 className="text-xl font-bold text-white mb-6">للاستفسارات القانونية</h2>
            <p className="text-slate-300 mb-6">
              إذا كان لديك أي استفسارات حول هذه الاتفاقية أو تحتاج إلى توضيحات إضافية، 
              يمكنك التواصل معنا عبر القنوات التالية:
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {contactInfo.map((contact, index) => (
                <div key={index} className="flex items-center gap-3 p-4 bg-slate-800 rounded-lg">
                  <div className="w-10 h-10 bg-indigo-600 rounded-full flex items-center justify-center">
                    <contact.icon className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <div className="text-slate-400 text-sm">{contact.label}</div>
                    <div className="text-white font-medium">{contact.value}</div>
                  </div>
                </div>
              ))}
            </div>
          </Card>

          {/* Related Links */}
          <div className="mt-12 text-center">
            <h3 className="text-lg font-bold text-white mb-6">وثائق ذات صلة</h3>
            <div className="flex flex-wrap justify-center gap-4">
              <Link href="/terms">
                <Badge variant="secondary" className="cursor-pointer hover:bg-slate-600">
                  شروط الاستخدام
                </Badge>
              </Link>
              <Link href="/privacy">
                <Badge variant="secondary" className="cursor-pointer hover:bg-slate-600">
                  سياسة الخصوصية
                </Badge>
              </Link>
              <Link href="/refund">
                <Badge variant="secondary" className="cursor-pointer hover:bg-slate-600">
                  سياسة الاسترداد
                </Badge>
              </Link>
              <Link href="/help">
                <Badge variant="secondary" className="cursor-pointer hover:bg-slate-600">
                  مركز المساعدة
                </Badge>
              </Link>
            </div>
          </div>
        </Container>
      </div>

      <Footer />
      <MobileNavigation />
    </div>
  )
}

export default AgreementPage
