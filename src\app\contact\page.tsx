'use client'

import React, { useState } from 'react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import { 
  Mail, 
  Phone, 
  MapPin, 
  Clock,
  Send,
  MessageCircle,
  HeadphonesIcon,
  Users
} from 'lucide-react'

const ContactPage: React.FC = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: '',
    userType: ''
  })

  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    
    // Simulate form submission
    setTimeout(() => {
      setIsSubmitting(false)
      alert('تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.')
      setFormData({
        name: '',
        email: '',
        phone: '',
        subject: '',
        message: '',
        userType: ''
      })
    }, 2000)
  }

  const contactInfo = [
    {
      icon: Phone,
      title: 'اتصل بنا',
      details: ['+966 11 123 4567', '+966 50 123 4567'],
      description: 'متاح من السبت إلى الخميس'
    },
    {
      icon: Mail,
      title: 'راسلنا',
      details: ['<EMAIL>', '<EMAIL>'],
      description: 'سنرد عليك خلال 24 ساعة'
    },
    {
      icon: MapPin,
      title: 'زورنا',
      details: ['الرياض، المملكة العربية السعودية', 'حي الملك فهد، طريق الملك فهد'],
      description: 'مواعيد العمل: 9 صباحاً - 6 مساءً'
    },
    {
      icon: Clock,
      title: 'ساعات العمل',
      details: ['السبت - الخميس: 9:00 - 18:00', 'الجمعة: مغلق'],
      description: 'دعم فني متاح 24/7'
    }
  ]

  const supportTypes = [
    {
      icon: Users,
      title: 'دعم المؤثرين',
      description: 'مساعدة في إنشاء الملف الشخصي وإدارة الحملات',
      email: '<EMAIL>'
    },
    {
      icon: MessageCircle,
      title: 'دعم التجار',
      description: 'مساعدة في إطلاق الحملات والعثور على المؤثرين',
      email: '<EMAIL>'
    },
    {
      icon: HeadphonesIcon,
      title: 'الدعم الفني',
      description: 'حل المشاكل التقنية ومساعدة في استخدام المنصة',
      email: '<EMAIL>'
    }
  ]

  const subjects = [
    'استفسار عام',
    'مشكلة تقنية',
    'طلب دعم',
    'اقتراح تحسين',
    'شكوى',
    'طلب شراكة',
    'استفسار عن الأسعار',
    'أخرى'
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
      <Header />
      
      {/* Hero Section */}
      <section className="py-20 bg-gradient-primary text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            تواصل معنا
          </h1>
          <p className="text-xl mb-8 opacity-90">
            نحن هنا لمساعدتك. تواصل معنا في أي وقت وسنكون سعداء للرد على استفساراتك
          </p>
        </div>
      </section>

      <div className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <div>
              <Card>
                <h2 className="text-2xl font-bold text-gray-900 mb-6">
                  أرسل لنا رسالة
                </h2>
                
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <Input
                      label="الاسم الكامل"
                      placeholder="أدخل اسمك"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      required
                    />
                    
                    <Input
                      label="البريد الإلكتروني"
                      type="email"
                      placeholder="<EMAIL>"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      leftIcon={<Mail className="h-5 w-5" />}
                      required
                    />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <Input
                      label="رقم الجوال"
                      placeholder="05xxxxxxxx"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      leftIcon={<Phone className="h-5 w-5" />}
                    />
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        نوع المستخدم
                      </label>
                      <select
                        value={formData.userType}
                        onChange={(e) => handleInputChange('userType', e.target.value)}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        required
                      >
                        <option value="">اختر نوع المستخدم</option>
                        <option value="influencer">مؤثر</option>
                        <option value="merchant">تاجر</option>
                        <option value="visitor">زائر</option>
                      </select>
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      موضوع الرسالة
                    </label>
                    <select
                      value={formData.subject}
                      onChange={(e) => handleInputChange('subject', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      required
                    >
                      <option value="">اختر الموضوع</option>
                      {subjects.map((subject) => (
                        <option key={subject} value={subject}>{subject}</option>
                      ))}
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الرسالة
                    </label>
                    <textarea
                      value={formData.message}
                      onChange={(e) => handleInputChange('message', e.target.value)}
                      placeholder="اكتب رسالتك هنا..."
                      rows={6}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      required
                    />
                  </div>
                  
                  <Button
                    type="submit"
                    size="lg"
                    className="w-full"
                    isLoading={isSubmitting}
                  >
                    {isSubmitting ? 'جاري الإرسال...' : 'إرسال الرسالة'}
                    {!isSubmitting && <Send className="h-5 w-5 mr-3" />}
                  </Button>
                </form>
              </Card>
            </div>

            {/* Contact Information */}
            <div className="space-y-8">
              {/* Contact Details */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                {contactInfo.map((info, index) => {
                  const Icon = info.icon
                  return (
                    <Card key={index} hover>
                      <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-4">
                        <Icon className="h-6 w-6 text-primary-600" />
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">
                        {info.title}
                      </h3>
                      <div className="space-y-1 mb-3">
                        {info.details.map((detail, i) => (
                          <p key={i} className="text-gray-700 font-medium">
                            {detail}
                          </p>
                        ))}
                      </div>
                      <p className="text-sm text-gray-500">
                        {info.description}
                      </p>
                    </Card>
                  )
                })}
              </div>

              {/* Support Types */}
              <Card>
                <h3 className="text-xl font-bold text-gray-900 mb-6">
                  أنواع الدعم المتاحة
                </h3>
                <div className="space-y-6">
                  {supportTypes.map((support, index) => {
                    const Icon = support.icon
                    return (
                      <div key={index} className="flex items-start">
                        <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center ml-4 flex-shrink-0">
                          <Icon className="h-5 w-5 text-blue-600" />
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-1">
                            {support.title}
                          </h4>
                          <p className="text-gray-600 text-sm mb-2">
                            {support.description}
                          </p>
                          <a 
                            href={`mailto:${support.email}`}
                            className="text-primary-600 hover:text-primary-700 text-sm font-medium"
                          >
                            {support.email}
                          </a>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </Card>

              {/* Quick Links */}
              <Card>
                <h3 className="text-xl font-bold text-gray-900 mb-6">
                  روابط مفيدة
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  <a href="/faq" className="text-primary-600 hover:text-primary-700 font-medium">
                    الأسئلة الشائعة
                  </a>
                  <a href="/help" className="text-primary-600 hover:text-primary-700 font-medium">
                    مركز المساعدة
                  </a>
                  <a href="/terms" className="text-primary-600 hover:text-primary-700 font-medium">
                    شروط الاستخدام
                  </a>
                  <a href="/privacy" className="text-primary-600 hover:text-primary-700 font-medium">
                    سياسة الخصوصية
                  </a>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  )
}

export default ContactPage
