'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import {
  Plus,
  MessageCircle,
  Phone,
  Users,
  Store,
  X,
  Zap,
  Video
} from 'lucide-react'

interface FloatingActionButtonProps {
  variant?: 'default' | 'merchant' | 'influencer'
}

const FloatingActionButton: React.FC<FloatingActionButtonProps> = ({
  variant = 'default'
}) => {
  const [isOpen, setIsOpen] = useState(false)

  const toggleMenu = () => {
    setIsOpen(!isOpen)
  }

  const defaultActions = [
    {
      name: 'تواصل معنا',
      href: '/contact',
      icon: MessageCircle,
      color: 'bg-blue-500 hover:bg-blue-600',
      description: 'راسلنا الآن'
    },
    {
      name: 'انضم كمؤثر',
      href: '/register/influencer',
      icon: Users,
      color: 'bg-purple-500 hover:bg-purple-600',
      description: '👑 سجل الآن'
    },
    {
      name: 'انضم كمبدع UGC',
      href: '/register/ugc-creator',
      icon: Video,
      color: 'bg-pink-500 hover:bg-pink-600',
      description: '🎬 أنتج محتوى'
    },
    {
      name: 'ابدأ حملة',
      href: '/register/merchant',
      icon: Store,
      color: 'bg-orange-500 hover:bg-orange-600',
      description: '🏪 للتجار'
    }
  ]

  const merchantActions = [
    {
      name: 'حملة جديدة',
      href: '/campaigns/create',
      icon: Plus,
      color: 'bg-green-500 hover:bg-green-600',
      description: 'أطلق حملتك'
    },
    {
      name: 'تصفح المؤثرين',
      href: '/influencers',
      icon: Users,
      color: 'bg-blue-500 hover:bg-blue-600',
      description: 'اختر المؤثر'
    },
    {
      name: 'الدعم الفني',
      href: '/support',
      icon: MessageCircle,
      color: 'bg-purple-500 hover:bg-purple-600',
      description: 'مساعدة فورية'
    }
  ]

  const influencerActions = [
    {
      name: 'الفرص المتاحة',
      href: '/opportunities',
      icon: Zap,
      color: 'bg-yellow-500 hover:bg-yellow-600',
      description: 'فرص جديدة'
    },
    {
      name: 'رسائل التجار',
      href: '/messages',
      icon: MessageCircle,
      color: 'bg-blue-500 hover:bg-blue-600',
      description: 'تواصل مع التجار'
    },
    {
      name: 'الدعم الفني',
      href: '/support',
      icon: MessageCircle,
      color: 'bg-purple-500 hover:bg-purple-600',
      description: 'مساعدة فورية'
    }
  ]

  const getActions = () => {
    switch (variant) {
      case 'merchant':
        return merchantActions
      case 'influencer':
        return influencerActions
      default:
        return defaultActions
    }
  }

  const actions = getActions()

  return (
    <div className="fixed bottom-24 left-4 z-40 md:bottom-8">
      {/* الإجراءات المنبثقة */}
      {isOpen && (
        <div className="mb-4 space-y-3">
          {actions.map((action, index) => {
            const Icon = action.icon
            return (
              <div
                key={action.name}
                className="flex items-center slide-up"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                {/* وصف الإجراء */}
                <div className="bg-slate-800 text-white px-3 py-2 rounded-lg text-sm font-medium ml-3 shadow-lg border border-slate-700">
                  <div className="font-medium">{action.name}</div>
                  <div className="text-xs text-slate-400">{action.description}</div>
                </div>

                {/* زر الإجراء */}
                <Link href={action.href}>
                  <button
                    className="w-12 h-12 rounded-full bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg transition-colors flex items-center justify-center"
                    onClick={() => setIsOpen(false)}
                  >
                    <Icon className="h-5 w-5" />
                  </button>
                </Link>
              </div>
            )
          })}
        </div>
      )}

      {/* الزر الرئيسي */}
      <button
        onClick={toggleMenu}
        className={`w-14 h-14 rounded-full bg-indigo-600 text-white shadow-lg hover:bg-indigo-700 transition-all duration-200 flex items-center justify-center ${
          isOpen ? 'rotate-45' : ''
        }`}
      >
        {isOpen ? (
          <X className="h-6 w-6" />
        ) : (
          <Plus className="h-6 w-6" />
        )}
      </button>
    </div>
  )
}

export default FloatingActionButton
