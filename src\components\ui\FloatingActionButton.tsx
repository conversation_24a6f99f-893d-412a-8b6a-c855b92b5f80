'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { 
  Plus, 
  MessageCircle, 
  Phone, 
  Users, 
  Store,
  X,
  Zap
} from 'lucide-react'

interface FloatingActionButtonProps {
  variant?: 'default' | 'merchant' | 'influencer'
}

const FloatingActionButton: React.FC<FloatingActionButtonProps> = ({ 
  variant = 'default' 
}) => {
  const [isOpen, setIsOpen] = useState(false)

  const toggleMenu = () => {
    setIsOpen(!isOpen)
  }

  const defaultActions = [
    {
      name: 'تواصل معنا',
      href: '/contact',
      icon: MessageCircle,
      color: 'bg-blue-500 hover:bg-blue-600',
      description: 'راسلنا الآن'
    },
    {
      name: 'اتصل بنا',
      href: 'tel:+966500000000',
      icon: Phone,
      color: 'bg-green-500 hover:bg-green-600',
      description: 'مكالمة مباشرة'
    },
    {
      name: 'انضم كمؤثر',
      href: '/register/influencer',
      icon: Users,
      color: 'bg-purple-500 hover:bg-purple-600',
      description: 'سجل الآن'
    },
    {
      name: 'ابدأ حملة',
      href: '/register/merchant',
      icon: Store,
      color: 'bg-orange-500 hover:bg-orange-600',
      description: 'للتجار'
    }
  ]

  const merchantActions = [
    {
      name: 'حملة جديدة',
      href: '/campaigns/create',
      icon: Plus,
      color: 'bg-green-500 hover:bg-green-600',
      description: 'أطلق حملتك'
    },
    {
      name: 'تصفح المؤثرين',
      href: '/influencers',
      icon: Users,
      color: 'bg-blue-500 hover:bg-blue-600',
      description: 'اختر المؤثر'
    },
    {
      name: 'الدعم الفني',
      href: '/support',
      icon: MessageCircle,
      color: 'bg-purple-500 hover:bg-purple-600',
      description: 'مساعدة فورية'
    }
  ]

  const influencerActions = [
    {
      name: 'الفرص المتاحة',
      href: '/opportunities',
      icon: Zap,
      color: 'bg-yellow-500 hover:bg-yellow-600',
      description: 'فرص جديدة'
    },
    {
      name: 'رسائل التجار',
      href: '/messages',
      icon: MessageCircle,
      color: 'bg-blue-500 hover:bg-blue-600',
      description: 'تواصل مع التجار'
    },
    {
      name: 'الدعم الفني',
      href: '/support',
      icon: MessageCircle,
      color: 'bg-purple-500 hover:bg-purple-600',
      description: 'مساعدة فورية'
    }
  ]

  const getActions = () => {
    switch (variant) {
      case 'merchant':
        return merchantActions
      case 'influencer':
        return influencerActions
      default:
        return defaultActions
    }
  }

  const actions = getActions()

  return (
    <div className="fixed bottom-24 left-4 z-40 md:bottom-8">
      {/* الإجراءات المنبثقة */}
      {isOpen && (
        <div className="mb-4 space-y-3">
          {actions.map((action, index) => {
            const Icon = action.icon
            return (
              <div
                key={action.name}
                className="flex items-center slide-up"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                {/* وصف الإجراء */}
                <div className="bg-gray-900 text-white px-3 py-2 rounded-lg text-sm font-medium ml-3 shadow-lg">
                  <div className="font-semibold">{action.name}</div>
                  <div className="text-xs opacity-80">{action.description}</div>
                </div>
                
                {/* زر الإجراء */}
                <Link href={action.href}>
                  <button
                    className={`w-12 h-12 rounded-full ${action.color} text-white shadow-lg hover:shadow-xl transform hover:scale-110 transition-all duration-300 flex items-center justify-center touch-target`}
                    onClick={() => setIsOpen(false)}
                  >
                    <Icon className="h-6 w-6" />
                  </button>
                </Link>
              </div>
            )
          })}
        </div>
      )}

      {/* الزر الرئيسي */}
      <button
        onClick={toggleMenu}
        className={`w-14 h-14 rounded-full bg-gradient-to-r from-green-500 to-green-600 text-white shadow-lg hover:shadow-xl transform transition-all duration-300 flex items-center justify-center touch-target ${
          isOpen ? 'rotate-45 scale-110' : 'hover:scale-110'
        }`}
      >
        {isOpen ? (
          <X className="h-7 w-7" />
        ) : (
          <Plus className="h-7 w-7" />
        )}
      </button>

      {/* مؤشر النبضة */}
      {!isOpen && (
        <div className="absolute inset-0 rounded-full bg-green-500 animate-ping opacity-20"></div>
      )}
    </div>
  )
}

export default FloatingActionButton
