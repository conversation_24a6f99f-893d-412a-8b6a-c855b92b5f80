'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { 
  Briefcase, 
  MapPin, 
  Clock, 
  DollarSign, 
  Users, 
  Code, 
  Palette, 
  BarChart3, 
  MessageCircle,
  Search,
  Filter,
  Heart,
  Star,
  Zap,
  Shield,
  ArrowRight,
  Calendar,
  Building,
  GraduationCap
} from 'lucide-react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import MobileNavigation from '@/components/layout/MobileNavigation'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Badge from '@/components/ui/Badge'
import Input from '@/components/ui/Input'
import Container from '@/components/ui/Container'

const CareersPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedDepartment, setSelectedDepartment] = useState('all')
  const [selectedType, setSelectedType] = useState('all')

  const jobs = [
    {
      id: 1,
      title: 'مطور Full Stack',
      department: 'التطوير التقني',
      type: 'دوام كامل',
      location: 'الرياض',
      salary: '12,000 - 18,000 ريال',
      experience: '3-5 سنوات',
      description: 'نبحث عن مطور Full Stack متمرس للانضمام إلى فريق التطوير لدينا. المرشح المثالي يجب أن يكون لديه خبرة في React, Node.js, وقواعد البيانات.',
      requirements: [
        'خبرة 3+ سنوات في تطوير الويب',
        'إتقان React, Node.js, TypeScript',
        'خبرة في قواعد البيانات (PostgreSQL, MongoDB)',
        'معرفة بـ AWS أو خدمات السحابة الأخرى',
        'إجادة اللغة العربية والإنجليزية'
      ],
      benefits: [
        'راتب تنافسي',
        'تأمين صحي شامل',
        'إجازات مرنة',
        'فرص تطوير مهني',
        'بيئة عمل إبداعية'
      ],
      posted: '2024-01-15',
      urgent: true
    },
    {
      id: 2,
      title: 'مصمم UX/UI',
      department: 'التصميم',
      type: 'دوام كامل',
      location: 'الرياض',
      salary: '10,000 - 15,000 ريال',
      experience: '2-4 سنوات',
      description: 'نبحث عن مصمم UX/UI مبدع لتصميم تجارب مستخدم استثنائية. المرشح المثالي يجب أن يكون لديه عين فنية وفهم عميق لاحتياجات المستخدمين.',
      requirements: [
        'خبرة 2+ سنوات في تصميم UX/UI',
        'إتقان Figma, Adobe Creative Suite',
        'فهم مبادئ تصميم المنتجات الرقمية',
        'خبرة في إجراء البحوث والاختبارات',
        'محفظة أعمال قوية'
      ],
      benefits: [
        'راتب تنافسي',
        'أدوات تصميم حديثة',
        'ورش تدريبية',
        'مرونة في العمل',
        'فريق إبداعي'
      ],
      posted: '2024-01-12',
      urgent: false
    },
    {
      id: 3,
      title: 'أخصائي تسويق رقمي',
      department: 'التسويق',
      type: 'دوام كامل',
      location: 'جدة',
      salary: '8,000 - 12,000 ريال',
      experience: '2-3 سنوات',
      description: 'نبحث عن أخصائي تسويق رقمي لإدارة حملاتنا التسويقية وبناء حضورنا الرقمي. المرشح المثالي يجب أن يكون مبدعاً ومتابعاً لأحدث اتجاهات التسويق.',
      requirements: [
        'خبرة 2+ سنوات في التسويق الرقمي',
        'إتقان Google Ads, Facebook Ads',
        'خبرة في تحليل البيانات والتقارير',
        'مهارات كتابة محتوى ممتازة',
        'معرفة بـ SEO و SEM'
      ],
      benefits: [
        'راتب تنافسي',
        'عمولات على الأداء',
        'دورات تدريبية',
        'أدوات تسويق متقدمة',
        'فرص نمو سريع'
      ],
      posted: '2024-01-10',
      urgent: false
    },
    {
      id: 4,
      title: 'مدير علاقات المؤثرين',
      department: 'علاقات المؤثرين',
      type: 'دوام كامل',
      location: 'الرياض',
      salary: '15,000 - 20,000 ريال',
      experience: '4-6 سنوات',
      description: 'نبحث عن مدير علاقات مؤثرين لإدارة شبكة المؤثرين لدينا وبناء شراكات استراتيجية. المرشح المثالي يجب أن يكون لديه خبرة في صناعة التأثير الرقمي.',
      requirements: [
        'خبرة 4+ سنوات في إدارة المؤثرين',
        'شبكة علاقات واسعة مع المؤثرين',
        'مهارات تفاوض وتواصل ممتازة',
        'فهم عميق لصناعة التأثير الرقمي',
        'خبرة في إدارة الحملات الإعلانية'
      ],
      benefits: [
        'راتب تنافسي جداً',
        'عمولات على الصفقات',
        'سيارة شركة',
        'مرونة في العمل',
        'فرص سفر'
      ],
      posted: '2024-01-08',
      urgent: true
    },
    {
      id: 5,
      title: 'محلل بيانات',
      department: 'التحليل والبيانات',
      type: 'دوام جزئي',
      location: 'عن بُعد',
      salary: '6,000 - 10,000 ريال',
      experience: '1-3 سنوات',
      description: 'نبحث عن محلل بيانات لمساعدتنا في فهم سلوك المستخدمين وتحسين أداء المنصة. المرشح المثالي يجب أن يكون لديه شغف بالأرقام والتحليل.',
      requirements: [
        'خبرة في تحليل البيانات',
        'إتقان SQL, Python أو R',
        'خبرة في Google Analytics',
        'مهارات في Excel المتقدم',
        'القدرة على إنشاء التقارير'
      ],
      benefits: [
        'مرونة في العمل',
        'عمل عن بُعد',
        'أدوات تحليل متقدمة',
        'فرص تعلم',
        'فريق داعم'
      ],
      posted: '2024-01-05',
      urgent: false
    },
    {
      id: 6,
      title: 'أخصائي دعم فني',
      department: 'الدعم الفني',
      type: 'دوام كامل',
      location: 'الدمام',
      salary: '5,000 - 8,000 ريال',
      experience: '1-2 سنة',
      description: 'نبحث عن أخصائي دعم فني لمساعدة عملائنا وحل مشاكلهم التقنية. المرشح المثالي يجب أن يكون صبوراً ولديه مهارات تواصل ممتازة.',
      requirements: [
        'خبرة في الدعم الفني',
        'مهارات تواصل ممتازة',
        'صبر في التعامل مع العملاء',
        'معرفة أساسية بالتقنية',
        'إجادة اللغة العربية والإنجليزية'
      ],
      benefits: [
        'راتب ثابت',
        'تدريب مستمر',
        'بيئة عمل داعمة',
        'فرص ترقية',
        'تأمين صحي'
      ],
      posted: '2024-01-03',
      urgent: false
    }
  ]

  const departments = [
    { id: 'all', name: 'جميع الأقسام', icon: Building },
    { id: 'التطوير التقني', name: 'التطوير التقني', icon: Code },
    { id: 'التصميم', name: 'التصميم', icon: Palette },
    { id: 'التسويق', name: 'التسويق', icon: BarChart3 },
    { id: 'علاقات المؤثرين', name: 'علاقات المؤثرين', icon: Users },
    { id: 'التحليل والبيانات', name: 'التحليل والبيانات', icon: BarChart3 },
    { id: 'الدعم الفني', name: 'الدعم الفني', icon: MessageCircle }
  ]

  const jobTypes = [
    { id: 'all', name: 'جميع الأنواع' },
    { id: 'دوام كامل', name: 'دوام كامل' },
    { id: 'دوام جزئي', name: 'دوام جزئي' },
    { id: 'عن بُعد', name: 'عن بُعد' },
    { id: 'تدريب', name: 'تدريب' }
  ]

  const benefits = [
    {
      icon: DollarSign,
      title: 'رواتب تنافسية',
      description: 'نقدم رواتب تنافسية تتماشى مع معايير السوق'
    },
    {
      icon: Heart,
      title: 'تأمين صحي شامل',
      description: 'تأمين صحي شامل لك ولعائلتك'
    },
    {
      icon: Clock,
      title: 'مرونة في العمل',
      description: 'ساعات عمل مرنة وإمكانية العمل عن بُعد'
    },
    {
      icon: GraduationCap,
      title: 'التطوير المهني',
      description: 'دورات تدريبية وفرص تطوير مستمرة'
    },
    {
      icon: Users,
      title: 'فريق متميز',
      description: 'العمل مع فريق من المتخصصين المتميزين'
    },
    {
      icon: Zap,
      title: 'بيئة إبداعية',
      description: 'بيئة عمل محفزة للإبداع والابتكار'
    }
  ]

  const filteredJobs = jobs.filter(job => {
    const matchesSearch = job.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         job.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesDepartment = selectedDepartment === 'all' || job.department === selectedDepartment
    const matchesType = selectedType === 'all' || job.type === selectedType
    
    return matchesSearch && matchesDepartment && matchesType
  })

  const getDaysAgo = (dateString: string) => {
    const posted = new Date(dateString)
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - posted.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  return (
    <div className="min-h-screen bg-slate-900">
      <Header />
      
      <div className="py-16">
        <Container size="lg">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
              انضم إلى فريقنا
            </h1>
            <p className="text-xl text-slate-300 max-w-3xl mx-auto mb-8">
              نبحث عن المواهب المتميزة للانضمام إلى رحلتنا في بناء أفضل منصة للتسويق بالمؤثرين في المنطقة
            </p>
            <div className="flex items-center justify-center gap-8 text-slate-400">
              <div className="text-center">
                <div className="text-2xl font-bold text-white">{jobs.length}</div>
                <div className="text-sm">وظيفة متاحة</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-white">6</div>
                <div className="text-sm">أقسام مختلفة</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-white">30+</div>
                <div className="text-sm">عضو فريق</div>
              </div>
            </div>
          </div>

          {/* Search and Filters */}
          <Card className="mb-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-5 w-5" />
                <Input
                  placeholder="ابحث عن وظيفة..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
              
              <select
                value={selectedDepartment}
                onChange={(e) => setSelectedDepartment(e.target.value)}
                className="input"
              >
                {departments.map((dept) => (
                  <option key={dept.id} value={dept.id}>
                    {dept.name}
                  </option>
                ))}
              </select>

              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="input"
              >
                {jobTypes.map((type) => (
                  <option key={type.id} value={type.id}>
                    {type.name}
                  </option>
                ))}
              </select>
            </div>
          </Card>

          {/* Jobs List */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-16">
            {filteredJobs.map((job) => (
              <Card key={job.id} hover>
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="text-lg font-bold text-white">{job.title}</h3>
                      {job.urgent && (
                        <Badge variant="warning" size="sm">
                          عاجل
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-4 text-sm text-slate-400 mb-3">
                      <div className="flex items-center">
                        <Building className="h-4 w-4 ml-1" />
                        {job.department}
                      </div>
                      <div className="flex items-center">
                        <MapPin className="h-4 w-4 ml-1" />
                        {job.location}
                      </div>
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 ml-1" />
                        {job.type}
                      </div>
                    </div>
                  </div>
                  <div className="text-left">
                    <div className="text-indigo-400 font-semibold">{job.salary}</div>
                    <div className="text-slate-400 text-sm">{job.experience}</div>
                  </div>
                </div>

                <p className="text-slate-300 text-sm mb-4 line-clamp-2">
                  {job.description}
                </p>

                <div className="flex items-center justify-between pt-4 border-t border-slate-700">
                  <div className="text-slate-400 text-sm">
                    منذ {getDaysAgo(job.posted)} أيام
                  </div>
                  <Button variant="primary" size="sm">
                    تقدم للوظيفة
                    <ArrowRight className="h-4 w-4 mr-2" />
                  </Button>
                </div>
              </Card>
            ))}
          </div>

          {filteredJobs.length === 0 && (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">🔍</div>
              <h3 className="text-xl font-bold text-white mb-2">لا توجد وظائف مطابقة</h3>
              <p className="text-slate-400">جرب تغيير معايير البحث أو تصفح جميع الوظائف المتاحة</p>
            </div>
          )}

          {/* Benefits */}
          <div className="mb-16">
            <h2 className="text-3xl font-bold text-white text-center mb-12">مزايا العمل معنا</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {benefits.map((benefit, index) => (
                <Card key={index}>
                  <div className="text-center">
                    <div className="w-12 h-12 bg-indigo-600 rounded-full flex items-center justify-center mx-auto mb-4">
                      <benefit.icon className="h-6 w-6 text-white" />
                    </div>
                    <h3 className="text-lg font-bold text-white mb-2">{benefit.title}</h3>
                    <p className="text-slate-300 text-sm">{benefit.description}</p>
                  </div>
                </Card>
              ))}
            </div>
          </div>

          {/* CTA */}
          <div className="text-center">
            <Card className="bg-gradient-to-r from-indigo-600 to-purple-600">
              <div className="text-center">
                <h2 className="text-2xl font-bold text-white mb-4">لم تجد الوظيفة المناسبة؟</h2>
                <p className="text-indigo-100 mb-6">
                  أرسل لنا سيرتك الذاتية وسنتواصل معك عند توفر فرصة مناسبة
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Link href="/contact">
                    <Button variant="secondary" className="bg-white text-indigo-600 hover:bg-gray-100">
                      أرسل سيرتك الذاتية
                    </Button>
                  </Link>
                  <Link href="/team">
                    <Button variant="outline" className="border-white text-white hover:bg-white/10">
                      تعرف على الفريق
                    </Button>
                  </Link>
                </div>
              </div>
            </Card>
          </div>
        </Container>
      </div>

      <Footer />
      <MobileNavigation />
    </div>
  )
}

export default CareersPage
