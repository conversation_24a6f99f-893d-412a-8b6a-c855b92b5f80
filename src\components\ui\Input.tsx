'use client'

import React from 'react'
import { clsx } from 'clsx'

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string
  error?: string
  helperText?: string
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
}

const Input: React.FC<InputProps> = ({
  label,
  error,
  helperText,
  leftIcon,
  rightIcon,
  className,
  id,
  ...props
}) => {
  const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`

  const baseClasses = 'input'

  const stateClasses = error
    ? 'border-red-500 focus:border-red-500 focus:ring-red-500/20'
    : 'focus:border-indigo-500 focus:ring-indigo-500/20'

  const iconPadding = leftIcon ? 'pr-12' : rightIcon ? 'pl-12' : ''

  return (
    <div className="w-full">
      {label && (
        <label htmlFor={inputId} className="block text-sm font-medium text-slate-200 mb-2">
          {label}
        </label>
      )}

      <div className="relative">
        {leftIcon && (
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400">
            {leftIcon}
          </div>
        )}

        <input
          id={inputId}
          className={clsx(
            baseClasses,
            stateClasses,
            iconPadding,
            'bg-slate-800/90 border-slate-600 text-slate-100 placeholder-slate-400',
            'focus:bg-slate-800 focus:ring-2 focus:ring-offset-0',
            'transition-all duration-200',
            className
          )}
          {...props}
        />

        {rightIcon && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400">
            {rightIcon}
          </div>
        )}
      </div>

      {error && (
        <p className="mt-2 text-sm text-red-300 font-medium">{error}</p>
      )}

      {helperText && !error && (
        <p className="mt-2 text-sm text-slate-300">{helperText}</p>
      )}
    </div>
  )
}

export default Input
