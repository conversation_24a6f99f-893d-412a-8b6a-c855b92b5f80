'use client'

import React from 'react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import Card from '@/components/ui/Card'
import { 
  Shield, 
  Lock, 
  Eye,
  Database,
  UserCheck,
  AlertTriangle,
  Clock,
  Mail
} from 'lucide-react'

const PrivacyPage: React.FC = () => {
  const lastUpdated = '15 يناير 2024'

  const dataTypes = [
    {
      icon: UserCheck,
      title: 'المعلومات الشخصية',
      items: [
        'الاسم الكامل',
        'البريد الإلكتروني',
        'رقم الهاتف',
        'العنوان والمدينة',
        'تاريخ الميلاد'
      ]
    },
    {
      icon: Database,
      title: 'معلومات الحساب',
      items: [
        'اسم المستخدم',
        'كلمة المرور (مشفرة)',
        'تفضيلات الحساب',
        'سجل النشاط',
        'إعدادات الخصوصية'
      ]
    },
    {
      icon: Eye,
      title: 'بيانات الاستخدام',
      items: [
        'عنوان IP',
        'نوع المتصفح',
        'نظام التشغيل',
        'صفحات الموقع المزارة',
        'وقت ومدة الزيارة'
      ]
    }
  ]

  const protectionMeasures = [
    {
      icon: Lock,
      title: 'التشفير',
      description: 'جميع البيانات الحساسة مشفرة باستخدام أحدث معايير التشفير'
    },
    {
      icon: Shield,
      title: 'الحماية من الاختراق',
      description: 'أنظمة حماية متقدمة لمنع الوصول غير المصرح به'
    },
    {
      icon: Database,
      title: 'النسخ الاحتياطي',
      description: 'نسخ احتياطية منتظمة لضمان عدم فقدان البيانات'
    },
    {
      icon: UserCheck,
      title: 'التحكم في الوصول',
      description: 'وصول محدود للبيانات للموظفين المخولين فقط'
    }
  ]

  const userRights = [
    'الحق في معرفة البيانات المجمعة عنك',
    'الحق في تصحيح البيانات غير الصحيحة',
    'الحق في حذف بياناتك الشخصية',
    'الحق في تقييد معالجة بياناتك',
    'الحق في نقل بياناتك',
    'الحق في الاعتراض على معالجة البيانات',
    'الحق في سحب الموافقة في أي وقت'
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
      <Header />
      
      {/* Hero Section */}
      <section className="py-20 bg-gradient-primary text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6">
            <Shield className="h-8 w-8 text-white" />
          </div>
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            سياسة الخصوصية
          </h1>
          <p className="text-xl mb-4 opacity-90">
            نحن ملتزمون بحماية خصوصيتك وأمان بياناتك الشخصية
          </p>
          <div className="flex items-center justify-center text-sm opacity-75">
            <Clock className="h-4 w-4 ml-2" />
            <span>آخر تحديث: {lastUpdated}</span>
          </div>
        </div>
      </section>

      <div className="py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Introduction */}
          <Card className="mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">مقدمة</h2>
            <p className="text-gray-700 leading-relaxed mb-4">
              في منصة المؤثرين السعودية، نحن نقدر ثقتكم ونلتزم بحماية خصوصيتكم. 
              هذه السياسة توضح كيفية جمعنا واستخدامنا وحمايتنا لمعلوماتكم الشخصية.
            </p>
            <p className="text-gray-700 leading-relaxed">
              نحن نلتزم بأعلى معايير الخصوصية والأمان، ونتبع جميع القوانين واللوائح 
              المعمول بها في المملكة العربية السعودية فيما يتعلق بحماية البيانات الشخصية.
            </p>
          </Card>

          {/* Data Collection */}
          <Card className="mb-8">
            <h2 className="text-xl font-bold text-gray-900 mb-6">
              البيانات التي نجمعها
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {dataTypes.map((type, index) => {
                const Icon = type.icon
                return (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center mb-3">
                      <Icon className="h-6 w-6 text-primary-600 ml-3" />
                      <h3 className="font-semibold text-gray-900">{type.title}</h3>
                    </div>
                    <ul className="space-y-1">
                      {type.items.map((item, i) => (
                        <li key={i} className="text-sm text-gray-600">• {item}</li>
                      ))}
                    </ul>
                  </div>
                )
              })}
            </div>
          </Card>

          {/* How We Use Data */}
          <Card className="mb-8">
            <h2 className="text-xl font-bold text-gray-900 mb-4">
              كيف نستخدم بياناتك
            </h2>
            <div className="space-y-4">
              <div className="flex items-start">
                <div className="w-2 h-2 bg-primary-600 rounded-full mt-2 ml-3 flex-shrink-0"></div>
                <p className="text-gray-700">
                  <strong>تقديم الخدمات:</strong> لتمكينك من استخدام المنصة والوصول إلى جميع الميزات
                </p>
              </div>
              <div className="flex items-start">
                <div className="w-2 h-2 bg-primary-600 rounded-full mt-2 ml-3 flex-shrink-0"></div>
                <p className="text-gray-700">
                  <strong>التواصل:</strong> لإرسال الإشعارات المهمة والتحديثات حول حسابك
                </p>
              </div>
              <div className="flex items-start">
                <div className="w-2 h-2 bg-primary-600 rounded-full mt-2 ml-3 flex-shrink-0"></div>
                <p className="text-gray-700">
                  <strong>تحسين الخدمة:</strong> لفهم كيفية استخدام المنصة وتطوير ميزات جديدة
                </p>
              </div>
              <div className="flex items-start">
                <div className="w-2 h-2 bg-primary-600 rounded-full mt-2 ml-3 flex-shrink-0"></div>
                <p className="text-gray-700">
                  <strong>الأمان:</strong> لحماية المنصة ومنع الاحتيال والأنشطة المشبوهة
                </p>
              </div>
              <div className="flex items-start">
                <div className="w-2 h-2 bg-primary-600 rounded-full mt-2 ml-3 flex-shrink-0"></div>
                <p className="text-gray-700">
                  <strong>الامتثال القانوني:</strong> للوفاء بالالتزامات القانونية والتنظيمية
                </p>
              </div>
            </div>
          </Card>

          {/* Data Protection */}
          <Card className="mb-8">
            <h2 className="text-xl font-bold text-gray-900 mb-6">
              كيف نحمي بياناتك
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {protectionMeasures.map((measure, index) => {
                const Icon = measure.icon
                return (
                  <div key={index} className="flex items-start">
                    <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center ml-4 flex-shrink-0">
                      <Icon className="h-6 w-6 text-primary-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-2">{measure.title}</h3>
                      <p className="text-gray-600 text-sm">{measure.description}</p>
                    </div>
                  </div>
                )
              })}
            </div>
          </Card>

          {/* Data Sharing */}
          <Card className="mb-8">
            <h2 className="text-xl font-bold text-gray-900 mb-4">
              مشاركة البيانات
            </h2>
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
              <div className="flex items-start">
                <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5 ml-3 flex-shrink-0" />
                <p className="text-yellow-800 text-sm">
                  <strong>مبدأ مهم:</strong> نحن لا نبيع أو نؤجر بياناتك الشخصية لأطراف ثالثة أبداً.
                </p>
              </div>
            </div>
            <p className="text-gray-700 mb-4">قد نشارك بياناتك في الحالات التالية فقط:</p>
            <div className="space-y-3">
              <div className="flex items-start">
                <div className="w-2 h-2 bg-gray-400 rounded-full mt-2 ml-3 flex-shrink-0"></div>
                <p className="text-gray-700">مع موافقتك الصريحة</p>
              </div>
              <div className="flex items-start">
                <div className="w-2 h-2 bg-gray-400 rounded-full mt-2 ml-3 flex-shrink-0"></div>
                <p className="text-gray-700">مع مقدمي الخدمات الموثوقين الذين يساعدوننا في تشغيل المنصة</p>
              </div>
              <div className="flex items-start">
                <div className="w-2 h-2 bg-gray-400 rounded-full mt-2 ml-3 flex-shrink-0"></div>
                <p className="text-gray-700">عند الطلب من السلطات القانونية المختصة</p>
              </div>
              <div className="flex items-start">
                <div className="w-2 h-2 bg-gray-400 rounded-full mt-2 ml-3 flex-shrink-0"></div>
                <p className="text-gray-700">لحماية حقوقنا أو حقوق المستخدمين الآخرين</p>
              </div>
            </div>
          </Card>

          {/* User Rights */}
          <Card className="mb-8">
            <h2 className="text-xl font-bold text-gray-900 mb-4">
              حقوقك في البيانات
            </h2>
            <p className="text-gray-700 mb-4">لديك الحقوق التالية فيما يتعلق ببياناتك الشخصية:</p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {userRights.map((right, index) => (
                <div key={index} className="flex items-start">
                  <UserCheck className="h-5 w-5 text-green-500 mt-0.5 ml-3 flex-shrink-0" />
                  <p className="text-gray-700 text-sm">{right}</p>
                </div>
              ))}
            </div>
          </Card>

          {/* Cookies */}
          <Card className="mb-8">
            <h2 className="text-xl font-bold text-gray-900 mb-4">
              ملفات تعريف الارتباط (Cookies)
            </h2>
            <p className="text-gray-700 mb-4">
              نستخدم ملفات تعريف الارتباط لتحسين تجربتك على المنصة. هذه الملفات تساعدنا في:
            </p>
            <div className="space-y-2">
              <div className="flex items-start">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 ml-3 flex-shrink-0"></div>
                <p className="text-gray-700">تذكر تفضيلاتك وإعداداتك</p>
              </div>
              <div className="flex items-start">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 ml-3 flex-shrink-0"></div>
                <p className="text-gray-700">تحليل استخدام الموقع وتحسين الأداء</p>
              </div>
              <div className="flex items-start">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 ml-3 flex-shrink-0"></div>
                <p className="text-gray-700">توفير محتوى مخصص لك</p>
              </div>
            </div>
            <p className="text-gray-700 mt-4">
              يمكنك التحكم في ملفات تعريف الارتباط من خلال إعدادات متصفحك.
            </p>
          </Card>

          {/* Data Retention */}
          <Card className="mb-8">
            <h2 className="text-xl font-bold text-gray-900 mb-4">
              الاحتفاظ بالبيانات
            </h2>
            <p className="text-gray-700 mb-4">
              نحتفظ ببياناتك الشخصية للمدة اللازمة لتقديم خدماتنا أو حسب ما يتطلبه القانون:
            </p>
            <div className="space-y-3">
              <div className="flex items-start">
                <Clock className="h-5 w-5 text-gray-500 mt-0.5 ml-3 flex-shrink-0" />
                <p className="text-gray-700">
                  <strong>بيانات الحساب:</strong> طوال فترة نشاط حسابك + 3 سنوات بعد الإغلاق
                </p>
              </div>
              <div className="flex items-start">
                <Clock className="h-5 w-5 text-gray-500 mt-0.5 ml-3 flex-shrink-0" />
                <p className="text-gray-700">
                  <strong>بيانات المعاملات:</strong> 7 سنوات للامتثال للمتطلبات المحاسبية
                </p>
              </div>
              <div className="flex items-start">
                <Clock className="h-5 w-5 text-gray-500 mt-0.5 ml-3 flex-shrink-0" />
                <p className="text-gray-700">
                  <strong>سجلات الأمان:</strong> سنة واحدة لأغراض الأمان والحماية
                </p>
              </div>
            </div>
          </Card>

          {/* Contact */}
          <Card className="mb-8 bg-blue-50 border-blue-200">
            <div className="flex items-start">
              <Mail className="h-6 w-6 text-blue-600 mt-1 ml-4 flex-shrink-0" />
              <div>
                <h3 className="text-lg font-semibold text-blue-900 mb-2">
                  تواصل معنا حول الخصوصية
                </h3>
                <p className="text-blue-800 leading-relaxed mb-4">
                  إذا كان لديك أي أسئلة حول سياسة الخصوصية أو تريد ممارسة حقوقك في البيانات:
                </p>
                <div className="space-y-2 text-blue-800">
                  <p>• مسؤول حماية البيانات: <EMAIL></p>
                  <p>• الهاتف: +966 11 123 4567</p>
                  <p>• العنوان: الرياض، المملكة العربية السعودية</p>
                </div>
              </div>
            </div>
          </Card>

          {/* Updates */}
          <Card className="mb-8">
            <h2 className="text-xl font-bold text-gray-900 mb-4">
              تحديثات السياسة
            </h2>
            <p className="text-gray-700 leading-relaxed">
              قد نقوم بتحديث هذه السياسة من وقت لآخر. سنقوم بإشعارك بأي تغييرات مهمة 
              عبر البريد الإلكتروني أو من خلال إشعار على المنصة. ننصحك بمراجعة هذه 
              السياسة بانتظام للبقاء على اطلاع بكيفية حمايتنا لمعلوماتك.
            </p>
          </Card>

          {/* Last Updated */}
          <div className="text-center text-gray-500">
            <p>آخر تحديث: {lastUpdated}</p>
            <p className="mt-2">جميع الحقوق محفوظة © 2024 منصة المؤثرين السعودية</p>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  )
}

export default PrivacyPage
