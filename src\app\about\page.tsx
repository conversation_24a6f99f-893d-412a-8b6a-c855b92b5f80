'use client'

import React from 'react'
import Link from 'next/link'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import {
  Target,
  Eye,
  Heart,
  Users,
  TrendingUp,
  Shield,
  Award,
  Globe,
  CheckCircle,
  Star
} from 'lucide-react'

const AboutPage: React.FC = () => {
  const values = [
    {
      icon: Shield,
      title: 'الثقة والأمان',
      description: 'نضع الثقة والأمان في المقدمة، مع نظام ضمان الأموال وحماية البيانات'
    },
    {
      icon: Users,
      title: 'المجتمع أولاً',
      description: 'نؤمن ببناء مجتمع قوي من المؤثرين والتجار يدعم بعضهم البعض'
    },
    {
      icon: TrendingUp,
      title: 'النمو المستدام',
      description: 'نساعد شركاءنا على تحقيق نمو مستدام وطويل الأمد'
    },
    {
      icon: Heart,
      title: 'الشغف بالتميز',
      description: 'نسعى دائماً لتقديم أفضل خدمة وتجربة مستخدم استثنائية'
    }
  ]

  const stats = [
    { number: '10,000+', label: 'مؤثر نشط', icon: Users },
    { number: '5,000+', label: 'تاجر راضي', icon: TrendingUp },
    { number: '50,000+', label: 'حملة ناجحة', icon: Award },
    { number: '95%', label: 'معدل الرضا', icon: Star }
  ]

  const timeline = [
    {
      year: '2022',
      title: 'البداية',
      description: 'تأسيس المنصة برؤية واضحة لربط المؤثرين بالتجار في السعودية'
    },
    {
      year: '2023',
      title: 'النمو السريع',
      description: 'وصلنا إلى 1000 مؤثر و500 تاجر مع إطلاق نظام الضمان'
    },
    {
      year: '2024',
      title: 'التوسع',
      description: 'أصبحنا المنصة الرائدة مع أكثر من 10,000 مؤثر و5,000 تاجر'
    },
    {
      year: 'المستقبل',
      title: 'الرؤية',
      description: 'التوسع لتغطية منطقة الخليج وإضافة تقنيات الذكاء الاصطناعي'
    }
  ]

  const team = [
    {
      name: 'أحمد محمد',
      position: 'الرئيس التنفيذي',
      bio: 'خبرة 15 سنة في التقنية والتسويق الرقمي',
      image: '/api/placeholder/150/150'
    },
    {
      name: 'فاطمة العلي',
      position: 'مديرة التطوير',
      bio: 'متخصصة في تطوير المنصات الرقمية',
      image: '/api/placeholder/150/150'
    },
    {
      name: 'خالد السعد',
      position: 'مدير التسويق',
      bio: 'خبير في التسويق بالمحتوى ووسائل التواصل',
      image: '/api/placeholder/150/150'
    },
    {
      name: 'نورا أحمد',
      position: 'مديرة خدمة العملاء',
      bio: 'متخصصة في تجربة المستخدم وخدمة العملاء',
      image: '/api/placeholder/150/150'
    }
  ]

  return (
    <div className="min-h-screen">
      <Header />

      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-r from-indigo-600 via-purple-600 to-indigo-800 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-6 text-shadow">
            من نحن
          </h1>
          <p className="text-xl mb-8 text-slate-100 leading-relaxed">
            نحن منصة سعودية رائدة تهدف إلى ربط المؤثرين بالتجار وبناء جسر الثقة بينهم
            لتحقيق النجاح المشترك في عالم التسويق الرقمي
          </p>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="py-20 bg-slate-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <Card className="text-center bg-slate-800/90 border-slate-700">
              <div className="w-16 h-16 bg-indigo-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <Target className="h-8 w-8 text-white" />
              </div>
              <h2 className="text-2xl font-bold text-white mb-4">رسالتنا</h2>
              <p className="text-slate-200 leading-relaxed">
                تمكين المؤثرين والتجار في المملكة العربية السعودية من خلال منصة آمنة وموثوقة
                تسهل التعاون وتضمن حقوق جميع الأطراف، مع تقديم أدوات متطورة لقياس النجاح
                وتحقيق أهداف التسويق الرقمي.
              </p>
            </Card>

            <Card className="text-center bg-slate-800/90 border-slate-700">
              <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <Eye className="h-8 w-8 text-white" />
              </div>
              <h2 className="text-2xl font-bold text-white mb-4">رؤيتنا</h2>
              <p className="text-slate-200 leading-relaxed">
                أن نكون المنصة الأولى والأكثر ثقة في منطقة الشرق الأوسط لربط المؤثرين
                بالعلامات التجارية، ونساهم في نمو اقتصاد المحتوى الرقمي وتطوير مهارات
                المؤثرين والتجار على حد سواء.
              </p>
            </Card>
          </div>
        </div>
      </section>

      {/* Values */}
      <section className="py-20 bg-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4 text-shadow">
              قيمنا الأساسية
            </h2>
            <p className="text-xl text-slate-300">
              القيم التي توجه عملنا وتحدد هويتنا
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => {
              const Icon = value.icon
              return (
                <Card key={index} hover className="text-center bg-slate-700/90 border-slate-600">
                  <div className="w-16 h-16 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                    <Icon className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-3">
                    {value.title}
                  </h3>
                  <p className="text-slate-200 leading-relaxed">
                    {value.description}
                  </p>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      {/* Stats */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              إنجازاتنا بالأرقام
            </h2>
            <p className="text-xl text-gray-600">
              أرقام تعكس نجاحنا ونمونا المستمر
            </p>
          </div>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => {
              const Icon = stat.icon
              return (
                <Card key={index} className="text-center">
                  <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Icon className="h-6 w-6 text-primary-600" />
                  </div>
                  <div className="text-3xl md:text-4xl font-bold text-primary-600 mb-2">
                    {stat.number}
                  </div>
                  <div className="text-gray-600 font-medium">
                    {stat.label}
                  </div>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      {/* Timeline */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              رحلتنا عبر الزمن
            </h2>
            <p className="text-xl text-gray-600">
              من البداية المتواضعة إلى الريادة في السوق
            </p>
          </div>

          <div className="space-y-8">
            {timeline.map((item, index) => (
              <div key={index} className="flex items-start">
                <div className="flex-shrink-0 w-24 text-center">
                  <div className="w-12 h-12 bg-primary-600 text-white rounded-full flex items-center justify-center font-bold text-sm mx-auto mb-2">
                    {item.year}
                  </div>
                  {index < timeline.length - 1 && (
                    <div className="w-0.5 h-16 bg-gray-300 mx-auto"></div>
                  )}
                </div>
                <Card className="flex-1 mr-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-2">
                    {item.title}
                  </h3>
                  <p className="text-gray-700">
                    {item.description}
                  </p>
                </Card>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Team */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              فريق العمل
            </h2>
            <p className="text-xl text-gray-600">
              الأشخاص المبدعون وراء نجاح المنصة
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {team.map((member, index) => (
              <Card key={index} hover className="text-center">
                <div className="w-24 h-24 bg-gradient-to-br from-purple-400 to-pink-400 rounded-full mx-auto mb-4 flex items-center justify-center text-white font-bold text-2xl">
                  {member.name.charAt(0)}
                </div>
                <h3 className="text-lg font-bold text-gray-900 mb-1">
                  {member.name}
                </h3>
                <p className="text-primary-600 font-medium mb-3">
                  {member.position}
                </p>
                <p className="text-gray-600 text-sm">
                  {member.bio}
                </p>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              لماذا تختارنا؟
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card>
              <CheckCircle className="h-8 w-8 text-green-500 mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                منصة سعودية 100%
              </h3>
              <p className="text-gray-600">
                نفهم السوق السعودي والثقافة المحلية بعمق
              </p>
            </Card>

            <Card>
              <Shield className="h-8 w-8 text-blue-500 mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                أمان وثقة
              </h3>
              <p className="text-gray-600">
                نظام ضمان الأموال وحماية البيانات بأعلى المعايير
              </p>
            </Card>

            <Card>
              <Users className="h-8 w-8 text-purple-500 mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                مجتمع نشط
              </h3>
              <p className="text-gray-600">
                آلاف المؤثرين والتجار النشطين والمتفاعلين
              </p>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-indigo-600 via-purple-600 to-indigo-800 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-shadow">
            انضم إلى قصة نجاحنا
          </h2>
          <p className="text-xl mb-8 text-slate-100">
            كن جزءاً من المنصة الرائدة في التسويق بالمحتوى في السعودية
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/register/influencer">
              <Button size="xl" variant="secondary" className="w-full sm:w-auto bg-emerald-600 hover:bg-emerald-700 text-white border-emerald-600">
                انضم كمؤثر
              </Button>
            </Link>
            <Link href="/register/merchant">
              <Button size="xl" className="w-full sm:w-auto bg-white text-indigo-700 hover:bg-slate-100 border-white font-bold">
                انضم كتاجر
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}

export default AboutPage
