import type { Metadata } from 'next'
import './globals.css'

export const metadata: Metadata = {
  title: 'منصة المؤثرين السعودية - ربط المؤثرين بالتجار',
  description: 'منصة احترافية لربط المؤثرين بالتجار في المملكة العربية السعودية مع ضمان الأموال ونظام الدفع الآمن',
  keywords: 'مؤثرين, تجار, السعودية, تسويق, إعلانات, سوشيال ميديا',
  authors: [{ name: 'منصة المؤثرين السعودية' }],
  viewport: 'width=device-width, initial-scale=1',
  robots: 'index, follow',
  openGraph: {
    title: 'منصة المؤثرين السعودية',
    description: 'منصة احترافية لربط المؤثرين بالتجار في المملكة العربية السعودية',
    type: 'website',
    locale: 'ar_SA',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="ar" dir="rtl">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="icon" href="/favicon.ico" />
      </head>
      <body className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 font-arabic">
        <div id="root">
          {children}
        </div>
      </body>
    </html>
  )
}
