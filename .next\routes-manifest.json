{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/influencers/[id]", "regex": "^/influencers/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/influencers/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/projects/[id]/review", "regex": "^/projects/([^/]+?)/review(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/projects/(?<nxtPid>[^/]+?)/review(?:/)?$"}, {"page": "/ugc-creators/[id]", "regex": "^/ugc\\-creators/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/ugc\\-creators/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/ugc-creators/[id]/order", "regex": "^/ugc\\-creators/([^/]+?)/order(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/ugc\\-creators/(?<nxtPid>[^/]+?)/order(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/about", "regex": "^/about(?:/)?$", "routeKeys": {}, "namedRegex": "^/about(?:/)?$"}, {"page": "/agreement", "regex": "^/agreement(?:/)?$", "routeKeys": {}, "namedRegex": "^/agreement(?:/)?$"}, {"page": "/campaigns/create", "regex": "^/campaigns/create(?:/)?$", "routeKeys": {}, "namedRegex": "^/campaigns/create(?:/)?$"}, {"page": "/careers", "regex": "^/careers(?:/)?$", "routeKeys": {}, "namedRegex": "^/careers(?:/)?$"}, {"page": "/contact", "regex": "^/contact(?:/)?$", "routeKeys": {}, "namedRegex": "^/contact(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/dashboard/influencer", "regex": "^/dashboard/influencer(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/influencer(?:/)?$"}, {"page": "/dashboard/merchant", "regex": "^/dashboard/merchant(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/merchant(?:/)?$"}, {"page": "/dashboard/ugc-creator", "regex": "^/dashboard/ugc\\-creator(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/ugc\\-creator(?:/)?$"}, {"page": "/dashboard/ugc-creator/pricing", "regex": "^/dashboard/ugc\\-creator/pricing(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/ugc\\-creator/pricing(?:/)?$"}, {"page": "/faq", "regex": "^/faq(?:/)?$", "routeKeys": {}, "namedRegex": "^/faq(?:/)?$"}, {"page": "/forgot-password", "regex": "^/forgot\\-password(?:/)?$", "routeKeys": {}, "namedRegex": "^/forgot\\-password(?:/)?$"}, {"page": "/help", "regex": "^/help(?:/)?$", "routeKeys": {}, "namedRegex": "^/help(?:/)?$"}, {"page": "/how-it-works", "regex": "^/how\\-it\\-works(?:/)?$", "routeKeys": {}, "namedRegex": "^/how\\-it\\-works(?:/)?$"}, {"page": "/influencers", "regex": "^/influencers(?:/)?$", "routeKeys": {}, "namedRegex": "^/influencers(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/merchants", "regex": "^/merchants(?:/)?$", "routeKeys": {}, "namedRegex": "^/merchants(?:/)?$"}, {"page": "/messages", "regex": "^/messages(?:/)?$", "routeKeys": {}, "namedRegex": "^/messages(?:/)?$"}, {"page": "/news", "regex": "^/news(?:/)?$", "routeKeys": {}, "namedRegex": "^/news(?:/)?$"}, {"page": "/pricing", "regex": "^/pricing(?:/)?$", "routeKeys": {}, "namedRegex": "^/pricing(?:/)?$"}, {"page": "/privacy", "regex": "^/privacy(?:/)?$", "routeKeys": {}, "namedRegex": "^/privacy(?:/)?$"}, {"page": "/refund", "regex": "^/refund(?:/)?$", "routeKeys": {}, "namedRegex": "^/refund(?:/)?$"}, {"page": "/register", "regex": "^/register(?:/)?$", "routeKeys": {}, "namedRegex": "^/register(?:/)?$"}, {"page": "/register/influencer", "regex": "^/register/influencer(?:/)?$", "routeKeys": {}, "namedRegex": "^/register/influencer(?:/)?$"}, {"page": "/register/merchant", "regex": "^/register/merchant(?:/)?$", "routeKeys": {}, "namedRegex": "^/register/merchant(?:/)?$"}, {"page": "/register/ugc-creator", "regex": "^/register/ugc\\-creator(?:/)?$", "routeKeys": {}, "namedRegex": "^/register/ugc\\-creator(?:/)?$"}, {"page": "/reports", "regex": "^/reports(?:/)?$", "routeKeys": {}, "namedRegex": "^/reports(?:/)?$"}, {"page": "/support", "regex": "^/support(?:/)?$", "routeKeys": {}, "namedRegex": "^/support(?:/)?$"}, {"page": "/team", "regex": "^/team(?:/)?$", "routeKeys": {}, "namedRegex": "^/team(?:/)?$"}, {"page": "/terms", "regex": "^/terms(?:/)?$", "routeKeys": {}, "namedRegex": "^/terms(?:/)?$"}, {"page": "/ugc-creators", "regex": "^/ugc\\-creators(?:/)?$", "routeKeys": {}, "namedRegex": "^/ugc\\-creators(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Url", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}