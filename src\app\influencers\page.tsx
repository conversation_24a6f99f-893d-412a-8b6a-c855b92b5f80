'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import MobileNavigation from '@/components/layout/MobileNavigation'
import FloatingActionButton from '@/components/ui/FloatingActionButton'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import {
  Search,
  Filter,
  MapPin,
  Users,
  Star,
  Instagram,
  Youtube,
  Twitter,
  Eye,
  Heart,
  MessageCircle
} from 'lucide-react'

const InfluencersPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCity, setSelectedCity] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('')
  const [selectedFollowerRange, setSelectedFollowerRange] = useState('')
  const [selectedPriceRange, setSelectedPriceRange] = useState('')

  // Mock data for influencers
  const influencers = [
    {
      id: 1,
      name: 'سارة أحمد',
      username: '@sarah_ahmed',
      category: 'الموضة والجمال',
      city: 'الرياض',
      bio: 'مؤثرة في مجال الموضة والجمال، أشارك أحدث صيحات الموضة ونصائح الجمال',
      avatar: '/api/placeholder/150/150',
      verified: true,
      rating: 4.9,
      followers: {
        instagram: 125000,
        youtube: 45000,
        twitter: 32000,
        tiktok: 89000,
        snapchat: 67000
      },
      pricing: {
        story: 150,
        post: 300,
        reel: 450,
        video: 800,
        visit: 1200
      },
      engagement: 8.5,
      completedCampaigns: 156
    },
    {
      id: 2,
      name: 'محمد العلي',
      username: '@mohammed_ali',
      category: 'التقنية',
      city: 'جدة',
      bio: 'متخصص في مراجعة التقنية والهواتف الذكية، أقدم محتوى تقني مفيد ومبسط',
      avatar: '/api/placeholder/150/150',
      verified: true,
      rating: 4.8,
      followers: {
        instagram: 89000,
        youtube: 156000,
        twitter: 78000,
        tiktok: 45000,
        snapchat: 23000
      },
      pricing: {
        story: 120,
        post: 250,
        reel: 380,
        video: 650,
        visit: 1000
      },
      engagement: 9.2,
      completedCampaigns: 203
    },
    {
      id: 3,
      name: 'نورا السعد',
      username: '@nora_alsaad',
      category: 'الطعام والمطاعم',
      city: 'الدمام',
      bio: 'عاشقة للطبخ والمطاعم، أستكشف أفضل المطاعم وأشارك وصفات لذيذة',
      avatar: '/api/placeholder/150/150',
      verified: true,
      rating: 4.7,
      followers: {
        instagram: 67000,
        youtube: 34000,
        twitter: 12000,
        tiktok: 78000,
        snapchat: 45000
      },
      pricing: {
        story: 100,
        post: 200,
        reel: 320,
        video: 550,
        visit: 900
      },
      engagement: 7.8,
      completedCampaigns: 89
    },
    {
      id: 4,
      name: 'خالد الشمري',
      username: '@khalid_sports',
      category: 'الرياضة واللياقة',
      city: 'الرياض',
      bio: 'مدرب لياقة بدنية معتمد، أشارك تمارين ونصائح للحصول على جسم صحي',
      avatar: '/api/placeholder/150/150',
      verified: true,
      rating: 4.9,
      followers: {
        instagram: 98000,
        youtube: 67000,
        twitter: 23000,
        tiktok: 134000,
        snapchat: 56000
      },
      pricing: {
        story: 130,
        post: 280,
        reel: 420,
        video: 700,
        visit: 1100
      },
      engagement: 8.9,
      completedCampaigns: 178
    }
  ]

  const cities = ['الرياض', 'جدة', 'الدمام', 'مكة المكرمة', 'المدينة المنورة', 'الطائف']
  const categories = ['الموضة والجمال', 'التقنية', 'الطعام والمطاعم', 'الرياضة واللياقة', 'السفر والسياحة', 'التعليم']
  const followerRanges = ['أقل من 10K', '10K - 50K', '50K - 100K', '100K - 500K', 'أكثر من 500K']
  const priceRanges = ['أقل من 100 ريال', '100 - 300 ريال', '300 - 500 ريال', '500 - 1000 ريال', 'أكثر من 1000 ريال']

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K'
    }
    return num.toString()
  }

  const filteredInfluencers = influencers.filter(influencer => {
    const matchesSearch = influencer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         influencer.username.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCity = !selectedCity || influencer.city === selectedCity
    const matchesCategory = !selectedCategory || influencer.category === selectedCategory

    return matchesSearch && matchesCity && matchesCategory
  })

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
      <Header />

      <div className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              اكتشف أفضل المؤثرين
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              تصفح قائمة المؤثرين المعتمدين واختر الأنسب لحملتك الإعلانية
            </p>
          </div>

          {/* Search and Filters */}
          <Card className="mb-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              <div className="lg:col-span-2">
                <Input
                  placeholder="ابحث عن مؤثر..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  leftIcon={<Search className="h-5 w-5" />}
                />
              </div>

              <select
                value={selectedCity}
                onChange={(e) => setSelectedCity(e.target.value)}
                className="px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="">جميع المدن</option>
                {cities.map(city => (
                  <option key={city} value={city}>{city}</option>
                ))}
              </select>

              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="">جميع التخصصات</option>
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>

              <Button variant="outline" className="flex items-center justify-center">
                <Filter className="h-4 w-4 ml-2" />
                فلاتر متقدمة
              </Button>
            </div>
          </Card>

          {/* Results Count */}
          <div className="mb-6">
            <p className="text-gray-600">
              تم العثور على <span className="font-semibold text-primary-600">{filteredInfluencers.length}</span> مؤثر
            </p>
          </div>

          {/* Influencers Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredInfluencers.map((influencer) => (
              <Card key={influencer.id} hover className="overflow-hidden">
                {/* Header */}
                <div className="flex items-center mb-4">
                  <div className="w-16 h-16 bg-gray-200 rounded-full overflow-hidden ml-4">
                    <div className="w-full h-full bg-gradient-to-br from-purple-400 to-pink-400 flex items-center justify-center text-white font-bold text-xl">
                      {influencer.name.charAt(0)}
                    </div>
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center">
                      <h3 className="text-lg font-semibold text-gray-900">{influencer.name}</h3>
                      {influencer.verified && (
                        <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center mr-2">
                          <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                      )}
                    </div>
                    <p className="text-sm text-gray-500">{influencer.username}</p>
                    <div className="flex items-center mt-1">
                      <Star className="h-4 w-4 text-yellow-400 fill-current" />
                      <span className="text-sm text-gray-600 mr-1">{influencer.rating}</span>
                      <span className="text-xs text-gray-500">({influencer.completedCampaigns} حملة)</span>
                    </div>
                  </div>
                </div>

                {/* Category and Location */}
                <div className="flex items-center justify-between mb-4">
                  <span className="px-3 py-1 bg-primary-100 text-primary-800 text-sm rounded-full">
                    {influencer.category}
                  </span>
                  <div className="flex items-center text-gray-500 text-sm">
                    <MapPin className="h-4 w-4 ml-1" />
                    {influencer.city}
                  </div>
                </div>

                {/* Bio */}
                <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                  {influencer.bio}
                </p>

                {/* Social Media Stats */}
                <div className="grid grid-cols-2 gap-3 mb-4">
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center">
                      <Instagram className="h-4 w-4 text-pink-500 ml-1" />
                      <span className="text-gray-600">Instagram</span>
                    </div>
                    <span className="font-semibold">{formatNumber(influencer.followers.instagram)}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center">
                      <Youtube className="h-4 w-4 text-red-500 ml-1" />
                      <span className="text-gray-600">YouTube</span>
                    </div>
                    <span className="font-semibold">{formatNumber(influencer.followers.youtube)}</span>
                  </div>
                </div>

                {/* Pricing */}
                <div className="border-t pt-4 mb-4">
                  <div className="flex items-center justify-between text-sm mb-2">
                    <span className="text-gray-600">ستوري</span>
                    <span className="font-semibold text-primary-600">{influencer.pricing.story} ريال</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">منشور</span>
                    <span className="font-semibold text-primary-600">{influencer.pricing.post} ريال</span>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex gap-2">
                  <Link href={`/influencers/${influencer.id}`} className="flex-1">
                    <Button variant="outline" size="sm" className="w-full">
                      <Eye className="h-4 w-4 ml-2" />
                      عرض الملف
                    </Button>
                  </Link>
                  <Button size="sm" className="flex-1">
                    <MessageCircle className="h-4 w-4 ml-2" />
                    تواصل
                  </Button>
                </div>
              </Card>
            ))}
          </div>

          {/* Load More */}
          <div className="text-center mt-12">
            <Button variant="outline" size="lg">
              عرض المزيد من المؤثرين
            </Button>
          </div>
        </div>
      </div>

      <Footer />

      {/* مكونات الجوال */}
      <MobileNavigation />
      <FloatingActionButton variant="merchant" />
    </div>
  )
}

export default InfluencersPage
