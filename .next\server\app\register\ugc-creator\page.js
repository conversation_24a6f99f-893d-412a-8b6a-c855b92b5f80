(()=>{var e={};e.id=965,e.ids=[965],e.modules={2643:(e,s,r)=>{"use strict";r.d(s,{A:()=>i});var t=r(60687);r(43210);var l=r(49384);let i=({variant:e="primary",size:s="md",isLoading:r=!1,fullWidth:i=!1,children:a,className:d,disabled:n,...o})=>(0,t.jsxs)("button",{className:(0,l.$)("transition-all duration-200 ease-in-out","focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-slate-900","disabled:opacity-50 disabled:cursor-not-allowed","font-medium rounded-lg border",{primary:"btn-primary",secondary:"btn-secondary",outline:"btn-outline",ghost:"bg-transparent text-slate-300 hover:text-white hover:bg-slate-700/50 border border-slate-600 hover:border-slate-500",danger:"bg-red-600 hover:bg-red-700 text-white border-red-600 shadow-md hover:shadow-lg",success:"bg-emerald-600 hover:bg-emerald-700 text-white border-emerald-600 shadow-md hover:shadow-lg",warning:"bg-amber-600 hover:bg-amber-700 text-white border-amber-600 shadow-md hover:shadow-lg"}[e],{sm:"text-sm px-3 py-2 min-h-[40px]",md:"text-sm px-4 py-3 min-h-[44px]",lg:"text-base px-6 py-3 min-h-[48px]",xl:"text-lg px-8 py-4 min-h-[52px]"}[s],i&&"w-full",n&&"opacity-50 cursor-not-allowed",d),disabled:n||r,...o,children:[r&&(0,t.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),a]})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10084:(e,s,r)=>{"use strict";r.d(s,{A:()=>m});var t=r(60687);r(43210);var l=r(85814),i=r.n(l),a=r(16189);let d=(0,r(18962).A)("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]]);var n=r(41312),o=r(99270),c=r(33872),x=r(58869);let m=()=>{let e=(0,a.usePathname)(),s=[{name:"الرئيسية",href:"/",icon:d,color:"text-green-600"},{name:"المؤثرين",href:"/influencers",icon:n.A,color:"text-blue-600"},{name:"البحث",href:"/search",icon:o.A,color:"text-purple-600"},{name:"الرسائل",href:"/messages",icon:c.A,color:"text-orange-600"},{name:"حسابي",href:"/profile",icon:x.A,color:"text-gray-600"}],r=s=>"/"===s?"/"===e:e.startsWith(s);return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"h-20 md:hidden"}),(0,t.jsx)("nav",{className:"md:hidden fixed bottom-0 left-0 right-0 bg-slate-900/95 backdrop-blur-md border-t border-slate-700/50 z-50",children:(0,t.jsx)("div",{className:"flex items-center justify-around py-2",children:s.map(e=>{let s=e.icon,l=r(e.href);return(0,t.jsx)(i(),{href:e.href,className:"flex-1",children:(0,t.jsxs)("div",{className:"flex flex-col items-center justify-center py-3",children:[(0,t.jsx)("div",{className:`p-2 rounded-lg transition-colors ${l?"bg-indigo-500/10":"hover:bg-slate-800"}`,children:(0,t.jsx)(s,{className:`h-5 w-5 transition-colors ${l?"text-indigo-400":"text-slate-400"}`})}),(0,t.jsx)("span",{className:`text-xs font-medium mt-1 transition-colors ${l?"text-indigo-400":"text-slate-400"}`,children:e.name})]})},e.name)})})})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},18752:(e,s,r)=>{Promise.resolve().then(r.bind(r,69356))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23689:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(18962).A)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},28559:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(18962).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33872:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(18962).A)("MessageCircle",[["path",{d:"m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z",key:"v2veuj"}]])},33873:e=>{"use strict";e.exports=require("path")},41312:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(18962).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},47049:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\register\\\\ugc-creator\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\register\\ugc-creator\\page.tsx","default")},51907:(e,s,r)=>{"use strict";r.d(s,{A:()=>i});var t=r(60687);r(43210);var l=r(49384);let i=({label:e,error:s,helperText:r,leftIcon:i,rightIcon:a,className:d,id:n,...o})=>{let c=n||`input-${Math.random().toString(36).substr(2,9)}`,x=s?"border-red-500 focus:border-red-500 focus:ring-red-500/20":"focus:border-indigo-500 focus:ring-indigo-500/20",m=i?"pr-12":a?"pl-12":"";return(0,t.jsxs)("div",{className:"w-full",children:[e&&(0,t.jsx)("label",{htmlFor:c,className:"block text-sm font-medium text-slate-200 mb-2",children:e}),(0,t.jsxs)("div",{className:"relative",children:[i&&(0,t.jsx)("div",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400",children:i}),(0,t.jsx)("input",{id:c,className:(0,l.$)("input",x,m,"bg-slate-800/90 border-slate-600 text-slate-100 placeholder-slate-400","focus:bg-slate-800 focus:ring-2 focus:ring-offset-0","transition-all duration-200",d),...o}),a&&(0,t.jsx)("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400",children:a})]}),s&&(0,t.jsx)("p",{className:"mt-2 text-sm text-red-300 font-medium",children:s}),r&&!s&&(0,t.jsx)("p",{className:"mt-2 text-sm text-slate-300",children:r})]})}},58869:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(18962).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69356:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>g});var t=r(60687),l=r(43210);let i=(0,r(18962).A)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]);var a=r(99891),d=r(64398),n=r(70334),o=r(23689),c=r(28559),x=r(24528),m=r(89683),h=r(10084),u=r(28749),p=r(2643),b=r(51907);let g=()=>{let[e,s]=(0,l.useState)(1),[r,g]=(0,l.useState)({fullName:"",email:"",phone:"",city:"",age:"",contentTypes:[],categories:[],customService:"",pricing:{},videoDurations:[],editingIncluded:!1,portfolioLinks:[],portfolioFiles:[],deliveryTime:"",experience:"",equipment:"",languages:[],availability:""}),v=[{id:"tiktok",label:"فيديو تيك توك",icon:"\uD83C\uDFB5",color:"from-pink-500 to-red-500"},{id:"product-photo",label:"تصوير منتج",icon:"\uD83D\uDCF8",color:"from-blue-500 to-cyan-500"},{id:"reviews",label:"مراجعات",icon:"⭐",color:"from-yellow-500 to-orange-500"},{id:"snapchat",label:"فيديو سناب شات",icon:"\uD83D\uDC7B",color:"from-yellow-400 to-yellow-600"},{id:"instagram-stories",label:"ستوريز انستقرام",icon:"\uD83D\uDCF1",color:"from-purple-500 to-pink-500"},{id:"video-editing",label:"مونتاج فيديو",icon:"✂️",color:"from-green-500 to-teal-500"},{id:"graphic-design",label:"تصميم صورة",icon:"\uD83C\uDFA8",color:"from-indigo-500 to-purple-500"},{id:"food-photography",label:"تصوير أكل",icon:"\uD83C\uDF7D️",color:"from-orange-500 to-red-500"},{id:"brand-design",label:"تصاميم براند",icon:"\uD83C\uDFF7️",color:"from-teal-500 to-blue-500"},{id:"custom",label:"خدمة مخصصة",icon:"⚡",color:"from-gray-500 to-gray-700"}],j=[{id:"beauty",label:"جمال",icon:"\uD83D\uDC84"},{id:"cars",label:"سيارات",icon:"\uD83D\uDE97"},{id:"restaurants",label:"مطاعم",icon:"\uD83C\uDF55"},{id:"food",label:"أكل",icon:"\uD83C\uDF54"},{id:"fashion",label:"موضة",icon:"\uD83D\uDC57"},{id:"tech",label:"تقنية",icon:"\uD83D\uDCF1"},{id:"travel",label:"سفر",icon:"✈️"},{id:"fitness",label:"رياضة",icon:"\uD83D\uDCAA"},{id:"home",label:"منزل",icon:"\uD83C\uDFE0"},{id:"kids",label:"أطفال",icon:"\uD83E\uDDF8"}],f=[{id:"10s",label:"10 ثواني",price:100},{id:"30s",label:"30 ثانية",price:200},{id:"60s",label:"دقيقة واحدة",price:350},{id:"120s",label:"دقيقتان",price:500}],y=[{id:"24h",label:"24 ساعة",icon:"⚡"},{id:"48h",label:"48 ساعة",icon:"\uD83D\uDE80"},{id:"3d",label:"3 أيام",icon:"\uD83D\uDCC5"},{id:"7d",label:"أسبوع",icon:"\uD83D\uDCC6"},{id:"14d",label:"أسبوعين",icon:"\uD83D\uDDD3️"}],N=(e,s)=>{g(r=>({...r,[e]:s}))},w=(e,s)=>{g(r=>{let t=r[e],l=Array.isArray(t)&&t.includes(s);return{...r,[e]:l?t.filter(e=>e!==s):[...t||[],s]}})};return(0,t.jsxs)("div",{className:"min-h-screen bg-slate-900",children:[(0,t.jsx)(x.A,{}),(0,t.jsx)("div",{className:"py-12",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-white",children:"انضم كمبدع UGC"}),(0,t.jsxs)("div",{className:"text-gray-400",children:["الخطوة ",e," من 5"]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-700 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full transition-all duration-500",style:{width:`${e/5*100}%`}})})]}),(0,t.jsx)(u.A,{children:(()=>{switch(e){case 1:return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("div",{className:"text-6xl mb-4",children:"\uD83C\uDFAC"}),(0,t.jsx)("h2",{className:"text-2xl font-bold text-white mb-2",children:"المعلومات الشخصية"}),(0,t.jsx)("p",{className:"text-gray-300",children:"أخبرنا عن نفسك لنبدأ رحلة الإبداع معاً"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsx)(b.A,{label:"الاسم الكامل",placeholder:"اسمك الكامل",value:r.fullName,onChange:e=>N("fullName",e.target.value),required:!0}),(0,t.jsx)(b.A,{label:"البريد الإلكتروني",type:"email",placeholder:"<EMAIL>",value:r.email,onChange:e=>N("email",e.target.value),required:!0}),(0,t.jsx)(b.A,{label:"رقم الجوال",placeholder:"05xxxxxxxx",value:r.phone,onChange:e=>N("phone",e.target.value),required:!0}),(0,t.jsx)(b.A,{label:"المدينة",placeholder:"الرياض، جدة، الدمام...",value:r.city,onChange:e=>N("city",e.target.value),required:!0}),(0,t.jsx)(b.A,{label:"العمر",type:"number",placeholder:"25",value:r.age,onChange:e=>N("age",e.target.value),required:!0})]})]});case 2:return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("div",{className:"text-6xl mb-4",children:"\uD83C\uDFA8"}),(0,t.jsx)("h2",{className:"text-2xl font-bold text-white mb-2",children:"تخصصك وخدماتك"}),(0,t.jsx)("p",{className:"text-gray-300",children:"اختر أنواع المحتوى التي تبدع فيها"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"أنواع المحتوى المتخصص فيها:"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:v.map(e=>(0,t.jsx)("div",{onClick:()=>w("contentTypes",e.id),className:`p-4 rounded-lg border cursor-pointer transition-colors ${r.contentTypes.includes(e.id)?"border-indigo-500 bg-indigo-500/10":"border-slate-600 bg-slate-800/50 hover:border-slate-500"}`,children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-3xl mb-2",children:e.icon}),(0,t.jsx)("div",{className:"text-white font-medium",children:e.label})]})},e.id))})]}),r.contentTypes.includes("custom")&&(0,t.jsx)("div",{children:(0,t.jsx)(b.A,{label:"اكتب نوع الخدمة المخصصة",placeholder:"مثال: تصوير فيديوهات تعليمية، إنتاج محتوى تسويقي...",value:r.customService,onChange:e=>N("customService",e.target.value)})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"الفئات المتخصص فيها:"}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-3",children:j.map(e=>(0,t.jsxs)("div",{onClick:()=>w("categories",e.id),className:`p-3 rounded-lg border cursor-pointer text-center transition-colors ${r.categories.includes(e.id)?"border-emerald-500 bg-emerald-500/10":"border-slate-600 bg-slate-800/50 hover:border-slate-500"}`,children:[(0,t.jsx)("div",{className:"text-2xl mb-1",children:e.icon}),(0,t.jsx)("div",{className:"text-white text-sm",children:e.label})]},e.id))})]})]});case 3:return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDCB0"}),(0,t.jsx)("h2",{className:"text-2xl font-bold text-white mb-2",children:"الأسعار والمدة"}),(0,t.jsx)("p",{className:"text-gray-300",children:"حدد أسعارك ومدة الفيديوهات المتاحة"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"مدة الفيديوهات المتاحة مع الأسعار:"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:f.map(e=>(0,t.jsxs)(u.A,{children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-white font-semibold",children:e.label}),(0,t.jsxs)("div",{className:"text-gray-400 text-sm",children:["السعر المقترح: ",e.price," ريال"]})]}),(0,t.jsx)("input",{type:"checkbox",checked:r.videoDurations.includes(e.id),onChange:()=>w("videoDurations",e.id),className:"w-5 h-5 text-purple-600 rounded focus:ring-purple-500"})]}),r.videoDurations.includes(e.id)&&(0,t.jsx)(b.A,{label:"سعرك (ريال)",type:"number",placeholder:e.price.toString(),value:r.pricing[e.id]||"",onChange:s=>N("pricing",{...r.pricing,[e.id]:parseInt(s.target.value)})})]},e.id))})]}),(0,t.jsx)(u.A,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-white font-semibold mb-2",children:"هل تشمل خدماتك المونتاج والتعديل؟"}),(0,t.jsx)("div",{className:"text-gray-400 text-sm",children:"يمكن إضافة رسوم إضافية للمونتاج المتقدم"})]}),(0,t.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,t.jsx)("input",{type:"checkbox",checked:r.editingIncluded,onChange:e=>N("editingIncluded",e.target.checked),className:"sr-only peer"}),(0,t.jsx)("div",{className:"w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"})]})]})})]});case 4:return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("div",{className:"text-6xl mb-4",children:"\uD83C\uDFAF"}),(0,t.jsx)("h2",{className:"text-2xl font-bold text-white mb-2",children:"معرض أعمالك"}),(0,t.jsx)("p",{className:"text-gray-300",children:"أضف أمثلة من أعمالك السابقة لتعرض مهاراتك"})]}),(0,t.jsxs)(u.A,{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"روابط أعمالك (يوتيوب، انستقرام، تيك توك):"}),(0,t.jsx)("div",{className:"space-y-3",children:[0,1,2].map(e=>(0,t.jsx)(b.A,{placeholder:`رابط العمل ${e+1} (اختياري)`,value:r.portfolioLinks[e]||"",onChange:s=>{let t=[...r.portfolioLinks];t[e]=s.target.value,N("portfolioLinks",t)}},e))})]}),(0,t.jsxs)(u.A,{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"رفع ملفات فيديو (حد أقصى 3 ملفات):"}),(0,t.jsxs)("div",{className:"border-2 border-dashed border-purple-500/50 rounded-2xl p-8 text-center hover:border-purple-500 transition-colors",children:[(0,t.jsx)(i,{className:"h-12 w-12 text-purple-400 mx-auto mb-4"}),(0,t.jsx)("div",{className:"text-white mb-2",children:"اسحب الملفات هنا أو اضغط للاختيار"}),(0,t.jsx)("div",{className:"text-gray-400 text-sm",children:"MP4, MOV, AVI (حد أقصى 50MB لكل ملف)"}),(0,t.jsx)("input",{type:"file",multiple:!0,accept:"video/*",className:"hidden",onChange:e=>{e.target.files&&N("portfolioFiles",Array.from(e.target.files).slice(0,3))}})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"وقت التسليم المعتاد:"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:y.map(e=>(0,t.jsxs)("div",{onClick:()=>N("deliveryTime",e.id),className:`p-4 rounded-lg border cursor-pointer text-center transition-colors ${r.deliveryTime===e.id?"border-amber-500 bg-amber-500/10":"border-slate-600 bg-slate-800/50 hover:border-slate-500"}`,children:[(0,t.jsx)("div",{className:"text-3xl mb-2",children:e.icon}),(0,t.jsx)("div",{className:"text-white font-medium",children:e.label})]},e.id))})]})]});case 5:return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("div",{className:"text-6xl mb-4",children:"✨"}),(0,t.jsx)("h2",{className:"text-2xl font-bold text-white mb-2",children:"اللمسة الأخيرة"}),(0,t.jsx)("p",{className:"text-gray-300",children:"معلومات إضافية لإكمال ملفك الشخصي"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"سنوات الخبرة"}),(0,t.jsxs)("select",{value:r.experience,onChange:e=>N("experience",e.target.value),className:"input",children:[(0,t.jsx)("option",{value:"",children:"اختر سنوات الخبرة"}),(0,t.jsx)("option",{value:"beginner",children:"مبتدئ (أقل من سنة)"}),(0,t.jsx)("option",{value:"1-2",children:"1-2 سنة"}),(0,t.jsx)("option",{value:"3-5",children:"3-5 سنوات"}),(0,t.jsx)("option",{value:"5+",children:"أكثر من 5 سنوات"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"المعدات المتاحة"}),(0,t.jsxs)("select",{value:r.equipment,onChange:e=>N("equipment",e.target.value),className:"input",children:[(0,t.jsx)("option",{value:"",children:"اختر المعدات"}),(0,t.jsx)("option",{value:"phone",children:"جوال احترافي"}),(0,t.jsx)("option",{value:"camera",children:"كاميرا DSLR"}),(0,t.jsx)("option",{value:"professional",children:"معدات احترافية كاملة"}),(0,t.jsx)("option",{value:"studio",children:"استوديو مجهز"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"اللغات المتاحة للمحتوى"}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3",children:["العربية","الإنجليزية","الفرنسية","الأخرى"].map(e=>(0,t.jsx)("div",{onClick:()=>w("languages",e),className:`p-3 rounded-lg border cursor-pointer text-center transition-colors ${r.languages.includes(e)?"border-emerald-500 bg-emerald-500/10":"border-slate-600 bg-slate-800/50 hover:border-slate-500"}`,children:(0,t.jsx)("div",{className:"text-white text-sm",children:e})},e))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"مدى التفرغ"}),(0,t.jsxs)("select",{value:r.availability,onChange:e=>N("availability",e.target.value),className:"input",children:[(0,t.jsx)("option",{value:"",children:"اختر مدى التفرغ"}),(0,t.jsx)("option",{value:"full-time",children:"متفرغ بالكامل"}),(0,t.jsx)("option",{value:"part-time",children:"دوام جزئي"}),(0,t.jsx)("option",{value:"weekends",children:"نهايات الأسبوع فقط"}),(0,t.jsx)("option",{value:"flexible",children:"مرن حسب المشروع"})]})]}),(0,t.jsx)(u.A,{className:"bg-emerald-500/10 border-emerald-500/20",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("div",{className:"flex items-center justify-center gap-4 mb-4",children:[(0,t.jsxs)("div",{className:"trust-indicator",children:[(0,t.jsx)(a.A,{className:"w-4 h-4"}),"\uD83C\uDDF8\uD83C\uDDE6 منصة سعودية موثوقة"]}),(0,t.jsxs)("div",{className:"trust-indicator",children:[(0,t.jsx)(d.A,{className:"w-4 h-4"}),"⭐ ضمان الجودة"]})]}),(0,t.jsx)("div",{className:"text-white font-semibold mb-2",children:"\uD83C\uDF89 مرحباً بك في عائلة المبدعين!"}),(0,t.jsx)("div",{className:"text-gray-300 text-sm",children:"ستحصل على فرص عمل حصرية وأدوات احترافية لتطوير مهاراتك"})]})})]});default:return null}})()}),(0,t.jsxs)("div",{className:"flex justify-between mt-8",children:[(0,t.jsxs)(p.A,{variant:"secondary",onClick:()=>{e>1&&s(e-1)},disabled:1===e,children:[(0,t.jsx)(n.A,{className:"h-5 w-5 ml-2"}),"السابق"]}),5===e?(0,t.jsxs)(p.A,{variant:"primary",onClick:()=>{console.log("Form submitted:",r),alert("تم إرسال طلب التسجيل بنجاح! سنتواصل معك قريباً.")},children:["\uD83D\uDE80 إرسال الطلب",(0,t.jsx)(o.A,{className:"h-5 w-5 mr-2"})]}):(0,t.jsxs)(p.A,{variant:"primary",onClick:()=>{e<5&&s(e+1)},children:["التالي",(0,t.jsx)(c.A,{className:"h-5 w-5 mr-2"})]})]})]})}),(0,t.jsx)(m.A,{}),(0,t.jsx)(h.A,{})]})}},70334:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(18962).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},89376:(e,s,r)=>{Promise.resolve().then(r.bind(r,47049))},93355:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>a.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>o});var t=r(65239),l=r(48088),i=r(88170),a=r.n(i),d=r(30893),n={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>d[e]);r.d(s,n);let o={children:["",{children:["register",{children:["ugc-creator",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,47049)),"C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\register\\ugc-creator\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\register\\ugc-creator\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:l.RouteKind.APP_PAGE,page:"/register/ugc-creator/page",pathname:"/register/ugc-creator",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},99270:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(18962).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[825,334,468],()=>r(93355));module.exports=t})();