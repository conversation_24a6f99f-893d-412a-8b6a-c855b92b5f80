'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import {
  BarChart3,
  Users,
  DollarSign,
  TrendingUp,
  Calendar,
  MessageCircle,
  Star,
  Eye,
  Settings,
  Bell,
  Plus,
  Filter,
  Download,
  Edit,
  CheckCircle,
  Clock,
  AlertCircle,
  Target,
  Zap,
  Video,
  Camera,
  Play,
  Upload,
  Award,
  Heart,
  Share2
} from 'lucide-react'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'

const UGCCreatorDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview')

  // Mock data
  const stats = {
    totalEarnings: 15600,
    activeProjects: 8,
    completedProjects: 47,
    averageRating: 4.9,
    totalViews: 850000,
    totalLikes: 42500,
    responseTime: '2 ساعات',
    completionRate: 98
  }

  const activeProjects = [
    {
      id: 1,
      client: 'براند الجمال',
      title: 'فيديو تيك توك لمنتج جديد',
      type: 'فيديو تيك توك',
      budget: 800,
      status: 'قيد التنفيذ',
      progress: 60,
      deadline: '2024-01-25',
      description: 'إنتاج فيديو تيك توك إبداعي لإطلاق منتج العناية الجديد',
      requirements: ['مدة 30 ثانية', 'مونتاج احترافي', 'موسيقى ترندنج'],
      clientAvatar: '💄'
    },
    {
      id: 2,
      client: 'متجر الأزياء',
      title: 'ستوريز انستقرام للمجموعة الشتوية',
      type: 'ستوري انستقرام',
      budget: 600,
      status: 'في المراجعة',
      progress: 90,
      deadline: '2024-01-22',
      description: 'سلسلة ستوريز لعرض المجموعة الشتوية الجديدة',
      requirements: ['5 ستوريز', 'عناصر تفاعلية', 'تصوير عالي الجودة'],
      clientAvatar: '👗'
    },
    {
      id: 3,
      client: 'شركة التقنية',
      title: 'مراجعة تطبيق جديد',
      type: 'مراجعة منتج',
      budget: 1200,
      status: 'جديد',
      progress: 10,
      deadline: '2024-01-30',
      description: 'مراجعة شاملة لتطبيق الذكي الجديد مع عرض المميزات',
      requirements: ['فيديو 60 ثانية', 'تجربة شخصية', 'مقارنات'],
      clientAvatar: '📱'
    }
  ]

  const recentEarnings = [
    { month: 'ديسمبر', amount: 4200, projects: 12 },
    { month: 'نوفمبر', amount: 3800, projects: 10 },
    { month: 'أكتوبر', amount: 5100, projects: 15 },
    { month: 'سبتمبر', amount: 3600, projects: 9 }
  ]

  const portfolioItems = [
    {
      id: 1,
      title: 'مراجعة كريم الوجه',
      type: 'فيديو تيك توك',
      views: 25000,
      likes: 1200,
      thumbnail: '🧴',
      platform: 'تيك توك',
      date: 'منذ 3 أيام'
    },
    {
      id: 2,
      title: 'ستايلنج فستان سهرة',
      type: 'ستوري انستقرام',
      views: 18000,
      likes: 890,
      thumbnail: '👗',
      platform: 'انستقرام',
      date: 'منذ أسبوع'
    },
    {
      id: 3,
      title: 'روتين العناية اليومي',
      type: 'ريلز انستقرام',
      views: 32000,
      likes: 1800,
      thumbnail: '✨',
      platform: 'انستقرام',
      date: 'منذ أسبوعين'
    }
  ]

  const notifications = [
    {
      id: 1,
      type: 'new_project',
      title: 'طلب مشروع جديد',
      message: 'شركة التقنية أرسلت طلب مشروع جديد',
      time: 'منذ ساعة',
      icon: '📱'
    },
    {
      id: 2,
      type: 'payment',
      title: 'تم استلام الدفعة',
      message: 'تم تحويل 800 ريال لحسابك',
      time: 'منذ 3 ساعات',
      icon: '💰'
    },
    {
      id: 3,
      type: 'review',
      title: 'تقييم جديد',
      message: 'براند الجمال قيّمك بـ 5 نجوم',
      time: 'منذ يوم',
      icon: '⭐'
    }
  ]

  const tabs = [
    { id: 'overview', label: 'نظرة عامة', icon: BarChart3 },
    { id: 'projects', label: 'المشاريع', icon: Video },
    { id: 'portfolio', label: 'معرض الأعمال', icon: Camera },
    { id: 'pricing', label: 'إعدادات الأسعار', icon: DollarSign },
    { id: 'earnings', label: 'الأرباح', icon: TrendingUp },
    { id: 'messages', label: 'الرسائل', icon: MessageCircle },
    { id: 'settings', label: 'الإعدادات', icon: Settings }
  ]

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K'
    }
    return num.toString()
  }

  return (
    <div className="min-h-screen bg-slate-900">
      {/* Header */}
      <header className="bg-slate-800/80 backdrop-blur-xl shadow-2xl sticky top-0 z-50 border-b border-purple-500/20">
        <div className="max-w-7xl mx-auto mobile-container">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="flex items-center">
                <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center">
                  <span className="text-white font-bold text-lg">🎬</span>
                </div>
                <span className="mr-3 text-lg font-bold text-white">لوحة تحكم المبدع</span>
              </Link>
            </div>

            <div className="flex items-center space-x-4 space-x-reverse">
              <button className="relative p-2 text-gray-400 hover:text-white transition-colors">
                <Bell className="h-6 w-6" />
                <span className="absolute top-0 right-0 h-2 w-2 bg-red-500 rounded-full"></span>
              </button>
              <div className="flex items-center">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-400 to-pink-400 rounded-full flex items-center justify-center text-white font-bold">
                  👩‍🎨
                </div>
                <span className="mr-3 text-sm font-medium text-white">سارة المبدعة</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto mobile-container py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar */}
          <div className="lg:w-64 flex-shrink-0">
            <Card className="card-mobile">
              <nav className="space-y-2">
                {tabs.map((tab) => {
                  const Icon = tab.icon
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full flex items-center px-3 py-3 rounded-xl text-right transition-all duration-300 ${
                        activeTab === tab.id
                          ? 'bg-purple-500/20 text-purple-400 border border-purple-500/30'
                          : 'text-gray-300 hover:bg-white/10 hover:text-white'
                      }`}
                    >
                      <Icon className="h-5 w-5 ml-3" />
                      <span>{tab.label}</span>
                    </button>
                  )
                })}
              </nav>
            </Card>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            {activeTab === 'overview' && (
              <div className="space-y-8">
                {/* Welcome */}
                <div>
                  <h1 className="text-3xl font-bold text-white mb-2">
                    مرحباً، سارة! 🎬
                  </h1>
                  <p className="text-gray-300">
                    إليك نظرة عامة على أداءك وأرباحك كمبدعة محتوى
                  </p>
                </div>

                {/* Stats Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <Card className="card-mobile text-center">
                    <div className="text-4xl mb-3">💰</div>
                    <div className="text-2xl font-bold text-white mb-1">{stats.totalEarnings.toLocaleString()} ريال</div>
                    <div className="text-gray-400 text-sm">إجمالي الأرباح</div>
                  </Card>

                  <Card className="card-mobile text-center">
                    <div className="text-4xl mb-3">🎯</div>
                    <div className="text-2xl font-bold text-white mb-1">{stats.activeProjects}</div>
                    <div className="text-gray-400 text-sm">المشاريع النشطة</div>
                  </Card>

                  <Card className="card-mobile text-center">
                    <div className="text-4xl mb-3">⭐</div>
                    <div className="text-2xl font-bold text-white mb-1">{stats.averageRating}</div>
                    <div className="text-gray-400 text-sm">متوسط التقييم</div>
                  </Card>

                  <Card className="card-mobile text-center">
                    <div className="text-4xl mb-3">👁️</div>
                    <div className="text-2xl font-bold text-white mb-1">{formatNumber(stats.totalViews)}</div>
                    <div className="text-gray-400 text-sm">إجمالي المشاهدات</div>
                  </Card>
                </div>

                {/* Active Projects */}
                <Card className="card-mobile">
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-xl font-bold text-white">المشاريع النشطة</h2>
                    <Button variant="secondary" size="sm" className="btn-secondary-mobile">
                      عرض الكل
                    </Button>
                  </div>

                  <div className="space-y-4">
                    {activeProjects.slice(0, 3).map((project) => (
                      <div key={project.id} className="border border-purple-500/20 rounded-2xl p-4 bg-white/5">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center">
                            <div className="text-2xl ml-3">{project.clientAvatar}</div>
                            <div>
                              <h3 className="font-semibold text-white">{project.title}</h3>
                              <p className="text-sm text-gray-400">{project.client} • {project.type}</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="text-lg font-bold text-white">{project.budget} ريال</p>
                            <p className="text-sm text-gray-400">{project.deadline}</p>
                          </div>
                        </div>

                        <div className="flex items-center justify-between mb-3">
                          <div className="flex-1 bg-gray-700 rounded-full h-2 ml-4">
                            <div
                              className="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full transition-all duration-500"
                              style={{ width: `${project.progress}%` }}
                            ></div>
                          </div>
                          <span className="text-sm font-medium text-white">{project.progress}%</span>
                        </div>

                        <div className="flex items-center justify-between">
                          <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                            project.status === 'قيد التنفيذ' ? 'bg-blue-500/20 text-blue-400' :
                            project.status === 'في المراجعة' ? 'bg-yellow-500/20 text-yellow-400' :
                            'bg-green-500/20 text-green-400'
                          }`}>
                            {project.status}
                          </span>
                          <div className="flex gap-2">
                            <Button size="sm" variant="secondary" className="btn-secondary-mobile">
                              <Eye className="h-4 w-4 ml-1" />
                              عرض
                            </Button>
                            <Button size="sm" className="btn-primary-mobile">
                              <Edit className="h-4 w-4 ml-1" />
                              تحديث
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </Card>

                {/* Recent Portfolio Items */}
                <Card className="card-mobile">
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-xl font-bold text-white">أحدث الأعمال</h2>
                    <Button variant="secondary" size="sm" className="btn-secondary-mobile">
                      إدارة المعرض
                    </Button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {portfolioItems.map((item) => (
                      <div key={item.id} className="bg-white/5 rounded-2xl p-4 hover:bg-white/10 transition-colors cursor-pointer">
                        <div className="text-center mb-3">
                          <div className="text-4xl mb-2">{item.thumbnail}</div>
                          <div className="text-sm text-gray-400">{item.platform}</div>
                        </div>
                        <h3 className="font-semibold text-white mb-2 text-center">{item.title}</h3>
                        <div className="flex items-center justify-between text-sm text-gray-400">
                          <span>{formatNumber(item.views)} مشاهدة</span>
                          <span>{formatNumber(item.likes)} إعجاب</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </Card>

                {/* Notifications */}
                <Card className="card-mobile">
                  <h2 className="text-xl font-bold text-white mb-6">الإشعارات الحديثة</h2>
                  <div className="space-y-3">
                    {notifications.map((notification) => (
                      <div key={notification.id} className="flex items-center p-3 bg-white/5 rounded-xl hover:bg-white/10 transition-colors">
                        <div className="text-2xl ml-3">{notification.icon}</div>
                        <div className="flex-1">
                          <div className="text-white font-medium">{notification.title}</div>
                          <div className="text-gray-400 text-sm">{notification.message}</div>
                        </div>
                        <div className="text-gray-400 text-xs">{notification.time}</div>
                      </div>
                    ))}
                  </div>
                </Card>
              </div>
            )}

            {activeTab === 'projects' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h1 className="text-2xl font-bold text-white">إدارة المشاريع</h1>
                  <div className="flex gap-3">
                    <Button variant="secondary" size="sm" className="btn-secondary-mobile">
                      <Filter className="h-4 w-4 ml-2" />
                      فلترة
                    </Button>
                    <Button variant="secondary" size="sm" className="btn-secondary-mobile">
                      <Download className="h-4 w-4 ml-2" />
                      تصدير
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                  <Card className="card-mobile text-center">
                    <Clock className="h-8 w-8 text-yellow-500 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-white">8</div>
                    <div className="text-sm text-gray-400">قيد التنفيذ</div>
                  </Card>
                  <Card className="card-mobile text-center">
                    <CheckCircle className="h-8 w-8 text-green-500 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-white">47</div>
                    <div className="text-sm text-gray-400">مكتملة</div>
                  </Card>
                  <Card className="card-mobile text-center">
                    <AlertCircle className="h-8 w-8 text-blue-500 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-white">3</div>
                    <div className="text-sm text-gray-400">في المراجعة</div>
                  </Card>
                  <Card className="card-mobile text-center">
                    <Target className="h-8 w-8 text-purple-500 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-white">58</div>
                    <div className="text-sm text-gray-400">إجمالي المشاريع</div>
                  </Card>
                </div>

                <Card className="card-mobile">
                  <div className="space-y-4">
                    {activeProjects.map((project) => (
                      <div key={project.id} className="border border-purple-500/20 rounded-2xl p-6 bg-white/5">
                        <div className="flex items-center justify-between mb-4">
                          <div className="flex items-center">
                            <div className="text-3xl ml-4">{project.clientAvatar}</div>
                            <div>
                              <h3 className="font-bold text-white text-lg">{project.title}</h3>
                              <p className="text-gray-400">{project.client} • {project.type}</p>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Button variant="secondary" size="sm" className="btn-secondary-mobile">
                              <Eye className="h-4 w-4 ml-2" />
                              عرض التفاصيل
                            </Button>
                            <Button size="sm" className="btn-primary-mobile">
                              <Edit className="h-4 w-4 ml-2" />
                              تحديث الحالة
                            </Button>
                          </div>
                        </div>

                        <p className="text-gray-300 mb-4">{project.description}</p>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                          <div>
                            <span className="text-gray-400 text-sm">الميزانية:</span>
                            <span className="font-bold text-white mr-2">{project.budget} ريال</span>
                          </div>
                          <div>
                            <span className="text-gray-400 text-sm">الموعد النهائي:</span>
                            <span className="font-medium text-white mr-2">{project.deadline}</span>
                          </div>
                          <div>
                            <span className="text-gray-400 text-sm">التقدم:</span>
                            <span className="font-medium text-white mr-2">{project.progress}%</span>
                          </div>
                        </div>

                        <div className="mb-4">
                          <span className="text-gray-400 text-sm">المتطلبات:</span>
                          <div className="flex flex-wrap gap-2 mt-2">
                            {project.requirements.map((req, i) => (
                              <span key={i} className="px-3 py-1 bg-cyan-500/20 text-cyan-300 text-sm rounded-full">
                                {req}
                              </span>
                            ))}
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                          <span className={`px-4 py-2 rounded-full text-sm font-medium ${
                            project.status === 'قيد التنفيذ' ? 'bg-blue-500/20 text-blue-400' :
                            project.status === 'في المراجعة' ? 'bg-yellow-500/20 text-yellow-400' :
                            'bg-green-500/20 text-green-400'
                          }`}>
                            {project.status}
                          </span>
                          <div className="flex-1 bg-gray-700 rounded-full h-2 mx-4">
                            <div
                              className="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full transition-all duration-500"
                              style={{ width: `${project.progress}%` }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </Card>
              </div>
            )}

            {activeTab === 'pricing' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h1 className="text-2xl font-bold text-white">إعدادات الأسعار</h1>
                  <Link href="/dashboard/ugc-creator/pricing">
                    <Button className="btn-primary-mobile">
                      <Settings className="h-4 w-4 ml-2" />
                      إدارة الأسعار المتقدمة
                    </Button>
                  </Link>
                </div>

                <Card className="card-mobile">
                  <h3 className="text-lg font-bold text-white mb-4">خدماتك الحالية</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 bg-white/5 rounded-xl">
                      <div className="flex items-center">
                        <span className="text-2xl ml-3">🎵</span>
                        <div>
                          <div className="text-white font-medium">فيديو تيك توك احترافي</div>
                          <div className="text-gray-400 text-sm">من 200 - 500 ريال</div>
                        </div>
                      </div>
                      <div className="text-green-400 font-semibold">نشط</div>
                    </div>

                    <div className="flex items-center justify-between p-4 bg-white/5 rounded-xl">
                      <div className="flex items-center">
                        <span className="text-2xl ml-3">📱</span>
                        <div>
                          <div className="text-white font-medium">ستوري انستقرام</div>
                          <div className="text-gray-400 text-sm">من 150 - 250 ريال</div>
                        </div>
                      </div>
                      <div className="text-green-400 font-semibold">نشط</div>
                    </div>

                    <div className="flex items-center justify-between p-4 bg-white/5 rounded-xl">
                      <div className="flex items-center">
                        <span className="text-2xl ml-3">📸</span>
                        <div>
                          <div className="text-white font-medium">تصوير منتج احترافي</div>
                          <div className="text-gray-400 text-sm">من 200 - 600 ريال</div>
                        </div>
                      </div>
                      <div className="text-green-400 font-semibold">نشط</div>
                    </div>
                  </div>
                </Card>

                <Card className="card-mobile bg-gradient-to-r from-purple-500/20 to-pink-500/20 border-purple-500/30">
                  <div className="text-center">
                    <h3 className="text-lg font-bold text-white mb-4">💡 نصائح لزيادة الأرباح</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-300">
                      <div>
                        <div className="text-2xl mb-2">📈</div>
                        <div className="font-medium mb-1">حدث أسعارك</div>
                        <div>راجع أسعارك شهرياً وحدثها حسب الطلب</div>
                      </div>
                      <div>
                        <div className="text-2xl mb-2">⭐</div>
                        <div className="font-medium mb-1">حسن جودتك</div>
                        <div>الجودة العالية تبرر الأسعار المرتفعة</div>
                      </div>
                      <div>
                        <div className="text-2xl mb-2">🎯</div>
                        <div className="font-medium mb-1">تخصص أكثر</div>
                        <div>التخصص في مجال معين يزيد قيمتك</div>
                      </div>
                    </div>
                  </div>
                </Card>
              </div>
            )}

            {/* Add other tab contents here */}
            {activeTab !== 'overview' && activeTab !== 'projects' && activeTab !== 'pricing' && (
              <Card className="card-mobile text-center py-12">
                <div className="text-6xl mb-4">🚧</div>
                <h2 className="text-xl font-bold text-white mb-4">
                  {tabs.find(tab => tab.id === activeTab)?.label}
                </h2>
                <p className="text-gray-400">
                  هذا القسم قيد التطوير وسيكون متاحاً قريباً
                </p>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default UGCCreatorDashboard
