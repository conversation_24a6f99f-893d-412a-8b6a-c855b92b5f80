"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/about/page",{

/***/ "(app-pages-browser)/./src/components/ui/Card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/Card.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst Card = (param)=>{\n    let { children, className, hover = false, padding = \"md\", shadow = \"md\" } = param;\n    const baseClasses = \"bg-white rounded-2xl border border-gray-100 transition-all duration-300 gpu-accelerated relative overflow-hidden\";\n    const paddingClasses = {\n        none: \"\",\n        sm: \"p-4\",\n        md: \"p-6\",\n        lg: \"p-8\",\n        xl: \"p-10\"\n    };\n    const shadowClasses = {\n        none: \"\",\n        sm: \"shadow-sm\",\n        md: \"shadow-lg\",\n        lg: \"shadow-xl\",\n        xl: \"shadow-2xl\"\n    };\n    const hoverClasses = hover ? \"hover:shadow-xl hover:-translate-y-2 cursor-pointer will-change-transform\" : \"\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(baseClasses, paddingClasses[padding], shadowClasses[shadow], hoverClasses, className),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Card;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Card);\nvar _c;\n$RefreshReg$(_c, \"Card\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/Card.tsx\n"));

/***/ })

});