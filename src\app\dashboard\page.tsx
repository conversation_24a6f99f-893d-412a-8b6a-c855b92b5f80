'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { 
  BarChart3, 
  Users, 
  MessageCircle, 
  Star, 
  TrendingUp, 
  Calendar,
  DollarSign,
  Eye,
  Heart,
  Play,
  Settings,
  Bell,
  Search,
  Filter,
  Download,
  Plus,
  ArrowRight
} from 'lucide-react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import MobileNavigation from '@/components/layout/MobileNavigation'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Badge from '@/components/ui/Badge'

const DashboardPage: React.FC = () => {
  const [userType, setUserType] = useState<'merchant' | 'influencer' | 'ugc-creator'>('merchant')

  // Mock user data
  const user = {
    name: 'أحمد التاجر',
    avatar: '👨‍💼',
    type: userType,
    joinDate: '2024-01-15',
    verified: true
  }

  const stats = {
    merchant: [
      { label: 'الحملات النشطة', value: '12', icon: BarChart3, color: 'text-blue-400' },
      { label: 'المؤثرين المتعاونين', value: '45', icon: Users, color: 'text-green-400' },
      { label: 'إجمالي المشاهدات', value: '2.5M', icon: Eye, color: 'text-purple-400' },
      { label: 'معدل التفاعل', value: '8.2%', icon: Heart, color: 'text-pink-400' }
    ],
    influencer: [
      { label: 'الحملات المكتملة', value: '28', icon: BarChart3, color: 'text-blue-400' },
      { label: 'المتابعين الجدد', value: '+1.2K', icon: Users, color: 'text-green-400' },
      { label: 'الأرباح هذا الشهر', value: '15,500 ريال', icon: DollarSign, color: 'text-yellow-400' },
      { label: 'التقييم', value: '4.9', icon: Star, color: 'text-orange-400' }
    ],
    'ugc-creator': [
      { label: 'المشاريع المكتملة', value: '34', icon: BarChart3, color: 'text-blue-400' },
      { label: 'العملاء الراضين', value: '98%', icon: Users, color: 'text-green-400' },
      { label: 'الأرباح هذا الشهر', value: '12,800 ريال', icon: DollarSign, color: 'text-yellow-400' },
      { label: 'متوسط التقييم', value: '4.8', icon: Star, color: 'text-orange-400' }
    ]
  }

  const recentActivities = {
    merchant: [
      { type: 'campaign', title: 'تم إطلاق حملة "منتج الصيف الجديد"', time: 'منذ ساعتين', status: 'active' },
      { type: 'influencer', title: 'انضم مؤثر جديد لحملة "العناية بالبشرة"', time: 'منذ 4 ساعات', status: 'success' },
      { type: 'report', title: 'تقرير أداء حملة "الموضة العصرية" جاهز', time: 'منذ يوم', status: 'info' },
      { type: 'payment', title: 'تم دفع 5,000 ريال للمؤثرين', time: 'منذ يومين', status: 'success' }
    ],
    influencer: [
      { type: 'offer', title: 'عرض جديد من "براند الجمال" - 2,500 ريال', time: 'منذ ساعة', status: 'pending' },
      { type: 'payment', title: 'تم استلام دفعة 3,200 ريال', time: 'منذ 3 ساعات', status: 'success' },
      { type: 'review', title: 'تقييم جديد 5 نجوم من "متجر الأزياء"', time: 'منذ يوم', status: 'success' },
      { type: 'follower', title: 'وصلت لـ 50K متابع على انستقرام!', time: 'منذ يومين', status: 'info' }
    ],
    'ugc-creator': [
      { type: 'project', title: 'طلب جديد: فيديو تيك توك لمنتج تقني', time: 'منذ 30 دقيقة', status: 'pending' },
      { type: 'delivery', title: 'تم تسليم مشروع "مراجعة المطعم الجديد"', time: 'منذ ساعتين', status: 'success' },
      { type: 'payment', title: 'تم استلام 1,800 ريال لمشروع الأسبوع الماضي', time: 'منذ يوم', status: 'success' },
      { type: 'review', title: 'تقييم ممتاز (5/5) من عميل جديد', time: 'منذ يومين', status: 'success' }
    ]
  }

  const quickActions = {
    merchant: [
      { title: 'إنشاء حملة جديدة', icon: Plus, href: '/campaigns/create', color: 'bg-blue-500' },
      { title: 'تصفح المؤثرين', icon: Users, href: '/influencers', color: 'bg-green-500' },
      { title: 'تصفح مبدعي UGC', icon: Play, href: '/ugc-creators', color: 'bg-purple-500' },
      { title: 'تقارير الأداء', icon: BarChart3, href: '/reports', color: 'bg-orange-500' }
    ],
    influencer: [
      { title: 'تحديث الملف الشخصي', icon: Settings, href: '/dashboard/influencer/profile', color: 'bg-blue-500' },
      { title: 'العروض الجديدة', icon: Bell, href: '/dashboard/influencer/offers', color: 'bg-green-500' },
      { title: 'إدارة المحتوى', icon: Play, href: '/dashboard/influencer/content', color: 'bg-purple-500' },
      { title: 'الأرباح والمدفوعات', icon: DollarSign, href: '/dashboard/influencer/earnings', color: 'bg-yellow-500' }
    ],
    'ugc-creator': [
      { title: 'إدارة الأسعار', icon: Settings, href: '/dashboard/ugc-creator/pricing', color: 'bg-blue-500' },
      { title: 'الطلبات الجديدة', icon: Bell, href: '/dashboard/ugc-creator/orders', color: 'bg-green-500' },
      { title: 'معرض الأعمال', icon: Play, href: '/dashboard/ugc-creator/portfolio', color: 'bg-purple-500' },
      { title: 'الأرباح', icon: DollarSign, href: '/dashboard/ugc-creator/earnings', color: 'bg-yellow-500' }
    ]
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-green-400'
      case 'pending': return 'text-yellow-400'
      case 'active': return 'text-blue-400'
      case 'info': return 'text-purple-400'
      default: return 'text-gray-400'
    }
  }

  const getStatusIcon = (type: string) => {
    switch (type) {
      case 'campaign': return '🚀'
      case 'influencer': return '👥'
      case 'report': return '📊'
      case 'payment': return '💰'
      case 'offer': return '💼'
      case 'review': return '⭐'
      case 'follower': return '📈'
      case 'project': return '🎬'
      case 'delivery': return '✅'
      default: return '📝'
    }
  }

  return (
    <div className="min-h-screen bg-slate-900">
      <Header />
      
      <div className="py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-2xl font-bold text-white mb-2">
                  مرحباً، {user.name} {user.avatar}
                </h1>
                <p className="text-slate-400">
                  {userType === 'merchant' && 'إدارة حملاتك الإعلانية والمؤثرين'}
                  {userType === 'influencer' && 'تابع أداءك وإدارة حملاتك'}
                  {userType === 'ugc-creator' && 'إدارة مشاريعك وأعمالك الإبداعية'}
                </p>
              </div>
              
              <div className="flex gap-2">
                <Badge variant="success" size="sm">
                  ✅ موثق
                </Badge>
                <Badge variant="primary" size="sm">
                  {userType === 'merchant' && '🏢 تاجر'}
                  {userType === 'influencer' && '⭐ مؤثر'}
                  {userType === 'ugc-creator' && '🎬 مبدع'}
                </Badge>
              </div>
            </div>

            {/* User Type Switcher (for demo) */}
            <div className="flex gap-2 mb-6">
              <Button 
                variant={userType === 'merchant' ? 'primary' : 'secondary'}
                size="sm"
                onClick={() => setUserType('merchant')}
              >
                تاجر
              </Button>
              <Button 
                variant={userType === 'influencer' ? 'primary' : 'secondary'}
                size="sm"
                onClick={() => setUserType('influencer')}
              >
                مؤثر
              </Button>
              <Button 
                variant={userType === 'ugc-creator' ? 'primary' : 'secondary'}
                size="sm"
                onClick={() => setUserType('ugc-creator')}
              >
                مبدع UGC
              </Button>
            </div>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {stats[userType].map((stat, index) => (
              <Card key={index}>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-slate-400 text-sm">{stat.label}</p>
                    <p className="text-2xl font-bold text-white mt-1">{stat.value}</p>
                  </div>
                  <div className={`p-3 rounded-lg bg-slate-800 ${stat.color}`}>
                    <stat.icon className="h-6 w-6" />
                  </div>
                </div>
              </Card>
            ))}
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Quick Actions */}
            <div className="lg:col-span-1">
              <Card>
                <h3 className="text-lg font-semibold text-white mb-4">إجراءات سريعة</h3>
                <div className="space-y-3">
                  {quickActions[userType].map((action, index) => (
                    <Link key={index} href={action.href}>
                      <div className="flex items-center p-3 rounded-lg bg-slate-800 hover:bg-slate-700 transition-colors cursor-pointer">
                        <div className={`p-2 rounded-lg ${action.color} mr-3`}>
                          <action.icon className="h-4 w-4 text-white" />
                        </div>
                        <span className="text-white font-medium">{action.title}</span>
                        <ArrowRight className="h-4 w-4 text-slate-400 mr-auto" />
                      </div>
                    </Link>
                  ))}
                </div>
              </Card>
            </div>

            {/* Recent Activity */}
            <div className="lg:col-span-2">
              <Card>
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-white">النشاط الأخير</h3>
                  <Button variant="secondary" size="sm">
                    عرض الكل
                  </Button>
                </div>
                <div className="space-y-4">
                  {recentActivities[userType].map((activity, index) => (
                    <div key={index} className="flex items-start p-3 rounded-lg bg-slate-800">
                      <div className="text-2xl mr-3">
                        {getStatusIcon(activity.type)}
                      </div>
                      <div className="flex-1">
                        <p className="text-white font-medium">{activity.title}</p>
                        <p className="text-slate-400 text-sm mt-1">{activity.time}</p>
                      </div>
                      <div className={`text-sm font-medium ${getStatusColor(activity.status)}`}>
                        {activity.status === 'success' && '✅'}
                        {activity.status === 'pending' && '⏳'}
                        {activity.status === 'active' && '🔄'}
                        {activity.status === 'info' && 'ℹ️'}
                      </div>
                    </div>
                  ))}
                </div>
              </Card>
            </div>
          </div>

          {/* Navigation Links */}
          <div className="mt-8">
            <Card>
              <h3 className="text-lg font-semibold text-white mb-4">روابط مفيدة</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Link href="/help" className="text-center p-4 rounded-lg bg-slate-800 hover:bg-slate-700 transition-colors">
                  <div className="text-2xl mb-2">❓</div>
                  <div className="text-white text-sm">مركز المساعدة</div>
                </Link>
                <Link href="/support" className="text-center p-4 rounded-lg bg-slate-800 hover:bg-slate-700 transition-colors">
                  <div className="text-2xl mb-2">🎧</div>
                  <div className="text-white text-sm">الدعم الفني</div>
                </Link>
                <Link href="/messages" className="text-center p-4 rounded-lg bg-slate-800 hover:bg-slate-700 transition-colors">
                  <div className="text-2xl mb-2">💬</div>
                  <div className="text-white text-sm">الرسائل</div>
                </Link>
                <Link href="/pricing" className="text-center p-4 rounded-lg bg-slate-800 hover:bg-slate-700 transition-colors">
                  <div className="text-2xl mb-2">💰</div>
                  <div className="text-white text-sm">الأسعار</div>
                </Link>
              </div>
            </Card>
          </div>
        </div>
      </div>

      <Footer />
      <MobileNavigation />
    </div>
  )
}

export default DashboardPage
