'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  Eye, 
  Heart, 
  MessageCircle, 
  Share2,
  Users,
  DollarSign,
  Calendar,
  Download,
  Filter,
  RefreshCw,
  Target,
  Award,
  Zap,
  Clock
} from 'lucide-react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import MobileNavigation from '@/components/layout/MobileNavigation'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Badge from '@/components/ui/Badge'
import Breadcrumb from '@/components/ui/Breadcrumb'
import Container from '@/components/ui/Container'

const ReportsPage: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('30d')
  const [selectedCampaign, setSelectedCampaign] = useState('all')

  const periods = [
    { id: '7d', label: '7 أيام' },
    { id: '30d', label: '30 يوم' },
    { id: '90d', label: '90 يوم' },
    { id: '1y', label: 'سنة' }
  ]

  const campaigns = [
    { id: 'all', name: 'جميع الحملات' },
    { id: 'summer-collection', name: 'مجموعة الصيف' },
    { id: 'tech-launch', name: 'إطلاق المنتج التقني' },
    { id: 'beauty-campaign', name: 'حملة الجمال' }
  ]

  const overviewStats = [
    {
      title: 'إجمالي المشاهدات',
      value: '2.5M',
      change: '+12.5%',
      trend: 'up',
      icon: Eye,
      color: 'text-blue-400'
    },
    {
      title: 'معدل التفاعل',
      value: '8.2%',
      change: '+2.1%',
      trend: 'up',
      icon: Heart,
      color: 'text-pink-400'
    },
    {
      title: 'الوصول الإجمالي',
      value: '1.8M',
      change: '+8.7%',
      trend: 'up',
      icon: Users,
      color: 'text-green-400'
    },
    {
      title: 'العائد على الاستثمار',
      value: '320%',
      change: '+15.3%',
      trend: 'up',
      icon: DollarSign,
      color: 'text-yellow-400'
    }
  ]

  const campaignPerformance = [
    {
      id: 1,
      name: 'مجموعة الصيف الجديدة',
      influencers: 12,
      reach: '850K',
      engagement: '9.2%',
      conversions: 1250,
      roi: '380%',
      status: 'active',
      budget: '25,000',
      spent: '18,500'
    },
    {
      id: 2,
      name: 'إطلاق المنتج التقني',
      influencers: 8,
      reach: '620K',
      engagement: '7.8%',
      conversions: 890,
      roi: '290%',
      status: 'completed',
      budget: '15,000',
      spent: '15,000'
    },
    {
      id: 3,
      name: 'حملة العناية بالبشرة',
      influencers: 15,
      reach: '1.2M',
      engagement: '8.9%',
      conversions: 2100,
      roi: '420%',
      status: 'active',
      budget: '35,000',
      spent: '28,000'
    }
  ]

  const topInfluencers = [
    {
      id: 1,
      name: 'سارة المؤثرة',
      avatar: '👩‍💼',
      followers: '250K',
      engagement: '12.5%',
      campaigns: 3,
      revenue: '8,500',
      rating: 4.9
    },
    {
      id: 2,
      name: 'أحمد التقني',
      avatar: '👨‍💻',
      followers: '180K',
      engagement: '10.2%',
      campaigns: 2,
      revenue: '6,200',
      rating: 4.8
    },
    {
      id: 3,
      name: 'فاطمة الجمال',
      avatar: '👩‍🎨',
      followers: '320K',
      engagement: '14.1%',
      campaigns: 4,
      revenue: '12,300',
      rating: 4.9
    }
  ]

  const platformStats = [
    {
      platform: 'انستقرام',
      icon: '📷',
      reach: '1.2M',
      engagement: '8.5%',
      conversions: 1850,
      share: '45%'
    },
    {
      platform: 'تيك توك',
      icon: '🎵',
      reach: '800K',
      engagement: '12.3%',
      conversions: 1200,
      share: '30%'
    },
    {
      platform: 'سناب شات',
      icon: '👻',
      reach: '450K',
      engagement: '6.8%',
      conversions: 680,
      share: '17%'
    },
    {
      platform: 'يوتيوب',
      icon: '📺',
      reach: '300K',
      engagement: '5.2%',
      conversions: 420,
      share: '8%'
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success'
      case 'completed': return 'primary'
      case 'paused': return 'warning'
      default: return 'secondary'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return 'نشطة'
      case 'completed': return 'مكتملة'
      case 'paused': return 'متوقفة'
      default: return 'غير محدد'
    }
  }

  return (
    <div className="min-h-screen bg-slate-900">
      <Header />
      
      <div className="py-8">
        <Container size="lg">
          {/* Breadcrumb */}
          <div className="mb-6">
            <Breadcrumb items={[
              { label: 'الرئيسية', href: '/' },
              { label: 'لوحة التحكم', href: '/dashboard' },
              { label: 'التقارير والإحصائيات' }
            ]} />
          </div>

          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-2xl font-bold text-white mb-2">التقارير والإحصائيات</h1>
              <p className="text-slate-400">تابع أداء حملاتك وتحليل النتائج</p>
            </div>
            
            <div className="flex gap-3">
              <Button variant="secondary" size="sm">
                <RefreshCw className="h-4 w-4 ml-2" />
                تحديث
              </Button>
              <Button variant="primary" size="sm">
                <Download className="h-4 w-4 ml-2" />
                تصدير
              </Button>
            </div>
          </div>

          {/* Filters */}
          <Card className="mb-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  الفترة الزمنية
                </label>
                <select
                  value={selectedPeriod}
                  onChange={(e) => setSelectedPeriod(e.target.value)}
                  className="input"
                >
                  {periods.map((period) => (
                    <option key={period.id} value={period.id}>
                      {period.label}
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  الحملة
                </label>
                <select
                  value={selectedCampaign}
                  onChange={(e) => setSelectedCampaign(e.target.value)}
                  className="input"
                >
                  {campaigns.map((campaign) => (
                    <option key={campaign.id} value={campaign.id}>
                      {campaign.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="flex items-end">
                <Button variant="secondary" className="w-full">
                  <Filter className="h-4 w-4 ml-2" />
                  تطبيق الفلاتر
                </Button>
              </div>
            </div>
          </Card>

          {/* Overview Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {overviewStats.map((stat, index) => (
              <Card key={index}>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-slate-400 text-sm">{stat.title}</p>
                    <p className="text-2xl font-bold text-white mt-1">{stat.value}</p>
                    <div className="flex items-center mt-2">
                      {stat.trend === 'up' ? (
                        <TrendingUp className="h-4 w-4 text-green-400 ml-1" />
                      ) : (
                        <TrendingDown className="h-4 w-4 text-red-400 ml-1" />
                      )}
                      <span className={`text-sm ${stat.trend === 'up' ? 'text-green-400' : 'text-red-400'}`}>
                        {stat.change}
                      </span>
                    </div>
                  </div>
                  <div className={`p-3 rounded-lg bg-slate-800 ${stat.color}`}>
                    <stat.icon className="h-6 w-6" />
                  </div>
                </div>
              </Card>
            ))}
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            {/* Campaign Performance */}
            <Card>
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-white">أداء الحملات</h3>
                <Button variant="secondary" size="sm">
                  عرض الكل
                </Button>
              </div>
              
              <div className="space-y-4">
                {campaignPerformance.map((campaign) => (
                  <div key={campaign.id} className="p-4 bg-slate-800 rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium text-white">{campaign.name}</h4>
                      <Badge variant={getStatusColor(campaign.status)} size="sm">
                        {getStatusText(campaign.status)}
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-slate-400">الوصول:</span>
                        <span className="text-white mr-2">{campaign.reach}</span>
                      </div>
                      <div>
                        <span className="text-slate-400">التفاعل:</span>
                        <span className="text-white mr-2">{campaign.engagement}</span>
                      </div>
                      <div>
                        <span className="text-slate-400">التحويلات:</span>
                        <span className="text-white mr-2">{campaign.conversions}</span>
                      </div>
                      <div>
                        <span className="text-slate-400">العائد:</span>
                        <span className="text-green-400 mr-2">{campaign.roi}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </Card>

            {/* Platform Performance */}
            <Card>
              <h3 className="text-lg font-semibold text-white mb-6">أداء المنصات</h3>
              
              <div className="space-y-4">
                {platformStats.map((platform, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-slate-800 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="text-2xl">{platform.icon}</div>
                      <div>
                        <div className="text-white font-medium">{platform.platform}</div>
                        <div className="text-slate-400 text-sm">الوصول: {platform.reach}</div>
                      </div>
                    </div>
                    
                    <div className="text-left">
                      <div className="text-white font-medium">{platform.engagement}</div>
                      <div className="text-slate-400 text-sm">{platform.share} من الإجمالي</div>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </div>

          {/* Top Influencers */}
          <Card>
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-white">أفضل المؤثرين</h3>
              <Link href="/influencers">
                <Button variant="secondary" size="sm">
                  عرض جميع المؤثرين
                </Button>
              </Link>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {topInfluencers.map((influencer) => (
                <div key={influencer.id} className="p-4 bg-slate-800 rounded-lg">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-12 h-12 bg-indigo-600 rounded-full flex items-center justify-center text-xl">
                      {influencer.avatar}
                    </div>
                    <div>
                      <h4 className="font-medium text-white">{influencer.name}</h4>
                      <div className="text-slate-400 text-sm">{influencer.followers} متابع</div>
                    </div>
                  </div>
                  
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-slate-400">التفاعل:</span>
                      <span className="text-white">{influencer.engagement}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-400">الحملات:</span>
                      <span className="text-white">{influencer.campaigns}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-400">الإيرادات:</span>
                      <span className="text-green-400">{influencer.revenue} ريال</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-400">التقييم:</span>
                      <div className="flex items-center">
                        <span className="text-yellow-400 mr-1">{influencer.rating}</span>
                        <div className="flex">
                          {[...Array(5)].map((_, i) => (
                            <div key={i} className={`w-3 h-3 ${i < Math.floor(influencer.rating) ? 'text-yellow-400' : 'text-slate-600'}`}>
                              ⭐
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Card>

          {/* Quick Actions */}
          <div className="mt-8">
            <Card>
              <h3 className="text-lg font-semibold text-white mb-6">إجراءات سريعة</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Link href="/campaigns/create" className="text-center p-4 rounded-lg bg-slate-800 hover:bg-slate-700 transition-colors">
                  <div className="text-2xl mb-2">🚀</div>
                  <div className="text-white text-sm">إنشاء حملة جديدة</div>
                </Link>
                <Link href="/influencers" className="text-center p-4 rounded-lg bg-slate-800 hover:bg-slate-700 transition-colors">
                  <div className="text-2xl mb-2">👥</div>
                  <div className="text-white text-sm">تصفح المؤثرين</div>
                </Link>
                <Link href="/ugc-creators" className="text-center p-4 rounded-lg bg-slate-800 hover:bg-slate-700 transition-colors">
                  <div className="text-2xl mb-2">🎬</div>
                  <div className="text-white text-sm">مبدعي UGC</div>
                </Link>
                <Link href="/messages" className="text-center p-4 rounded-lg bg-slate-800 hover:bg-slate-700 transition-colors">
                  <div className="text-2xl mb-2">💬</div>
                  <div className="text-white text-sm">الرسائل</div>
                </Link>
              </div>
            </Card>
          </div>
        </Container>
      </div>

      <Footer />
      <MobileNavigation />
    </div>
  )
}

export default ReportsPage
