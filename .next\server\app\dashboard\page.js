/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(rsc)/./src/app/dashboard/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(rsc)/./src/app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3NobTcxJTVDJTVDRGVza3RvcCU1QyU1Q2Ftc2hvcjIlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNkYXNoYm9hcmQlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0tBQXNHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxzaG03MVxcXFxEZXNrdG9wXFxcXGFtc2hvcjJcXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\dashboard\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"09bb85b10d2b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNobTcxXFxEZXNrdG9wXFxhbXNob3IyXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIwOWJiODViMTBkMmJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\nconst metadata = {\n    title: 'منصة المؤثرين السعودية - ربط المؤثرين بالتجار',\n    description: 'منصة احترافية لربط المؤثرين بالتجار في المملكة العربية السعودية مع ضمان الأموال ونظام الدفع الآمن',\n    keywords: 'مؤثرين, تجار, السعودية, تسويق, إعلانات, سوشيال ميديا',\n    authors: [\n        {\n            name: 'منصة المؤثرين السعودية'\n        }\n    ],\n    viewport: 'width=device-width, initial-scale=1',\n    robots: 'index, follow',\n    openGraph: {\n        title: 'منصة المؤثرين السعودية',\n        description: 'منصة احترافية لربط المؤثرين بالتجار في المملكة العربية السعودية',\n        type: 'website',\n        locale: 'ar_SA'\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 font-arabic\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    id: \"root\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(ssr)/./src/app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3NobTcxJTVDJTVDRGVza3RvcCU1QyU1Q2Ftc2hvcjIlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNkYXNoYm9hcmQlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0tBQXNHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxzaG03MVxcXFxEZXNrdG9wXFxcXGFtc2hvcjJcXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cshm71%5C%5CDesktop%5C%5Camshor2%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Bell_DollarSign_Eye_Heart_Play_Plus_Settings_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Bell,DollarSign,Eye,Heart,Play,Plus,Settings,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Bell_DollarSign_Eye_Heart_Play_Plus_Settings_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Bell,DollarSign,Eye,Heart,Play,Plus,Settings,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Bell_DollarSign_Eye_Heart_Play_Plus_Settings_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Bell,DollarSign,Eye,Heart,Play,Plus,Settings,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Bell_DollarSign_Eye_Heart_Play_Plus_Settings_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Bell,DollarSign,Eye,Heart,Play,Plus,Settings,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Bell_DollarSign_Eye_Heart_Play_Plus_Settings_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Bell,DollarSign,Eye,Heart,Play,Plus,Settings,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Bell_DollarSign_Eye_Heart_Play_Plus_Settings_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Bell,DollarSign,Eye,Heart,Play,Plus,Settings,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Bell_DollarSign_Eye_Heart_Play_Plus_Settings_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Bell,DollarSign,Eye,Heart,Play,Plus,Settings,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Bell_DollarSign_Eye_Heart_Play_Plus_Settings_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Bell,DollarSign,Eye,Heart,Play,Plus,Settings,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Bell_DollarSign_Eye_Heart_Play_Plus_Settings_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Bell,DollarSign,Eye,Heart,Play,Plus,Settings,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Bell_DollarSign_Eye_Heart_Play_Plus_Settings_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Bell,DollarSign,Eye,Heart,Play,Plus,Settings,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Bell_DollarSign_Eye_Heart_Play_Plus_Settings_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Bell,DollarSign,Eye,Heart,Play,Plus,Settings,Star,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _components_layout_Header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/Header */ \"(ssr)/./src/components/layout/Header.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/Footer */ \"(ssr)/./src/components/layout/Footer.tsx\");\n/* harmony import */ var _components_layout_MobileNavigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/layout/MobileNavigation */ \"(ssr)/./src/components/layout/MobileNavigation.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/Card */ \"(ssr)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/Badge */ \"(ssr)/./src/components/ui/Badge.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nconst DashboardPage = ()=>{\n    const [userType, setUserType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('merchant');\n    // Mock user data\n    const user = {\n        name: 'أحمد التاجر',\n        avatar: '👨‍💼',\n        type: userType,\n        joinDate: '2024-01-15',\n        verified: true\n    };\n    const stats = {\n        merchant: [\n            {\n                label: 'الحملات النشطة',\n                value: '12',\n                icon: _barrel_optimize_names_ArrowRight_BarChart3_Bell_DollarSign_Eye_Heart_Play_Plus_Settings_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                color: 'text-blue-400'\n            },\n            {\n                label: 'المؤثرين المتعاونين',\n                value: '45',\n                icon: _barrel_optimize_names_ArrowRight_BarChart3_Bell_DollarSign_Eye_Heart_Play_Plus_Settings_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                color: 'text-green-400'\n            },\n            {\n                label: 'إجمالي المشاهدات',\n                value: '2.5M',\n                icon: _barrel_optimize_names_ArrowRight_BarChart3_Bell_DollarSign_Eye_Heart_Play_Plus_Settings_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                color: 'text-purple-400'\n            },\n            {\n                label: 'معدل التفاعل',\n                value: '8.2%',\n                icon: _barrel_optimize_names_ArrowRight_BarChart3_Bell_DollarSign_Eye_Heart_Play_Plus_Settings_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                color: 'text-pink-400'\n            }\n        ],\n        influencer: [\n            {\n                label: 'الحملات المكتملة',\n                value: '28',\n                icon: _barrel_optimize_names_ArrowRight_BarChart3_Bell_DollarSign_Eye_Heart_Play_Plus_Settings_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                color: 'text-blue-400'\n            },\n            {\n                label: 'المتابعين الجدد',\n                value: '+1.2K',\n                icon: _barrel_optimize_names_ArrowRight_BarChart3_Bell_DollarSign_Eye_Heart_Play_Plus_Settings_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                color: 'text-green-400'\n            },\n            {\n                label: 'الأرباح هذا الشهر',\n                value: '15,500 ريال',\n                icon: _barrel_optimize_names_ArrowRight_BarChart3_Bell_DollarSign_Eye_Heart_Play_Plus_Settings_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                color: 'text-yellow-400'\n            },\n            {\n                label: 'التقييم',\n                value: '4.9',\n                icon: _barrel_optimize_names_ArrowRight_BarChart3_Bell_DollarSign_Eye_Heart_Play_Plus_Settings_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                color: 'text-orange-400'\n            }\n        ],\n        'ugc-creator': [\n            {\n                label: 'المشاريع المكتملة',\n                value: '34',\n                icon: _barrel_optimize_names_ArrowRight_BarChart3_Bell_DollarSign_Eye_Heart_Play_Plus_Settings_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                color: 'text-blue-400'\n            },\n            {\n                label: 'العملاء الراضين',\n                value: '98%',\n                icon: _barrel_optimize_names_ArrowRight_BarChart3_Bell_DollarSign_Eye_Heart_Play_Plus_Settings_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                color: 'text-green-400'\n            },\n            {\n                label: 'الأرباح هذا الشهر',\n                value: '12,800 ريال',\n                icon: _barrel_optimize_names_ArrowRight_BarChart3_Bell_DollarSign_Eye_Heart_Play_Plus_Settings_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                color: 'text-yellow-400'\n            },\n            {\n                label: 'متوسط التقييم',\n                value: '4.8',\n                icon: _barrel_optimize_names_ArrowRight_BarChart3_Bell_DollarSign_Eye_Heart_Play_Plus_Settings_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                color: 'text-orange-400'\n            }\n        ]\n    };\n    const recentActivities = {\n        merchant: [\n            {\n                type: 'campaign',\n                title: 'تم إطلاق حملة \"منتج الصيف الجديد\"',\n                time: 'منذ ساعتين',\n                status: 'active'\n            },\n            {\n                type: 'influencer',\n                title: 'انضم مؤثر جديد لحملة \"العناية بالبشرة\"',\n                time: 'منذ 4 ساعات',\n                status: 'success'\n            },\n            {\n                type: 'report',\n                title: 'تقرير أداء حملة \"الموضة العصرية\" جاهز',\n                time: 'منذ يوم',\n                status: 'info'\n            },\n            {\n                type: 'payment',\n                title: 'تم دفع 5,000 ريال للمؤثرين',\n                time: 'منذ يومين',\n                status: 'success'\n            }\n        ],\n        influencer: [\n            {\n                type: 'offer',\n                title: 'عرض جديد من \"براند الجمال\" - 2,500 ريال',\n                time: 'منذ ساعة',\n                status: 'pending'\n            },\n            {\n                type: 'payment',\n                title: 'تم استلام دفعة 3,200 ريال',\n                time: 'منذ 3 ساعات',\n                status: 'success'\n            },\n            {\n                type: 'review',\n                title: 'تقييم جديد 5 نجوم من \"متجر الأزياء\"',\n                time: 'منذ يوم',\n                status: 'success'\n            },\n            {\n                type: 'follower',\n                title: 'وصلت لـ 50K متابع على انستقرام!',\n                time: 'منذ يومين',\n                status: 'info'\n            }\n        ],\n        'ugc-creator': [\n            {\n                type: 'project',\n                title: 'طلب جديد: فيديو تيك توك لمنتج تقني',\n                time: 'منذ 30 دقيقة',\n                status: 'pending'\n            },\n            {\n                type: 'delivery',\n                title: 'تم تسليم مشروع \"مراجعة المطعم الجديد\"',\n                time: 'منذ ساعتين',\n                status: 'success'\n            },\n            {\n                type: 'payment',\n                title: 'تم استلام 1,800 ريال لمشروع الأسبوع الماضي',\n                time: 'منذ يوم',\n                status: 'success'\n            },\n            {\n                type: 'review',\n                title: 'تقييم ممتاز (5/5) من عميل جديد',\n                time: 'منذ يومين',\n                status: 'success'\n            }\n        ]\n    };\n    const quickActions = {\n        merchant: [\n            {\n                title: 'إنشاء حملة جديدة',\n                icon: _barrel_optimize_names_ArrowRight_BarChart3_Bell_DollarSign_Eye_Heart_Play_Plus_Settings_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                href: '/campaigns/create',\n                color: 'bg-blue-500'\n            },\n            {\n                title: 'تصفح المؤثرين',\n                icon: _barrel_optimize_names_ArrowRight_BarChart3_Bell_DollarSign_Eye_Heart_Play_Plus_Settings_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                href: '/influencers',\n                color: 'bg-green-500'\n            },\n            {\n                title: 'تصفح مبدعي UGC',\n                icon: _barrel_optimize_names_ArrowRight_BarChart3_Bell_DollarSign_Eye_Heart_Play_Plus_Settings_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                href: '/ugc-creators',\n                color: 'bg-purple-500'\n            },\n            {\n                title: 'تقارير الأداء',\n                icon: _barrel_optimize_names_ArrowRight_BarChart3_Bell_DollarSign_Eye_Heart_Play_Plus_Settings_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                href: '/reports',\n                color: 'bg-orange-500'\n            }\n        ],\n        influencer: [\n            {\n                title: 'تحديث الملف الشخصي',\n                icon: _barrel_optimize_names_ArrowRight_BarChart3_Bell_DollarSign_Eye_Heart_Play_Plus_Settings_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                href: '/dashboard/influencer/profile',\n                color: 'bg-blue-500'\n            },\n            {\n                title: 'العروض الجديدة',\n                icon: _barrel_optimize_names_ArrowRight_BarChart3_Bell_DollarSign_Eye_Heart_Play_Plus_Settings_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                href: '/dashboard/influencer/offers',\n                color: 'bg-green-500'\n            },\n            {\n                title: 'إدارة المحتوى',\n                icon: _barrel_optimize_names_ArrowRight_BarChart3_Bell_DollarSign_Eye_Heart_Play_Plus_Settings_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                href: '/dashboard/influencer/content',\n                color: 'bg-purple-500'\n            },\n            {\n                title: 'الأرباح والمدفوعات',\n                icon: _barrel_optimize_names_ArrowRight_BarChart3_Bell_DollarSign_Eye_Heart_Play_Plus_Settings_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                href: '/dashboard/influencer/earnings',\n                color: 'bg-yellow-500'\n            }\n        ],\n        'ugc-creator': [\n            {\n                title: 'إدارة الأسعار',\n                icon: _barrel_optimize_names_ArrowRight_BarChart3_Bell_DollarSign_Eye_Heart_Play_Plus_Settings_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                href: '/dashboard/ugc-creator/pricing',\n                color: 'bg-blue-500'\n            },\n            {\n                title: 'الطلبات الجديدة',\n                icon: _barrel_optimize_names_ArrowRight_BarChart3_Bell_DollarSign_Eye_Heart_Play_Plus_Settings_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                href: '/dashboard/ugc-creator/orders',\n                color: 'bg-green-500'\n            },\n            {\n                title: 'معرض الأعمال',\n                icon: _barrel_optimize_names_ArrowRight_BarChart3_Bell_DollarSign_Eye_Heart_Play_Plus_Settings_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                href: '/dashboard/ugc-creator/portfolio',\n                color: 'bg-purple-500'\n            },\n            {\n                title: 'الأرباح',\n                icon: _barrel_optimize_names_ArrowRight_BarChart3_Bell_DollarSign_Eye_Heart_Play_Plus_Settings_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                href: '/dashboard/ugc-creator/earnings',\n                color: 'bg-yellow-500'\n            }\n        ]\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'success':\n                return 'text-green-400';\n            case 'pending':\n                return 'text-yellow-400';\n            case 'active':\n                return 'text-blue-400';\n            case 'info':\n                return 'text-purple-400';\n            default:\n                return 'text-gray-400';\n        }\n    };\n    const getStatusIcon = (type)=>{\n        switch(type){\n            case 'campaign':\n                return '🚀';\n            case 'influencer':\n                return '👥';\n            case 'report':\n                return '📊';\n            case 'payment':\n                return '💰';\n            case 'offer':\n                return '💼';\n            case 'review':\n                return '⭐';\n            case 'follower':\n                return '📈';\n            case 'project':\n                return '🎬';\n            case 'delivery':\n                return '✅';\n            default:\n                return '📝';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-slate-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl font-bold text-white mb-2\",\n                                                    children: [\n                                                        \"مرحباً، \",\n                                                        user.name,\n                                                        \" \",\n                                                        user.avatar\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-slate-400\",\n                                                    children: [\n                                                        userType === 'merchant' && 'إدارة حملاتك الإعلانية والمؤثرين',\n                                                        userType === 'influencer' && 'تابع أداءك وإدارة حملاتك',\n                                                        userType === 'ugc-creator' && 'إدارة مشاريعك وأعمالك الإبداعية'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    variant: \"success\",\n                                                    size: \"sm\",\n                                                    children: \"✅ موثق\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    variant: \"primary\",\n                                                    size: \"sm\",\n                                                    children: [\n                                                        userType === 'merchant' && '🏢 تاجر',\n                                                        userType === 'influencer' && '⭐ مؤثر',\n                                                        userType === 'ugc-creator' && '🎬 مبدع'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            variant: userType === 'merchant' ? 'primary' : 'secondary',\n                                            size: \"sm\",\n                                            onClick: ()=>setUserType('merchant'),\n                                            children: \"تاجر\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            variant: userType === 'influencer' ? 'primary' : 'secondary',\n                                            size: \"sm\",\n                                            onClick: ()=>setUserType('influencer'),\n                                            children: \"مؤثر\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            variant: userType === 'ugc-creator' ? 'primary' : 'secondary',\n                                            size: \"sm\",\n                                            onClick: ()=>setUserType('ugc-creator'),\n                                            children: \"مبدع UGC\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                            children: stats[userType].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-400 text-sm\",\n                                                        children: stat.label\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-white mt-1\",\n                                                        children: stat.value\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `p-3 rounded-lg bg-slate-800 ${stat.color}`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                                    className: \"h-6 w-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-white mb-4\",\n                                                children: \"إجراءات سريعة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: quickActions[userType].map((action, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: action.href,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center p-3 rounded-lg bg-slate-800 hover:bg-slate-700 transition-colors cursor-pointer\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: `p-2 rounded-lg ${action.color} mr-3`,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(action.icon, {\n                                                                        className: \"h-4 w-4 text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 216,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 215,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-white font-medium\",\n                                                                    children: action.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 218,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Bell_DollarSign_Eye_Heart_Play_Plus_Settings_Star_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-slate-400 mr-auto\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 219,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 214,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, index, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-white\",\n                                                        children: \"النشاط الأخير\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        variant: \"secondary\",\n                                                        size: \"sm\",\n                                                        children: \"عرض الكل\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: recentActivities[userType].map((activity, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start p-3 rounded-lg bg-slate-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl mr-3\",\n                                                                children: getStatusIcon(activity.type)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 239,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-white font-medium\",\n                                                                        children: activity.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 243,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-slate-400 text-sm mt-1\",\n                                                                        children: activity.time\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 244,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 242,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: `text-sm font-medium ${getStatusColor(activity.status)}`,\n                                                                children: [\n                                                                    activity.status === 'success' && '✅',\n                                                                    activity.status === 'pending' && '⏳',\n                                                                    activity.status === 'active' && '🔄',\n                                                                    activity.status === 'info' && 'ℹ️'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 246,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-white mb-4\",\n                                        children: \"روابط مفيدة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/help\",\n                                                className: \"text-center p-4 rounded-lg bg-slate-800 hover:bg-slate-700 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl mb-2\",\n                                                        children: \"❓\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white text-sm\",\n                                                        children: \"مركز المساعدة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/support\",\n                                                className: \"text-center p-4 rounded-lg bg-slate-800 hover:bg-slate-700 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl mb-2\",\n                                                        children: \"\\uD83C\\uDFA7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white text-sm\",\n                                                        children: \"الدعم الفني\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/messages\",\n                                                className: \"text-center p-4 rounded-lg bg-slate-800 hover:bg-slate-700 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl mb-2\",\n                                                        children: \"\\uD83D\\uDCAC\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white text-sm\",\n                                                        children: \"الرسائل\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/pricing\",\n                                                className: \"text-center p-4 rounded-lg bg-slate-800 hover:bg-slate-700 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl mb-2\",\n                                                        children: \"\\uD83D\\uDCB0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white text-sm\",\n                                                        children: \"الأسعار\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 286,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_MobileNavigation__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 287,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 132,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DashboardPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Shield_Star_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Shield,Star,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/facebook.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Shield_Star_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Shield,Star,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Shield_Star_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Shield,Star,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/instagram.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Shield_Star_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Shield,Star,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/youtube.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Shield_Star_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Shield,Star,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Shield_Star_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Shield,Star,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Shield_Star_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Shield,Star,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Shield_Star_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Shield,Star,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Shield_Star_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Mail,MapPin,Phone,Shield,Star,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst Footer = ()=>{\n    const currentYear = new Date().getFullYear();\n    const footerLinks = {\n        platform: [\n            {\n                name: 'كيف يعمل',\n                href: '/how-it-works'\n            },\n            {\n                name: 'الأسعار',\n                href: '/pricing'\n            },\n            {\n                name: 'المؤثرين',\n                href: '/influencers'\n            },\n            {\n                name: 'للتجار',\n                href: '/merchants'\n            }\n        ],\n        support: [\n            {\n                name: 'مركز المساعدة',\n                href: '/help'\n            },\n            {\n                name: 'اتصل بنا',\n                href: '/contact'\n            },\n            {\n                name: 'الأسئلة الشائعة',\n                href: '/faq'\n            },\n            {\n                name: 'الدعم الفني',\n                href: '/support'\n            }\n        ],\n        legal: [\n            {\n                name: 'شروط الاستخدام',\n                href: '/terms'\n            },\n            {\n                name: 'سياسة الخصوصية',\n                href: '/privacy'\n            },\n            {\n                name: 'سياسة الاسترداد',\n                href: '/refund'\n            },\n            {\n                name: 'اتفاقية المستخدم',\n                href: '/agreement'\n            }\n        ],\n        company: [\n            {\n                name: 'من نحن',\n                href: '/about'\n            },\n            {\n                name: 'فريق العمل',\n                href: '/team'\n            },\n            {\n                name: 'الوظائف',\n                href: '/careers'\n            },\n            {\n                name: 'الأخبار',\n                href: '/news'\n            }\n        ]\n    };\n    const socialLinks = [\n        {\n            name: 'Facebook',\n            icon: _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Shield_Star_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            href: '#'\n        },\n        {\n            name: 'Twitter',\n            icon: _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Shield_Star_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            href: '#'\n        },\n        {\n            name: 'Instagram',\n            icon: _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Shield_Star_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            href: '#'\n        },\n        {\n            name: 'YouTube',\n            icon: _barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Shield_Star_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            href: '#'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-slate-900 text-white border-t border-purple-500/20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto mobile-container py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center glow-effect\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl\",\n                                                children: \"\\uD83D\\uDE80\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 53,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 52,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-3 text-xl font-bold\",\n                                            children: \"منصة المؤثرين السعودية\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 mb-6 leading-relaxed\",\n                                    children: \"منصة احترافية تربط المؤثرين بالتجار في المملكة العربية السعودية \\uD83C\\uDDF8\\uD83C\\uDDE6 نوفر بيئة آمنة وموثوقة للتعاون التجاري مع ضمان الأموال ونظام الدفع الآمن ✨\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"trust-indicator\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Shield_Star_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-3 h-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 65,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"\\uD83D\\uDEE1️ منصة سعودية موثوقة\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"trust-indicator\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Shield_Star_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-3 h-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"⭐ معتمدة ومرخصة\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-gray-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Shield_Star_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-5 w-5 ml-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 77,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"<EMAIL>\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 78,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-gray-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Shield_Star_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-5 w-5 ml-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"+966 11 123 4567\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 82,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-gray-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Instagram_Mail_MapPin_Phone_Shield_Star_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-5 w-5 ml-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 85,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"الرياض، المملكة العربية السعودية\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 86,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"المنصة\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-3\",\n                                    children: footerLinks.platform.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: link.href,\n                                                className: \"text-gray-300 hover:text-white transition-colors duration-200\",\n                                                children: link.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, link.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"الدعم\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-3\",\n                                    children: footerLinks.support.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: link.href,\n                                                className: \"text-gray-300 hover:text-white transition-colors duration-200\",\n                                                children: link.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, link.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"قانوني\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-3\",\n                                    children: footerLinks.legal.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: link.href,\n                                                className: \"text-gray-300 hover:text-white transition-colors duration-200\",\n                                                children: link.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, link.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-gray-800 mt-12 pt-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-6 space-x-reverse mb-4 md:mb-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-300 font-medium\",\n                                        children: \"تابعنا على:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    socialLinks.map((social)=>{\n                                        const Icon = social.icon;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: social.href,\n                                            className: \"text-gray-400 hover:text-white transition-colors duration-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                className: \"h-6 w-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, social.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 19\n                                        }, undefined);\n                                    })\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-300 font-medium\",\n                                        children: \"اشترك في النشرة الإخبارية:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"email\",\n                                                placeholder: \"البريد الإلكتروني\",\n                                                className: \"px-4 py-2 bg-gray-800 border border-gray-700 rounded-r-lg focus:outline-none focus:ring-2 focus:ring-primary-500 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"px-4 py-2 bg-primary-600 hover:bg-primary-700 rounded-l-lg transition-colors duration-200\",\n                                                children: \"اشتراك\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-gray-800 mt-8 pt-8 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: [\n                            \"\\xa9 \",\n                            currentYear,\n                            \" منصة المؤثرين السعودية. جميع الحقوق محفوظة.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n            lineNumber: 46,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst Header = ()=>{\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const navigation = [\n        {\n            name: 'الرئيسية',\n            href: '/'\n        },\n        {\n            name: 'المؤثرين',\n            href: '/influencers'\n        },\n        {\n            name: 'مبدعي UGC',\n            href: '/ugc-creators'\n        },\n        {\n            name: 'الرسائل',\n            href: '/messages'\n        },\n        {\n            name: 'كيف يعمل',\n            href: '/how-it-works'\n        },\n        {\n            name: 'الأسعار',\n            href: '/pricing'\n        },\n        {\n            name: 'اتصل بنا',\n            href: '/contact'\n        }\n    ];\n    const isActive = (href)=>pathname === href;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-slate-900/95 backdrop-blur-md border-b border-slate-700/50 sticky top-0 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16 md:h-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-indigo-600 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-bold text-lg\",\n                                            children: \"م\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 33,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 32,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mr-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xl font-bold text-white\",\n                                                children: \"منصة المؤثرين\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 36,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-slate-400 hidden sm:block\",\n                                                children: \"منصة سعودية موثوقة\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 37,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 35,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 31,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"hidden md:flex space-x-8 space-x-reverse\",\n                            children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: `px-3 py-2 rounded-lg text-sm font-medium transition-colors ${isActive(item.href) ? 'text-indigo-400 bg-indigo-500/10' : 'text-slate-300 hover:text-white hover:bg-slate-800'}`,\n                                    children: item.name\n                                }, item.name, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-3 space-x-reverse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/login\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"btn-secondary\",\n                                        children: \"تسجيل الدخول\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/register\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"btn-primary\",\n                                        children: \"إنشاء حساب\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                className: \"p-2 rounded-lg text-slate-400 hover:text-white hover:bg-slate-800 transition-colors\",\n                                children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, undefined),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 pt-4 pb-6 space-y-2 bg-slate-800/95 backdrop-blur-md border-t border-slate-700/50\",\n                        children: [\n                            navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: `block px-4 py-3 rounded-lg text-base font-medium transition-colors ${isActive(item.href) ? 'text-indigo-400 bg-indigo-500/10' : 'text-slate-300 hover:text-white hover:bg-slate-700'}`,\n                                    onClick: ()=>setIsMenuOpen(false),\n                                    children: item.name\n                                }, item.name, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 17\n                                }, undefined)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-4 border-t border-slate-700/50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/login\",\n                                            onClick: ()=>setIsMenuOpen(false),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"btn-secondary w-full\",\n                                                children: \"تسجيل الدخول\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 111,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/register\",\n                                            onClick: ()=>setIsMenuOpen(false),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"btn-primary w-full\",\n                                                children: \"إنشاء حساب\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvSGVhZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUV1QztBQUNYO0FBQ2lCO0FBRTJCO0FBRXhFLE1BQU1NLFNBQW1CO0lBQ3ZCLE1BQU0sQ0FBQ0MsWUFBWUMsY0FBYyxHQUFHUCwrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNUSxXQUFXTiw0REFBV0E7SUFFNUIsTUFBTU8sYUFBYTtRQUNqQjtZQUFFQyxNQUFNO1lBQVlDLE1BQU07UUFBSTtRQUM5QjtZQUFFRCxNQUFNO1lBQVlDLE1BQU07UUFBZTtRQUN6QztZQUFFRCxNQUFNO1lBQWFDLE1BQU07UUFBZ0I7UUFDM0M7WUFBRUQsTUFBTTtZQUFXQyxNQUFNO1FBQVk7UUFDckM7WUFBRUQsTUFBTTtZQUFZQyxNQUFNO1FBQWdCO1FBQzFDO1lBQUVELE1BQU07WUFBV0MsTUFBTTtRQUFXO1FBQ3BDO1lBQUVELE1BQU07WUFBWUMsTUFBTTtRQUFXO0tBQ3RDO0lBRUQsTUFBTUMsV0FBVyxDQUFDRCxPQUFpQkgsYUFBYUc7SUFFaEQscUJBQ0UsOERBQUNFO1FBQU9DLFdBQVU7a0JBQ2hCLDRFQUFDQztZQUFJRCxXQUFVOzs4QkFDYiw4REFBQ0M7b0JBQUlELFdBQVU7O3NDQUViLDhEQUFDQzs0QkFBSUQsV0FBVTtzQ0FDYiw0RUFBQ2Isa0RBQUlBO2dDQUFDVSxNQUFLO2dDQUFJRyxXQUFVOztrREFDdkIsOERBQUNDO3dDQUFJRCxXQUFVO2tEQUNiLDRFQUFDRTs0Q0FBS0YsV0FBVTtzREFBK0I7Ozs7Ozs7Ozs7O2tEQUVqRCw4REFBQ0M7d0NBQUlELFdBQVU7OzBEQUNiLDhEQUFDRTtnREFBS0YsV0FBVTswREFBK0I7Ozs7OzswREFDL0MsOERBQUNDO2dEQUFJRCxXQUFVOzBEQUF5Qzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBTTlELDhEQUFDRzs0QkFBSUgsV0FBVTtzQ0FDWkwsV0FBV1MsR0FBRyxDQUFDLENBQUNDLHFCQUNmLDhEQUFDbEIsa0RBQUlBO29DQUVIVSxNQUFNUSxLQUFLUixJQUFJO29DQUNmRyxXQUFXLENBQUMsMkRBQTJELEVBQ3JFRixTQUFTTyxLQUFLUixJQUFJLElBQ2QscUNBQ0Esc0RBQ0o7OENBRURRLEtBQUtULElBQUk7bUNBUkxTLEtBQUtULElBQUk7Ozs7Ozs7Ozs7c0NBY3BCLDhEQUFDSzs0QkFBSUQsV0FBVTs7OENBQ2IsOERBQUNiLGtEQUFJQTtvQ0FBQ1UsTUFBSzs4Q0FDVCw0RUFBQ1M7d0NBQU9OLFdBQVU7a0RBQWdCOzs7Ozs7Ozs7Ozs4Q0FJcEMsOERBQUNiLGtEQUFJQTtvQ0FBQ1UsTUFBSzs4Q0FDVCw0RUFBQ1M7d0NBQU9OLFdBQVU7a0RBQWM7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU9wQyw4REFBQ0M7NEJBQUlELFdBQVU7c0NBQ2IsNEVBQUNNO2dDQUNDQyxTQUFTLElBQU1kLGNBQWMsQ0FBQ0Q7Z0NBQzlCUSxXQUFVOzBDQUVUUiwyQkFDQyw4REFBQ0Ysa0ZBQUNBO29DQUFDVSxXQUFVOzs7Ozs4REFFYiw4REFBQ1gsa0ZBQUlBO29DQUFDVyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2dCQU92QlIsNEJBQ0MsOERBQUNTO29CQUFJRCxXQUFVOzhCQUNiLDRFQUFDQzt3QkFBSUQsV0FBVTs7NEJBRVpMLFdBQVdTLEdBQUcsQ0FBQyxDQUFDQyxxQkFDZiw4REFBQ2xCLGtEQUFJQTtvQ0FFSFUsTUFBTVEsS0FBS1IsSUFBSTtvQ0FDZkcsV0FBVyxDQUFDLG1FQUFtRSxFQUM3RUYsU0FBU08sS0FBS1IsSUFBSSxJQUNkLHFDQUNBLHNEQUNKO29DQUNGVSxTQUFTLElBQU1kLGNBQWM7OENBRTVCWSxLQUFLVCxJQUFJO21DQVRMUyxLQUFLVCxJQUFJOzs7OzswQ0FhbEIsOERBQUNLO2dDQUFJRCxXQUFVOzBDQUNiLDRFQUFDQztvQ0FBSUQsV0FBVTs7c0RBQ2IsOERBQUNiLGtEQUFJQTs0Q0FBQ1UsTUFBSzs0Q0FBU1UsU0FBUyxJQUFNZCxjQUFjO3NEQUMvQyw0RUFBQ2E7Z0RBQU9OLFdBQVU7MERBQXVCOzs7Ozs7Ozs7OztzREFJM0MsOERBQUNiLGtEQUFJQTs0Q0FBQ1UsTUFBSzs0Q0FBWVUsU0FBUyxJQUFNZCxjQUFjO3NEQUNsRCw0RUFBQ2E7Z0RBQU9OLFdBQVU7MERBQXFCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVkzRDtBQUVBLGlFQUFlVCxNQUFNQSxFQUFBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNobTcxXFxEZXNrdG9wXFxhbXNob3IyXFxzcmNcXGNvbXBvbmVudHNcXGxheW91dFxcSGVhZGVyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnXG5pbXBvcnQgeyB1c2VQYXRobmFtZSB9IGZyb20gJ25leHQvbmF2aWdhdGlvbidcbmltcG9ydCBCdXR0b24gZnJvbSAnLi4vdWkvQnV0dG9uJ1xuaW1wb3J0IHsgTWVudSwgWCwgVXNlciwgU2VhcmNoLCBCZWxsLCBTaGllbGQsIFN0YXIgfSBmcm9tICdsdWNpZGUtcmVhY3QnXG5cbmNvbnN0IEhlYWRlcjogUmVhY3QuRkMgPSAoKSA9PiB7XG4gIGNvbnN0IFtpc01lbnVPcGVuLCBzZXRJc01lbnVPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBwYXRobmFtZSA9IHVzZVBhdGhuYW1lKClcblxuICBjb25zdCBuYXZpZ2F0aW9uID0gW1xuICAgIHsgbmFtZTogJ9in2YTYsdim2YrYs9mK2KknLCBocmVmOiAnLycgfSxcbiAgICB7IG5hbWU6ICfYp9mE2YXYpNir2LHZitmGJywgaHJlZjogJy9pbmZsdWVuY2VycycgfSxcbiAgICB7IG5hbWU6ICfZhdio2K/YudmKIFVHQycsIGhyZWY6ICcvdWdjLWNyZWF0b3JzJyB9LFxuICAgIHsgbmFtZTogJ9in2YTYsdiz2KfYptmEJywgaHJlZjogJy9tZXNzYWdlcycgfSxcbiAgICB7IG5hbWU6ICfZg9mK2YEg2YrYudmF2YQnLCBocmVmOiAnL2hvdy1pdC13b3JrcycgfSxcbiAgICB7IG5hbWU6ICfYp9mE2KPYs9i52KfYsScsIGhyZWY6ICcvcHJpY2luZycgfSxcbiAgICB7IG5hbWU6ICfYp9iq2LXZhCDYqNmG2KcnLCBocmVmOiAnL2NvbnRhY3QnIH0sXG4gIF1cblxuICBjb25zdCBpc0FjdGl2ZSA9IChocmVmOiBzdHJpbmcpID0+IHBhdGhuYW1lID09PSBocmVmXG5cbiAgcmV0dXJuIChcbiAgICA8aGVhZGVyIGNsYXNzTmFtZT1cImJnLXNsYXRlLTkwMC85NSBiYWNrZHJvcC1ibHVyLW1kIGJvcmRlci1iIGJvcmRlci1zbGF0ZS03MDAvNTAgc3RpY2t5IHRvcC0wIHotNTBcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LThcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgaC0xNiBtZDpoLTIwXCI+XG4gICAgICAgICAgey8qIExvZ28gKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LXNocmluay0wXCI+XG4gICAgICAgICAgICA8TGluayBocmVmPVwiL1wiIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMCBoLTEwIGJnLWluZGlnby02MDAgcm91bmRlZC1sZyBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1ib2xkIHRleHQtbGdcIj7ZhTwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXItM1wiPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1ib2xkIHRleHQtd2hpdGVcIj7ZhdmG2LXYqSDYp9mE2YXYpNir2LHZitmGPC9zcGFuPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXNsYXRlLTQwMCBoaWRkZW4gc206YmxvY2tcIj7ZhdmG2LXYqSDYs9i52YjYr9mK2Kkg2YXZiNir2YjZgtipPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIERlc2t0b3AgTmF2aWdhdGlvbiAqL31cbiAgICAgICAgICA8bmF2IGNsYXNzTmFtZT1cImhpZGRlbiBtZDpmbGV4IHNwYWNlLXgtOCBzcGFjZS14LXJldmVyc2VcIj5cbiAgICAgICAgICAgIHtuYXZpZ2F0aW9uLm1hcCgoaXRlbSkgPT4gKFxuICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgIGtleT17aXRlbS5uYW1lfVxuICAgICAgICAgICAgICAgIGhyZWY9e2l0ZW0uaHJlZn1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BweC0zIHB5LTIgcm91bmRlZC1sZyB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tY29sb3JzICR7XG4gICAgICAgICAgICAgICAgICBpc0FjdGl2ZShpdGVtLmhyZWYpXG4gICAgICAgICAgICAgICAgICAgID8gJ3RleHQtaW5kaWdvLTQwMCBiZy1pbmRpZ28tNTAwLzEwJ1xuICAgICAgICAgICAgICAgICAgICA6ICd0ZXh0LXNsYXRlLTMwMCBob3Zlcjp0ZXh0LXdoaXRlIGhvdmVyOmJnLXNsYXRlLTgwMCdcbiAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHtpdGVtLm5hbWV9XG4gICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvbmF2PlxuXG4gICAgICAgICAgey8qIERlc2t0b3AgQWN0aW9ucyAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImhpZGRlbiBtZDpmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTMgc3BhY2UteC1yZXZlcnNlXCI+XG4gICAgICAgICAgICA8TGluayBocmVmPVwiL2xvZ2luXCI+XG4gICAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwiYnRuLXNlY29uZGFyeVwiPlxuICAgICAgICAgICAgICAgINiq2LPYrNmK2YQg2KfZhNiv2K7ZiNmEXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9yZWdpc3RlclwiPlxuICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cImJ0bi1wcmltYXJ5XCI+XG4gICAgICAgICAgICAgICAg2KXZhti02KfYoSDYrdiz2KfYqFxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBNb2JpbGUgbWVudSBidXR0b24gKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtZDpoaWRkZW5cIj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNNZW51T3BlbighaXNNZW51T3Blbil9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMiByb3VuZGVkLWxnIHRleHQtc2xhdGUtNDAwIGhvdmVyOnRleHQtd2hpdGUgaG92ZXI6Ymctc2xhdGUtODAwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge2lzTWVudU9wZW4gPyAoXG4gICAgICAgICAgICAgICAgPFggY2xhc3NOYW1lPVwiaC02IHctNlwiIC8+XG4gICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgPE1lbnUgY2xhc3NOYW1lPVwiaC02IHctNlwiIC8+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIE1vYmlsZSBOYXZpZ2F0aW9uICovfVxuICAgICAgICB7aXNNZW51T3BlbiAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtZDpoaWRkZW5cIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtNCBwdC00IHBiLTYgc3BhY2UteS0yIGJnLXNsYXRlLTgwMC85NSBiYWNrZHJvcC1ibHVyLW1kIGJvcmRlci10IGJvcmRlci1zbGF0ZS03MDAvNTBcIj5cblxuICAgICAgICAgICAgICB7bmF2aWdhdGlvbi5tYXAoKGl0ZW0pID0+IChcbiAgICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgICAga2V5PXtpdGVtLm5hbWV9XG4gICAgICAgICAgICAgICAgICBocmVmPXtpdGVtLmhyZWZ9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BibG9jayBweC00IHB5LTMgcm91bmRlZC1sZyB0ZXh0LWJhc2UgZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1jb2xvcnMgJHtcbiAgICAgICAgICAgICAgICAgICAgaXNBY3RpdmUoaXRlbS5ocmVmKVxuICAgICAgICAgICAgICAgICAgICAgID8gJ3RleHQtaW5kaWdvLTQwMCBiZy1pbmRpZ28tNTAwLzEwJ1xuICAgICAgICAgICAgICAgICAgICAgIDogJ3RleHQtc2xhdGUtMzAwIGhvdmVyOnRleHQtd2hpdGUgaG92ZXI6Ymctc2xhdGUtNzAwJ1xuICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc01lbnVPcGVuKGZhbHNlKX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICB7aXRlbS5uYW1lfVxuICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgKSl9XG5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwdC00IGJvcmRlci10IGJvcmRlci1zbGF0ZS03MDAvNTBcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9sb2dpblwiIG9uQ2xpY2s9eygpID0+IHNldElzTWVudU9wZW4oZmFsc2UpfT5cbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJidG4tc2Vjb25kYXJ5IHctZnVsbFwiPlxuICAgICAgICAgICAgICAgICAgICAgINiq2LPYrNmK2YQg2KfZhNiv2K7ZiNmEXG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9yZWdpc3RlclwiIG9uQ2xpY2s9eygpID0+IHNldElzTWVudU9wZW4oZmFsc2UpfT5cbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJidG4tcHJpbWFyeSB3LWZ1bGxcIj5cbiAgICAgICAgICAgICAgICAgICAgICDYpdmG2LTYp9ihINit2LPYp9ioXG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuICAgICAgPC9kaXY+XG4gICAgPC9oZWFkZXI+XG4gIClcbn1cblxuZXhwb3J0IGRlZmF1bHQgSGVhZGVyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsIkxpbmsiLCJ1c2VQYXRobmFtZSIsIk1lbnUiLCJYIiwiSGVhZGVyIiwiaXNNZW51T3BlbiIsInNldElzTWVudU9wZW4iLCJwYXRobmFtZSIsIm5hdmlnYXRpb24iLCJuYW1lIiwiaHJlZiIsImlzQWN0aXZlIiwiaGVhZGVyIiwiY2xhc3NOYW1lIiwiZGl2Iiwic3BhbiIsIm5hdiIsIm1hcCIsIml0ZW0iLCJidXR0b24iLCJvbkNsaWNrIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/MobileNavigation.tsx":
/*!****************************************************!*\
  !*** ./src/components/layout/MobileNavigation.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Home_MessageCircle_Search_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Home,MessageCircle,Search,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_Home_MessageCircle_Search_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Home,MessageCircle,Search,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Home_MessageCircle_Search_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Home,MessageCircle,Search,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Home_MessageCircle_Search_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Home,MessageCircle,Search,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Home_MessageCircle_Search_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Home,MessageCircle,Search,User,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst MobileNavigation = ()=>{\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const navigationItems = [\n        {\n            name: 'الرئيسية',\n            href: '/',\n            icon: _barrel_optimize_names_Home_MessageCircle_Search_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            color: 'text-green-600'\n        },\n        {\n            name: 'المؤثرين',\n            href: '/influencers',\n            icon: _barrel_optimize_names_Home_MessageCircle_Search_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            color: 'text-blue-600'\n        },\n        {\n            name: 'البحث',\n            href: '/search',\n            icon: _barrel_optimize_names_Home_MessageCircle_Search_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            color: 'text-purple-600'\n        },\n        {\n            name: 'الرسائل',\n            href: '/messages',\n            icon: _barrel_optimize_names_Home_MessageCircle_Search_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            color: 'text-orange-600'\n        },\n        {\n            name: 'حسابي',\n            href: '/profile',\n            icon: _barrel_optimize_names_Home_MessageCircle_Search_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            color: 'text-gray-600'\n        }\n    ];\n    const isActive = (href)=>{\n        if (href === '/') {\n            return pathname === '/';\n        }\n        return pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-20 md:hidden\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"md:hidden fixed bottom-0 left-0 right-0 bg-slate-900/95 backdrop-blur-md border-t border-slate-700/50 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-around py-2\",\n                    children: navigationItems.map((item)=>{\n                        const Icon = item.icon;\n                        const active = isActive(item.href);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: item.href,\n                            className: \"flex-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center justify-center py-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `p-2 rounded-lg transition-colors ${active ? 'bg-indigo-500/10' : 'hover:bg-slate-800'}`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: `h-5 w-5 transition-colors ${active ? 'text-indigo-400' : 'text-slate-400'}`\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: `text-xs font-medium mt-1 transition-colors ${active ? 'text-indigo-400' : 'text-slate-400'}`,\n                                        children: item.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 17\n                            }, undefined)\n                        }, item.name, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MobileNavigation);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvTW9iaWxlTmF2aWdhdGlvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFeUI7QUFDRztBQUNpQjtBQVd4QjtBQUVyQixNQUFNUSxtQkFBNkI7SUFDakMsTUFBTUMsV0FBV1AsNERBQVdBO0lBRTVCLE1BQU1RLGtCQUFrQjtRQUN0QjtZQUNFQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsTUFBTVYsZ0hBQUlBO1lBQ1ZXLE9BQU87UUFDVDtRQUNBO1lBQ0VILE1BQU07WUFDTkMsTUFBTTtZQUNOQyxNQUFNVCxnSEFBS0E7WUFDWFUsT0FBTztRQUNUO1FBQ0E7WUFDRUgsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLE1BQU1SLGdIQUFNQTtZQUNaUyxPQUFPO1FBQ1Q7UUFDQTtZQUNFSCxNQUFNO1lBQ05DLE1BQU07WUFDTkMsTUFBTVAsZ0hBQWFBO1lBQ25CUSxPQUFPO1FBQ1Q7UUFDQTtZQUNFSCxNQUFNO1lBQ05DLE1BQU07WUFDTkMsTUFBTU4sZ0hBQUlBO1lBQ1ZPLE9BQU87UUFDVDtLQUNEO0lBRUQsTUFBTUMsV0FBVyxDQUFDSDtRQUNoQixJQUFJQSxTQUFTLEtBQUs7WUFDaEIsT0FBT0gsYUFBYTtRQUN0QjtRQUNBLE9BQU9BLFNBQVNPLFVBQVUsQ0FBQ0o7SUFDN0I7SUFFQSxxQkFDRTs7MEJBRUUsOERBQUNLO2dCQUFJQyxXQUFVOzs7Ozs7MEJBR2YsOERBQUNDO2dCQUFJRCxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTs4QkFDWlIsZ0JBQWdCVSxHQUFHLENBQUMsQ0FBQ0M7d0JBQ3BCLE1BQU1DLE9BQU9ELEtBQUtSLElBQUk7d0JBQ3RCLE1BQU1VLFNBQVNSLFNBQVNNLEtBQUtULElBQUk7d0JBRWpDLHFCQUNFLDhEQUFDWCxrREFBSUE7NEJBRUhXLE1BQU1TLEtBQUtULElBQUk7NEJBQ2ZNLFdBQVU7c0NBRVYsNEVBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVcsQ0FBQyxpQ0FBaUMsRUFDaERLLFNBQ0kscUJBQ0Esc0JBQ0o7a0RBQ0EsNEVBQUNEOzRDQUNDSixXQUFXLENBQUMsMEJBQTBCLEVBQ3BDSyxTQUNJLG9CQUNBLGtCQUNKOzs7Ozs7Ozs7OztrREFHTiw4REFBQ0M7d0NBQUtOLFdBQVcsQ0FBQywyQ0FBMkMsRUFDM0RLLFNBQ0ksb0JBQ0Esa0JBQ0o7a0RBQ0NGLEtBQUtWLElBQUk7Ozs7Ozs7Ozs7OzsyQkF2QlRVLEtBQUtWLElBQUk7Ozs7O29CQTRCcEI7Ozs7Ozs7Ozs7Ozs7QUFLVjtBQUVBLGlFQUFlSCxnQkFBZ0JBLEVBQUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc2htNzFcXERlc2t0b3BcXGFtc2hvcjJcXHNyY1xcY29tcG9uZW50c1xcbGF5b3V0XFxNb2JpbGVOYXZpZ2F0aW9uLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0J1xuaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJ1xuaW1wb3J0IHsgdXNlUGF0aG5hbWUgfSBmcm9tICduZXh0L25hdmlnYXRpb24nXG5pbXBvcnQge1xuICBIb21lLFxuICBVc2VycyxcbiAgU2VhcmNoLFxuICBNZXNzYWdlQ2lyY2xlLFxuICBVc2VyLFxuICBTdG9yZSxcbiAgVHJlbmRpbmdVcCxcbiAgSGVhcnQsXG4gIEJlbGxcbn0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuXG5jb25zdCBNb2JpbGVOYXZpZ2F0aW9uOiBSZWFjdC5GQyA9ICgpID0+IHtcbiAgY29uc3QgcGF0aG5hbWUgPSB1c2VQYXRobmFtZSgpXG5cbiAgY29uc3QgbmF2aWdhdGlvbkl0ZW1zID0gW1xuICAgIHtcbiAgICAgIG5hbWU6ICfYp9mE2LHYptmK2LPZitipJyxcbiAgICAgIGhyZWY6ICcvJyxcbiAgICAgIGljb246IEhvbWUsXG4gICAgICBjb2xvcjogJ3RleHQtZ3JlZW4tNjAwJ1xuICAgIH0sXG4gICAge1xuICAgICAgbmFtZTogJ9in2YTZhdik2KvYsdmK2YYnLFxuICAgICAgaHJlZjogJy9pbmZsdWVuY2VycycsXG4gICAgICBpY29uOiBVc2VycyxcbiAgICAgIGNvbG9yOiAndGV4dC1ibHVlLTYwMCdcbiAgICB9LFxuICAgIHtcbiAgICAgIG5hbWU6ICfYp9mE2KjYrdirJyxcbiAgICAgIGhyZWY6ICcvc2VhcmNoJyxcbiAgICAgIGljb246IFNlYXJjaCxcbiAgICAgIGNvbG9yOiAndGV4dC1wdXJwbGUtNjAwJ1xuICAgIH0sXG4gICAge1xuICAgICAgbmFtZTogJ9in2YTYsdiz2KfYptmEJyxcbiAgICAgIGhyZWY6ICcvbWVzc2FnZXMnLFxuICAgICAgaWNvbjogTWVzc2FnZUNpcmNsZSxcbiAgICAgIGNvbG9yOiAndGV4dC1vcmFuZ2UtNjAwJ1xuICAgIH0sXG4gICAge1xuICAgICAgbmFtZTogJ9it2LPYp9io2YonLFxuICAgICAgaHJlZjogJy9wcm9maWxlJyxcbiAgICAgIGljb246IFVzZXIsXG4gICAgICBjb2xvcjogJ3RleHQtZ3JheS02MDAnXG4gICAgfVxuICBdXG5cbiAgY29uc3QgaXNBY3RpdmUgPSAoaHJlZjogc3RyaW5nKSA9PiB7XG4gICAgaWYgKGhyZWYgPT09ICcvJykge1xuICAgICAgcmV0dXJuIHBhdGhuYW1lID09PSAnLydcbiAgICB9XG4gICAgcmV0dXJuIHBhdGhuYW1lLnN0YXJ0c1dpdGgoaHJlZilcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPD5cbiAgICAgIHsvKiDZhdiz2KfYrdipINmB2KfYsdi62Kkg2YTYqtis2YbYqCDYqtiv2KfYrtmEINin2YTZhdit2KrZiNmJINmF2Lkg2KfZhNiq2YbZgtmEINin2YTYs9mB2YTZiiAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC0yMCBtZDpoaWRkZW5cIj48L2Rpdj5cblxuICAgICAgey8qINi02LHZiti3INin2YTYqtmG2YLZhCDYp9mE2LPZgdmE2YogKi99XG4gICAgICA8bmF2IGNsYXNzTmFtZT1cIm1kOmhpZGRlbiBmaXhlZCBib3R0b20tMCBsZWZ0LTAgcmlnaHQtMCBiZy1zbGF0ZS05MDAvOTUgYmFja2Ryb3AtYmx1ci1tZCBib3JkZXItdCBib3JkZXItc2xhdGUtNzAwLzUwIHotNTBcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWFyb3VuZCBweS0yXCI+XG4gICAgICAgICAge25hdmlnYXRpb25JdGVtcy5tYXAoKGl0ZW0pID0+IHtcbiAgICAgICAgICAgIGNvbnN0IEljb24gPSBpdGVtLmljb25cbiAgICAgICAgICAgIGNvbnN0IGFjdGl2ZSA9IGlzQWN0aXZlKGl0ZW0uaHJlZilcblxuICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICBrZXk9e2l0ZW0ubmFtZX1cbiAgICAgICAgICAgICAgICBocmVmPXtpdGVtLmhyZWZ9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC0xXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcHktM1wiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BwLTIgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWNvbG9ycyAke1xuICAgICAgICAgICAgICAgICAgICBhY3RpdmVcbiAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1pbmRpZ28tNTAwLzEwJ1xuICAgICAgICAgICAgICAgICAgICAgIDogJ2hvdmVyOmJnLXNsYXRlLTgwMCdcbiAgICAgICAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAgICAgICAgPEljb25cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BoLTUgdy01IHRyYW5zaXRpb24tY29sb3JzICR7XG4gICAgICAgICAgICAgICAgICAgICAgICBhY3RpdmVcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPyAndGV4dC1pbmRpZ28tNDAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICA6ICd0ZXh0LXNsYXRlLTQwMCdcbiAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgdGV4dC14cyBmb250LW1lZGl1bSBtdC0xIHRyYW5zaXRpb24tY29sb3JzICR7XG4gICAgICAgICAgICAgICAgICAgIGFjdGl2ZVxuICAgICAgICAgICAgICAgICAgICAgID8gJ3RleHQtaW5kaWdvLTQwMCdcbiAgICAgICAgICAgICAgICAgICAgICA6ICd0ZXh0LXNsYXRlLTQwMCdcbiAgICAgICAgICAgICAgICAgIH1gfT5cbiAgICAgICAgICAgICAgICAgICAge2l0ZW0ubmFtZX1cbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgKVxuICAgICAgICAgIH0pfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvbmF2PlxuICAgIDwvPlxuICApXG59XG5cbmV4cG9ydCBkZWZhdWx0IE1vYmlsZU5hdmlnYXRpb25cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkxpbmsiLCJ1c2VQYXRobmFtZSIsIkhvbWUiLCJVc2VycyIsIlNlYXJjaCIsIk1lc3NhZ2VDaXJjbGUiLCJVc2VyIiwiTW9iaWxlTmF2aWdhdGlvbiIsInBhdGhuYW1lIiwibmF2aWdhdGlvbkl0ZW1zIiwibmFtZSIsImhyZWYiLCJpY29uIiwiY29sb3IiLCJpc0FjdGl2ZSIsInN0YXJ0c1dpdGgiLCJkaXYiLCJjbGFzc05hbWUiLCJuYXYiLCJtYXAiLCJpdGVtIiwiSWNvbiIsImFjdGl2ZSIsInNwYW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/MobileNavigation.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/Badge.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst Badge = ({ children, variant = 'primary', size = 'md', className = '' })=>{\n    const variantClasses = {\n        primary: 'badge-primary',\n        secondary: 'bg-slate-500/10 text-slate-400 border-slate-500/20',\n        success: 'badge-success',\n        warning: 'badge-warning',\n        error: 'bg-red-500/10 text-red-400 border-red-500/20'\n    };\n    const sizeClasses = {\n        sm: 'text-xs px-2 py-1',\n        md: 'text-sm px-3 py-1',\n        lg: 'text-base px-4 py-2'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)('badge', variantClasses[variant], sizeClasses[size], className),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\ui\\\\Badge.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Badge);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Badge.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/Button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst Button = ({ variant = 'primary', size = 'md', isLoading = false, children, className, disabled, ...props })=>{\n    const variantClasses = {\n        primary: 'btn-primary',\n        secondary: 'btn-secondary',\n        outline: 'btn-outline',\n        ghost: 'bg-transparent text-slate-400 hover:text-slate-200 hover:bg-slate-800/50',\n        danger: 'bg-red-600 hover:bg-red-700 text-white border-red-600'\n    };\n    const sizeClasses = {\n        sm: 'text-sm px-3 py-2 min-h-[40px]',\n        md: 'text-sm px-4 py-3 min-h-[44px]',\n        lg: 'text-base px-6 py-3 min-h-[48px]',\n        xl: 'text-lg px-8 py-4 min-h-[52px]'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(variantClasses[variant], sizeClasses[size], disabled && 'opacity-50 cursor-not-allowed', className),\n        disabled: disabled || isLoading,\n        ...props,\n        children: [\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-white\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                        className: \"opacity-25\",\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"10\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        className: \"opacity-75\",\n                        fill: \"currentColor\",\n                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n                lineNumber: 49,\n                columnNumber: 9\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Button);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/Card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst Card = ({ children, className, hover = false, padding = 'md', shadow = 'md' })=>{\n    const baseClasses = 'card';\n    const paddingClasses = {\n        none: 'p-0',\n        sm: 'p-3',\n        md: 'p-5',\n        lg: 'p-6',\n        xl: 'p-8'\n    };\n    const shadowClasses = {\n        none: '',\n        sm: 'shadow-sm',\n        md: 'shadow-md',\n        lg: 'shadow-lg',\n        xl: 'shadow-xl'\n    };\n    const hoverClasses = hover ? 'card-interactive' : '';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__.clsx)(baseClasses, paddingClasses[padding], shadowClasses[shadow], hoverClasses, className),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Card);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9DYXJkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRXlCO0FBQ0U7QUFVM0IsTUFBTUUsT0FBNEIsQ0FBQyxFQUNqQ0MsUUFBUSxFQUNSQyxTQUFTLEVBQ1RDLFFBQVEsS0FBSyxFQUNiQyxVQUFVLElBQUksRUFDZEMsU0FBUyxJQUFJLEVBQ2Q7SUFDQyxNQUFNQyxjQUFjO0lBRXBCLE1BQU1DLGlCQUFpQjtRQUNyQkMsTUFBTTtRQUNOQyxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsSUFBSTtRQUNKQyxJQUFJO0lBQ047SUFFQSxNQUFNQyxnQkFBZ0I7UUFDcEJMLE1BQU07UUFDTkMsSUFBSTtRQUNKQyxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsSUFBSTtJQUNOO0lBRUEsTUFBTUUsZUFBZVgsUUFBUSxxQkFBcUI7SUFFbEQscUJBQ0UsOERBQUNZO1FBQ0NiLFdBQVdILDBDQUFJQSxDQUNiTyxhQUNBQyxjQUFjLENBQUNILFFBQVEsRUFDdkJTLGFBQWEsQ0FBQ1IsT0FBTyxFQUNyQlMsY0FDQVo7a0JBR0REOzs7Ozs7QUFHUDtBQUVBLGlFQUFlRCxJQUFJQSxFQUFBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNobTcxXFxEZXNrdG9wXFxhbXNob3IyXFxzcmNcXGNvbXBvbmVudHNcXHVpXFxDYXJkLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgY2xzeCB9IGZyb20gJ2Nsc3gnXG5cbmludGVyZmFjZSBDYXJkUHJvcHMge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG4gIGNsYXNzTmFtZT86IHN0cmluZ1xuICBob3Zlcj86IGJvb2xlYW5cbiAgcGFkZGluZz86ICdub25lJyB8ICdzbScgfCAnbWQnIHwgJ2xnJyB8ICd4bCdcbiAgc2hhZG93PzogJ25vbmUnIHwgJ3NtJyB8ICdtZCcgfCAnbGcnIHwgJ3hsJ1xufVxuXG5jb25zdCBDYXJkOiBSZWFjdC5GQzxDYXJkUHJvcHM+ID0gKHtcbiAgY2hpbGRyZW4sXG4gIGNsYXNzTmFtZSxcbiAgaG92ZXIgPSBmYWxzZSxcbiAgcGFkZGluZyA9ICdtZCcsXG4gIHNoYWRvdyA9ICdtZCdcbn0pID0+IHtcbiAgY29uc3QgYmFzZUNsYXNzZXMgPSAnY2FyZCdcblxuICBjb25zdCBwYWRkaW5nQ2xhc3NlcyA9IHtcbiAgICBub25lOiAncC0wJyxcbiAgICBzbTogJ3AtMycsXG4gICAgbWQ6ICdwLTUnLFxuICAgIGxnOiAncC02JyxcbiAgICB4bDogJ3AtOCdcbiAgfVxuXG4gIGNvbnN0IHNoYWRvd0NsYXNzZXMgPSB7XG4gICAgbm9uZTogJycsXG4gICAgc206ICdzaGFkb3ctc20nLFxuICAgIG1kOiAnc2hhZG93LW1kJyxcbiAgICBsZzogJ3NoYWRvdy1sZycsXG4gICAgeGw6ICdzaGFkb3cteGwnXG4gIH1cblxuICBjb25zdCBob3ZlckNsYXNzZXMgPSBob3ZlciA/ICdjYXJkLWludGVyYWN0aXZlJyA6ICcnXG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2XG4gICAgICBjbGFzc05hbWU9e2Nsc3goXG4gICAgICAgIGJhc2VDbGFzc2VzLFxuICAgICAgICBwYWRkaW5nQ2xhc3Nlc1twYWRkaW5nXSxcbiAgICAgICAgc2hhZG93Q2xhc3Nlc1tzaGFkb3ddLFxuICAgICAgICBob3ZlckNsYXNzZXMsXG4gICAgICAgIGNsYXNzTmFtZVxuICAgICAgKX1cbiAgICA+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9kaXY+XG4gIClcbn1cblxuZXhwb3J0IGRlZmF1bHQgQ2FyZFxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY2xzeCIsIkNhcmQiLCJjaGlsZHJlbiIsImNsYXNzTmFtZSIsImhvdmVyIiwicGFkZGluZyIsInNoYWRvdyIsImJhc2VDbGFzc2VzIiwicGFkZGluZ0NsYXNzZXMiLCJub25lIiwic20iLCJtZCIsImxnIiwieGwiLCJzaGFkb3dDbGFzc2VzIiwiaG92ZXJDbGFzc2VzIiwiZGl2Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Card.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cshm71%5CDesktop%5Camshor2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();