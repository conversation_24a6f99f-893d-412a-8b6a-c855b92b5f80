'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { 
  Calendar, 
  Clock, 
  Eye, 
  Heart, 
  Share2, 
  TrendingUp, 
  Award, 
  Users, 
  Zap,
  Search,
  Filter,
  ArrowRight,
  Tag,
  MessageCircle,
  BookOpen,
  Star,
  Rocket
} from 'lucide-react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import MobileNavigation from '@/components/layout/MobileNavigation'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Badge from '@/components/ui/Badge'
import Input from '@/components/ui/Input'
import Container from '@/components/ui/Container'

const NewsPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')

  const news = [
    {
      id: 1,
      title: 'إطلاق ميزة مبدعي UGC الجديدة في المنصة',
      excerpt: 'نعلن بفخر عن إطلاق قسم مبدعي المحتوى المولد من المستخدمين (UGC) في منصتنا، مما يوفر فرصاً جديدة للمبدعين والعلامات التجارية.',
      content: 'تم إطلاق ميزة جديدة ومثيرة في منصتنا تتيح للعلامات التجارية التواصل مع مبدعي المحتوى المولد من المستخدمين...',
      image: '🚀',
      category: 'إعلانات المنتج',
      author: 'فريق التطوير',
      publishDate: '2024-01-15',
      readTime: '5 دقائق',
      views: 1250,
      likes: 89,
      featured: true,
      tags: ['UGC', 'ميزة جديدة', 'مبدعين']
    },
    {
      id: 2,
      title: 'وصولنا إلى 10,000 مؤثر معتمد في المنصة',
      excerpt: 'نحتفل اليوم بوصولنا إلى معلم مهم - 10,000 مؤثر معتمد في منصتنا، مما يجعلنا أكبر منصة للتسويق بالمؤثرين في المملكة.',
      content: 'في إنجاز مهم لمنصتنا، وصلنا اليوم إلى 10,000 مؤثر معتمد من جميع أنحاء المملكة العربية السعودية...',
      image: '🎉',
      category: 'أخبار الشركة',
      author: 'إدارة المنصة',
      publishDate: '2024-01-12',
      readTime: '3 دقائق',
      views: 2100,
      likes: 156,
      featured: true,
      tags: ['معلم', 'نمو', 'مؤثرين']
    },
    {
      id: 3,
      title: 'شراكة استراتيجية مع أكبر العلامات التجارية السعودية',
      excerpt: 'أعلنا عن شراكات استراتيجية جديدة مع مجموعة من أكبر العلامات التجارية السعودية لتوفير فرص أكثر للمؤثرين.',
      content: 'في خطوة مهمة لتوسيع نطاق خدماتنا، أعلنا اليوم عن شراكات استراتيجية مع عدد من أكبر العلامات التجارية...',
      image: '🤝',
      category: 'شراكات',
      author: 'قسم الشراكات',
      publishDate: '2024-01-10',
      readTime: '4 دقائق',
      views: 1800,
      likes: 134,
      featured: false,
      tags: ['شراكات', 'علامات تجارية', 'توسع']
    },
    {
      id: 4,
      title: 'تحديث أمني جديد لحماية بيانات المستخدمين',
      excerpt: 'تم تطبيق تحديث أمني شامل لضمان أقصى درجات الحماية لبيانات مستخدمينا وتعزيز الثقة في المنصة.',
      content: 'في إطار التزامنا بحماية خصوصية وأمان مستخدمينا، قمنا بتطبيق تحديث أمني شامل يتضمن...',
      image: '🔒',
      category: 'تحديثات تقنية',
      author: 'فريق الأمان',
      publishDate: '2024-01-08',
      readTime: '6 دقائق',
      views: 950,
      likes: 67,
      featured: false,
      tags: ['أمان', 'تحديث', 'حماية']
    },
    {
      id: 5,
      title: 'ورشة عمل مجانية: كيف تصبح مؤثراً ناجحاً',
      excerpt: 'ندعوكم لحضور ورشة عمل مجانية نقدم فيها نصائح وإرشادات للمؤثرين الجدد لبناء حضور رقمي قوي.',
      content: 'يسعدنا أن نعلن عن تنظيم ورشة عمل مجانية بعنوان "كيف تصبح مؤثراً ناجحاً" والتي ستقام...',
      image: '📚',
      category: 'فعاليات',
      author: 'فريق التدريب',
      publishDate: '2024-01-05',
      readTime: '2 دقيقة',
      views: 1650,
      likes: 198,
      featured: false,
      tags: ['ورشة عمل', 'تدريب', 'مؤثرين']
    },
    {
      id: 6,
      title: 'إطلاق تطبيق الجوال الجديد للمنصة',
      excerpt: 'تم إطلاق تطبيق الجوال الجديد للمنصة بواجهة محسنة وميزات جديدة لتحسين تجربة المستخدم.',
      content: 'نفخر بالإعلان عن إطلاق تطبيق الجوال الجديد لمنصتنا، والذي يأتي بتصميم عصري وميزات محسنة...',
      image: '📱',
      category: 'إعلانات المنتج',
      author: 'فريق التطوير',
      publishDate: '2024-01-03',
      readTime: '4 دقائق',
      views: 2800,
      likes: 245,
      featured: false,
      tags: ['تطبيق جوال', 'تحديث', 'تجربة مستخدم']
    }
  ]

  const categories = [
    { id: 'all', name: 'جميع الأخبار', icon: BookOpen },
    { id: 'إعلانات المنتج', name: 'إعلانات المنتج', icon: Rocket },
    { id: 'أخبار الشركة', name: 'أخبار الشركة', icon: Users },
    { id: 'شراكات', name: 'شراكات', icon: Award },
    { id: 'تحديثات تقنية', name: 'تحديثات تقنية', icon: Zap },
    { id: 'فعاليات', name: 'فعاليات', icon: Calendar }
  ]

  const filteredNews = news.filter(article => {
    const matchesSearch = article.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         article.excerpt.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || article.category === selectedCategory
    
    return matchesSearch && matchesCategory
  })

  const featuredNews = news.filter(article => article.featured)
  const regularNews = filteredNews.filter(article => !article.featured)

  const getDaysAgo = (dateString: string) => {
    const published = new Date(dateString)
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - published.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'إعلانات المنتج': return 'bg-blue-500'
      case 'أخبار الشركة': return 'bg-green-500'
      case 'شراكات': return 'bg-purple-500'
      case 'تحديثات تقنية': return 'bg-orange-500'
      case 'فعاليات': return 'bg-pink-500'
      default: return 'bg-gray-500'
    }
  }

  return (
    <div className="min-h-screen bg-slate-900">
      <Header />
      
      <div className="py-16">
        <Container size="lg">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
              أخبار المنصة
            </h1>
            <p className="text-xl text-slate-300 max-w-3xl mx-auto mb-8">
              تابع آخر الأخبار والتحديثات والإعلانات المهمة حول منصتنا وخدماتنا الجديدة
            </p>
          </div>

          {/* Search and Filters */}
          <Card className="mb-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-5 w-5" />
                <Input
                  placeholder="ابحث في الأخبار..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
              
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="input"
              >
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
          </Card>

          {/* Featured News */}
          {featuredNews.length > 0 && selectedCategory === 'all' && (
            <div className="mb-12">
              <h2 className="text-2xl font-bold text-white mb-6">الأخبار المميزة</h2>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {featuredNews.map((article) => (
                  <Card key={article.id} hover className="relative overflow-hidden">
                    <div className="absolute top-4 right-4">
                      <Badge variant="warning" size="sm">
                        <Star className="w-3 h-3 ml-1" />
                        مميز
                      </Badge>
                    </div>
                    
                    <div className="text-center mb-6">
                      <div className="text-6xl mb-4">{article.image}</div>
                      <div className={`inline-block px-3 py-1 rounded-full text-white text-sm ${getCategoryColor(article.category)}`}>
                        {article.category}
                      </div>
                    </div>

                    <h3 className="text-xl font-bold text-white mb-3">{article.title}</h3>
                    <p className="text-slate-300 text-sm mb-4 line-clamp-3">{article.excerpt}</p>

                    <div className="flex items-center justify-between text-sm text-slate-400 mb-4">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 ml-1" />
                          منذ {getDaysAgo(article.publishDate)} أيام
                        </div>
                        <div className="flex items-center">
                          <Clock className="h-4 w-4 ml-1" />
                          {article.readTime}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between pt-4 border-t border-slate-700">
                      <div className="flex items-center gap-4 text-sm text-slate-400">
                        <div className="flex items-center">
                          <Eye className="h-4 w-4 ml-1" />
                          {article.views}
                        </div>
                        <div className="flex items-center">
                          <Heart className="h-4 w-4 ml-1" />
                          {article.likes}
                        </div>
                      </div>
                      <Button variant="primary" size="sm">
                        اقرأ المزيد
                        <ArrowRight className="h-4 w-4 mr-2" />
                      </Button>
                    </div>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {/* Regular News */}
          <div className="mb-12">
            <h2 className="text-2xl font-bold text-white mb-6">
              {selectedCategory === 'all' ? 'جميع الأخبار' : categories.find(c => c.id === selectedCategory)?.name}
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {regularNews.map((article) => (
                <Card key={article.id} hover>
                  <div className="text-center mb-4">
                    <div className="text-4xl mb-3">{article.image}</div>
                    <div className={`inline-block px-2 py-1 rounded-full text-white text-xs ${getCategoryColor(article.category)}`}>
                      {article.category}
                    </div>
                  </div>

                  <h3 className="text-lg font-bold text-white mb-2">{article.title}</h3>
                  <p className="text-slate-300 text-sm mb-4 line-clamp-2">{article.excerpt}</p>

                  <div className="flex flex-wrap gap-1 mb-4">
                    {article.tags.map((tag, index) => (
                      <Badge key={index} variant="secondary" size="sm">
                        {tag}
                      </Badge>
                    ))}
                  </div>

                  <div className="flex items-center justify-between text-xs text-slate-400 mb-4">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center">
                        <Calendar className="h-3 w-3 ml-1" />
                        منذ {getDaysAgo(article.publishDate)} أيام
                      </div>
                      <div className="flex items-center">
                        <Clock className="h-3 w-3 ml-1" />
                        {article.readTime}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between pt-4 border-t border-slate-700">
                    <div className="flex items-center gap-3 text-sm text-slate-400">
                      <div className="flex items-center">
                        <Eye className="h-4 w-4 ml-1" />
                        {article.views}
                      </div>
                      <div className="flex items-center">
                        <Heart className="h-4 w-4 ml-1" />
                        {article.likes}
                      </div>
                    </div>
                    <Button variant="secondary" size="sm">
                      اقرأ
                    </Button>
                  </div>
                </Card>
              ))}
            </div>
          </div>

          {filteredNews.length === 0 && (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">📰</div>
              <h3 className="text-xl font-bold text-white mb-2">لا توجد أخبار مطابقة</h3>
              <p className="text-slate-400">جرب تغيير معايير البحث أو تصفح جميع الأخبار</p>
            </div>
          )}

          {/* Newsletter Signup */}
          <div className="mt-16">
            <Card className="bg-gradient-to-r from-indigo-600 to-purple-600">
              <div className="text-center">
                <h2 className="text-2xl font-bold text-white mb-4">اشترك في النشرة الإخبارية</h2>
                <p className="text-indigo-100 mb-6">
                  احصل على آخر الأخبار والتحديثات مباشرة في بريدك الإلكتروني
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
                  <Input
                    placeholder="بريدك الإلكتروني"
                    className="flex-1"
                  />
                  <Button variant="secondary" className="bg-white text-indigo-600 hover:bg-gray-100">
                    اشترك الآن
                  </Button>
                </div>
                <p className="text-indigo-200 text-sm mt-4">
                  لن نشارك بريدك الإلكتروني مع أي طرف ثالث
                </p>
              </div>
            </Card>
          </div>
        </Container>
      </div>

      <Footer />
      <MobileNavigation />
    </div>
  )
}

export default NewsPage
