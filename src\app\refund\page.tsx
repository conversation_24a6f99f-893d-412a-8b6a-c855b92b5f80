'use client'

import React from 'react'
import Link from 'next/link'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { 
  RefreshCw, 
  Shield, 
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  CreditCard,
  FileText,
  MessageCircle
} from 'lucide-react'

const RefundPage: React.FC = () => {
  const lastUpdated = '15 يناير 2024'

  const refundScenarios = [
    {
      icon: CheckCircle,
      title: 'استرداد كامل مضمون',
      color: 'text-green-600 bg-green-100',
      scenarios: [
        'عدم تسليم المحتوى في الموعد المحدد',
        'عدم التزام المؤثر بالمتطلبات المتفق عليها',
        'محتوى لا يتطابق مع الوصف المطلوب',
        'انتهاك شروط الاستخدام من قبل المؤثر',
        'مشاكل تقنية تمنع إكمال الحملة'
      ]
    },
    {
      icon: AlertTriangle,
      title: 'استرداد جزئي',
      color: 'text-yellow-600 bg-yellow-100',
      scenarios: [
        'تسليم جزئي للمحتوى المطلوب',
        'جودة أقل من المتوقع مع موافقة التاجر',
        'تعديلات طفيفة على المتطلبات الأصلية',
        'تأخير بسيط مع موافقة الطرفين'
      ]
    },
    {
      icon: XCircle,
      title: 'لا يحق الاسترداد',
      color: 'text-red-600 bg-red-100',
      scenarios: [
        'تغيير رأي التاجر بعد بدء العمل',
        'طلب إلغاء بعد تسليم المحتوى',
        'عدم رضا شخصي غير مبرر',
        'تغيير في استراتيجية التسويق',
        'مشاكل خارجة عن سيطرة المؤثر'
      ]
    }
  ]

  const refundProcess = [
    {
      step: 1,
      title: 'تقديم طلب الاسترداد',
      description: 'قم بتقديم طلب الاسترداد عبر لوحة التحكم أو تواصل مع الدعم',
      timeframe: 'خلال 7 أيام من انتهاء الحملة'
    },
    {
      step: 2,
      title: 'مراجعة الطلب',
      description: 'فريقنا سيراجع الطلب والأدلة المقدمة',
      timeframe: '2-3 أيام عمل'
    },
    {
      step: 3,
      title: 'التحقيق',
      description: 'التواصل مع الطرفين وجمع المعلومات اللازمة',
      timeframe: '3-5 أيام عمل'
    },
    {
      step: 4,
      title: 'القرار النهائي',
      description: 'إشعارك بقرار الاسترداد مع التبرير',
      timeframe: '1-2 يوم عمل'
    },
    {
      step: 5,
      title: 'تنفيذ الاسترداد',
      description: 'معالجة الاسترداد إلى طريقة الدفع الأصلية',
      timeframe: '3-7 أيام عمل'
    }
  ]

  const paymentMethods = [
    {
      method: 'البطاقات الائتمانية',
      timeframe: '3-5 أيام عمل',
      note: 'يظهر في كشف الحساب خلال دورة الفوترة التالية'
    },
    {
      method: 'Apple Pay',
      timeframe: '1-3 أيام عمل',
      note: 'يظهر في محفظة Apple Pay فوراً'
    },
    {
      method: 'Google Pay',
      timeframe: '1-3 أيام عمل',
      note: 'يظهر في محفظة Google Pay فوراً'
    },
    {
      method: 'التحويل البنكي',
      timeframe: '5-7 أيام عمل',
      note: 'يتطلب تأكيد بيانات الحساب البنكي'
    }
  ]

  const requiredDocuments = [
    'لقطات شاشة من المراسلات',
    'نسخة من الاتفاق الأصلي',
    'أدلة على عدم الالتزام',
    'تفاصيل المشكلة بوضوح',
    'أي مستندات داعمة أخرى'
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
      <Header />
      
      {/* Hero Section */}
      <section className="py-20 bg-gradient-primary text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6">
            <RefreshCw className="h-8 w-8 text-white" />
          </div>
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            سياسة الاسترداد
          </h1>
          <p className="text-xl mb-4 opacity-90">
            نحن ملتزمون بحماية حقوقك المالية وضمان تجربة آمنة وموثوقة
          </p>
          <div className="flex items-center justify-center text-sm opacity-75">
            <Clock className="h-4 w-4 ml-2" />
            <span>آخر تحديث: {lastUpdated}</span>
          </div>
        </div>
      </section>

      <div className="py-20">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Introduction */}
          <Card className="mb-12">
            <div className="flex items-start">
              <Shield className="h-8 w-8 text-primary-600 mt-1 ml-4 flex-shrink-0" />
              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-4">ضمان استرداد الأموال</h2>
                <p className="text-gray-700 leading-relaxed mb-4">
                  في منصة المؤثرين السعودية، نؤمن بأهمية حماية استثماراتك. لذلك نوفر نظام ضمان 
                  شامل يحمي أموالك ويضمن حصولك على الخدمة المتفق عليها أو استرداد أموالك بالكامل.
                </p>
                <p className="text-gray-700 leading-relaxed">
                  نحن نحتفظ بأموالك في نظام ضمان آمن حتى إكمال الحملة بنجاح وموافقتك على النتائج. 
                  هذا يضمن حقوقك ويوفر بيئة آمنة للتعامل مع المؤثرين.
                </p>
              </div>
            </div>
          </Card>

          {/* Refund Scenarios */}
          <div className="mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
              حالات الاسترداد
            </h2>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {refundScenarios.map((scenario, index) => {
                const Icon = scenario.icon
                return (
                  <Card key={index}>
                    <div className="text-center mb-6">
                      <div className={`w-16 h-16 rounded-full ${scenario.color} flex items-center justify-center mx-auto mb-4`}>
                        <Icon className="h-8 w-8" />
                      </div>
                      <h3 className="text-lg font-bold text-gray-900">{scenario.title}</h3>
                    </div>
                    <ul className="space-y-3">
                      {scenario.scenarios.map((item, i) => (
                        <li key={i} className="flex items-start text-sm text-gray-700">
                          <div className="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 ml-3 flex-shrink-0"></div>
                          <span>{item}</span>
                        </li>
                      ))}
                    </ul>
                  </Card>
                )
              })}
            </div>
          </div>

          {/* Refund Process */}
          <div className="mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
              عملية الاسترداد
            </h2>
            <div className="space-y-6">
              {refundProcess.map((step, index) => (
                <Card key={index}>
                  <div className="flex items-start">
                    <div className="w-12 h-12 bg-primary-600 text-white rounded-full flex items-center justify-center font-bold text-lg ml-6 flex-shrink-0">
                      {step.step}
                    </div>
                    <div className="flex-1">
                      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-2">
                        <h3 className="text-lg font-bold text-gray-900">{step.title}</h3>
                        <span className="text-sm text-primary-600 font-medium">{step.timeframe}</span>
                      </div>
                      <p className="text-gray-700">{step.description}</p>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Payment Methods */}
            <div>
              <Card>
                <h3 className="text-xl font-bold text-gray-900 mb-6">
                  <CreditCard className="h-6 w-6 inline ml-3" />
                  مدة الاسترداد حسب طريقة الدفع
                </h3>
                <div className="space-y-4">
                  {paymentMethods.map((payment, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex justify-between items-start mb-2">
                        <h4 className="font-semibold text-gray-900">{payment.method}</h4>
                        <span className="text-sm text-primary-600 font-medium">{payment.timeframe}</span>
                      </div>
                      <p className="text-sm text-gray-600">{payment.note}</p>
                    </div>
                  ))}
                </div>
              </Card>
            </div>

            {/* Required Documents */}
            <div>
              <Card>
                <h3 className="text-xl font-bold text-gray-900 mb-6">
                  <FileText className="h-6 w-6 inline ml-3" />
                  المستندات المطلوبة
                </h3>
                <p className="text-gray-700 mb-4">
                  لتسريع عملية مراجعة طلب الاسترداد، يرجى تقديم:
                </p>
                <ul className="space-y-3">
                  {requiredDocuments.map((doc, index) => (
                    <li key={index} className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 ml-3 flex-shrink-0" />
                      <span className="text-gray-700">{doc}</span>
                    </li>
                  ))}
                </ul>
                <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <p className="text-blue-800 text-sm">
                    <strong>نصيحة:</strong> كلما كانت الأدلة أوضح وأكثر تفصيلاً، 
                    كلما كانت عملية المراجعة أسرع وأدق.
                  </p>
                </div>
              </Card>
            </div>
          </div>

          {/* Important Notes */}
          <Card className="mt-12 border-l-4 border-l-yellow-500 bg-yellow-50">
            <div className="flex items-start">
              <AlertTriangle className="h-6 w-6 text-yellow-600 mt-1 ml-4 flex-shrink-0" />
              <div>
                <h3 className="text-lg font-semibold text-yellow-800 mb-2">
                  ملاحظات مهمة
                </h3>
                <ul className="space-y-2 text-yellow-700">
                  <li>• يجب تقديم طلب الاسترداد خلال 7 أيام من انتهاء الحملة</li>
                  <li>• الاسترداد يتم إلى نفس طريقة الدفع المستخدمة في الشراء</li>
                  <li>• قد تطبق رسوم معالجة من البنك أو مقدم خدمة الدفع</li>
                  <li>• القرارات النهائية للاسترداد تخضع لمراجعة فريق متخصص</li>
                  <li>• يمكن استئناف قرار الاسترداد خلال 30 يوماً</li>
                </ul>
              </div>
            </div>
          </Card>

          {/* Contact Support */}
          <Card className="mt-12 text-center">
            <h3 className="text-xl font-bold text-gray-900 mb-4">
              تحتاج مساعدة في طلب الاسترداد؟
            </h3>
            <p className="text-gray-600 mb-6">
              فريق الدعم لدينا جاهز لمساعدتك في عملية الاسترداد وتوضيح أي استفسارات
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/contact">
                <Button className="w-full sm:w-auto">
                  <MessageCircle className="h-5 w-5 ml-2" />
                  تواصل مع الدعم
                </Button>
              </Link>
              <Link href="/help">
                <Button variant="outline" className="w-full sm:w-auto">
                  <FileText className="h-5 w-5 ml-2" />
                  مركز المساعدة
                </Button>
              </Link>
            </div>
          </Card>

          {/* Last Updated */}
          <div className="text-center mt-12 text-gray-500">
            <p>آخر تحديث: {lastUpdated}</p>
            <p className="mt-2">جميع الحقوق محفوظة © 2024 منصة المؤثرين السعودية</p>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  )
}

export default RefundPage
