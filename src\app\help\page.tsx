'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import { 
  Search, 
  BookOpen, 
  Users,
  Store,
  CreditCard,
  Shield,
  MessageCircle,
  Phone,
  Mail,
  Video,
  FileText,
  HelpCircle,
  ArrowLeft,
  ExternalLink
} from 'lucide-react'

const HelpPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('')

  const helpCategories = [
    {
      id: 'getting-started',
      title: 'البدء',
      description: 'كل ما تحتاج لمعرفته للبدء في استخدام المنصة',
      icon: BookOpen,
      color: 'from-blue-500 to-blue-600',
      articles: [
        'كيفية إنشاء حساب جديد',
        'إعداد الملف الشخصي',
        'فهم واجهة المنصة',
        'الخطوات الأولى بعد التسجيل'
      ]
    },
    {
      id: 'influencers',
      title: 'دليل المؤثرين',
      description: 'إرشادات شاملة للمؤثرين لتحقيق أقصى استفادة',
      icon: Users,
      color: 'from-purple-500 to-purple-600',
      articles: [
        'كيفية تحديد الأسعار المناسبة',
        'إنشاء محتوى جذاب',
        'إدارة الحملات بفعالية',
        'بناء علاقات طويلة الأمد مع التجار'
      ]
    },
    {
      id: 'merchants',
      title: 'دليل التجار',
      description: 'كيفية العثور على المؤثرين المناسبين وإدارة الحملات',
      icon: Store,
      color: 'from-green-500 to-green-600',
      articles: [
        'اختيار المؤثر المناسب',
        'كتابة وصف حملة فعال',
        'قياس نجاح الحملات',
        'أفضل الممارسات في التسويق بالمؤثرين'
      ]
    },
    {
      id: 'payments',
      title: 'المدفوعات والفواتير',
      description: 'كل ما يتعلق بالمدفوعات ونظام الضمان',
      icon: CreditCard,
      color: 'from-yellow-500 to-yellow-600',
      articles: [
        'كيف يعمل نظام الضمان',
        'طرق الدفع المتاحة',
        'فهم الرسوم والعمولات',
        'استرداد الأموال'
      ]
    },
    {
      id: 'security',
      title: 'الأمان والخصوصية',
      description: 'حماية حسابك ومعلوماتك الشخصية',
      icon: Shield,
      color: 'from-red-500 to-red-600',
      articles: [
        'تأمين حسابك',
        'التحقق من الهوية',
        'الإبلاغ عن المشاكل',
        'حماية البيانات الشخصية'
      ]
    }
  ]

  const quickActions = [
    {
      title: 'تواصل مع الدعم',
      description: 'احصل على مساعدة فورية من فريق الدعم',
      icon: MessageCircle,
      action: 'تواصل الآن',
      href: '/contact'
    },
    {
      title: 'الأسئلة الشائعة',
      description: 'ابحث في قاعدة الأسئلة الشائعة',
      icon: HelpCircle,
      action: 'تصفح الأسئلة',
      href: '/faq'
    },
    {
      title: 'فيديوهات تعليمية',
      description: 'شاهد فيديوهات توضيحية لاستخدام المنصة',
      icon: Video,
      action: 'شاهد الآن',
      href: '#'
    },
    {
      title: 'دليل المستخدم',
      description: 'دليل شامل لجميع ميزات المنصة',
      icon: FileText,
      action: 'تحميل الدليل',
      href: '#'
    }
  ]

  const supportChannels = [
    {
      title: 'الدردشة المباشرة',
      description: 'متاح 24/7 للمساعدة الفورية',
      icon: MessageCircle,
      status: 'متاح الآن',
      statusColor: 'text-green-600'
    },
    {
      title: 'البريد الإلكتروني',
      description: '<EMAIL>',
      icon: Mail,
      status: 'رد خلال 24 ساعة',
      statusColor: 'text-blue-600'
    },
    {
      title: 'الهاتف',
      description: '+966 11 123 4567',
      icon: Phone,
      status: 'السبت - الخميس 9-18',
      statusColor: 'text-gray-600'
    }
  ]

  const popularArticles = [
    'كيفية إنشاء حملة إعلانية ناجحة',
    'فهم نظام التقييم والمراجعات',
    'حل مشاكل الدفع الشائعة',
    'تحسين الملف الشخصي للمؤثرين',
    'اختيار المؤثر المناسب لعلامتك التجارية'
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
      <Header />
      
      {/* Hero Section */}
      <section className="py-20 bg-gradient-primary text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            مركز المساعدة
          </h1>
          <p className="text-xl mb-8 opacity-90">
            كل ما تحتاج لمعرفته لاستخدام منصة المؤثرين السعودية بفعالية
          </p>
          
          {/* Search */}
          <div className="max-w-md mx-auto">
            <Input
              placeholder="ابحث عن مساعدة..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              leftIcon={<Search className="h-5 w-5" />}
              className="bg-white"
            />
          </div>
        </div>
      </section>

      <div className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Quick Actions */}
          <div className="mb-16">
            <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
              إجراءات سريعة
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {quickActions.map((action, index) => {
                const Icon = action.icon
                return (
                  <Card key={index} hover className="text-center">
                    <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                      <Icon className="h-6 w-6 text-primary-600" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      {action.title}
                    </h3>
                    <p className="text-gray-600 text-sm mb-4">
                      {action.description}
                    </p>
                    <Link href={action.href}>
                      <Button variant="outline" size="sm" className="w-full">
                        {action.action}
                        <ExternalLink className="h-4 w-4 mr-2" />
                      </Button>
                    </Link>
                  </Card>
                )
              })}
            </div>
          </div>

          {/* Help Categories */}
          <div className="mb-16">
            <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
              أقسام المساعدة
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {helpCategories.map((category) => {
                const Icon = category.icon
                return (
                  <Card key={category.id} hover className="overflow-hidden">
                    <div className={`h-2 bg-gradient-to-r ${category.color}`}></div>
                    <div className="p-6">
                      <div className="flex items-center mb-4">
                        <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${category.color} flex items-center justify-center`}>
                          <Icon className="h-6 w-6 text-white" />
                        </div>
                        <div className="mr-4">
                          <h3 className="text-lg font-bold text-gray-900">{category.title}</h3>
                        </div>
                      </div>
                      
                      <p className="text-gray-600 mb-4">{category.description}</p>
                      
                      <div className="space-y-2 mb-6">
                        {category.articles.slice(0, 3).map((article, index) => (
                          <div key={index} className="flex items-center text-sm text-gray-700">
                            <div className="w-1.5 h-1.5 bg-gray-400 rounded-full ml-2 flex-shrink-0"></div>
                            <span>{article}</span>
                          </div>
                        ))}
                        {category.articles.length > 3 && (
                          <div className="text-sm text-primary-600">
                            +{category.articles.length - 3} مقال آخر
                          </div>
                        )}
                      </div>
                      
                      <Button variant="outline" size="sm" className="w-full">
                        استكشف القسم
                        <ArrowLeft className="h-4 w-4 mr-2" />
                      </Button>
                    </div>
                  </Card>
                )
              })}
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Popular Articles */}
            <div>
              <Card>
                <h3 className="text-xl font-bold text-gray-900 mb-6">
                  المقالات الأكثر شعبية
                </h3>
                <div className="space-y-4">
                  {popularArticles.map((article, index) => (
                    <div key={index} className="flex items-center p-3 hover:bg-gray-50 rounded-lg transition-colors cursor-pointer">
                      <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center ml-3 flex-shrink-0">
                        <span className="text-primary-600 font-semibold text-sm">{index + 1}</span>
                      </div>
                      <span className="text-gray-700 hover:text-primary-600 transition-colors">
                        {article}
                      </span>
                    </div>
                  ))}
                </div>
              </Card>
            </div>

            {/* Support Channels */}
            <div>
              <Card>
                <h3 className="text-xl font-bold text-gray-900 mb-6">
                  قنوات الدعم
                </h3>
                <div className="space-y-6">
                  {supportChannels.map((channel, index) => {
                    const Icon = channel.icon
                    return (
                      <div key={index} className="flex items-start">
                        <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center ml-4 flex-shrink-0">
                          <Icon className="h-6 w-6 text-gray-600" />
                        </div>
                        <div className="flex-1">
                          <h4 className="font-semibold text-gray-900 mb-1">
                            {channel.title}
                          </h4>
                          <p className="text-gray-600 text-sm mb-2">
                            {channel.description}
                          </p>
                          <span className={`text-sm font-medium ${channel.statusColor}`}>
                            {channel.status}
                          </span>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </Card>
            </div>
          </div>

          {/* Still Need Help */}
          <div className="mt-16 text-center">
            <Card className="max-w-2xl mx-auto">
              <h3 className="text-xl font-bold text-gray-900 mb-4">
                لا تزال تحتاج مساعدة؟
              </h3>
              <p className="text-gray-600 mb-6">
                فريق الدعم لدينا جاهز لمساعدتك في أي وقت. تواصل معنا وسنكون سعداء للمساعدة.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/contact">
                  <Button className="w-full sm:w-auto">
                    <MessageCircle className="h-5 w-5 ml-2" />
                    تواصل مع الدعم
                  </Button>
                </Link>
                <Link href="/faq">
                  <Button variant="outline" className="w-full sm:w-auto">
                    <HelpCircle className="h-5 w-5 ml-2" />
                    الأسئلة الشائعة
                  </Button>
                </Link>
              </div>
            </Card>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  )
}

export default HelpPage
