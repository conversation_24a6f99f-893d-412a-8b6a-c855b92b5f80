'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import { 
  HeadphonesIcon, 
  MessageCircle, 
  Phone,
  Mail,
  Clock,
  CheckCircle,
  AlertCircle,
  Users,
  Store,
  CreditCard,
  Shield,
  Search,
  Send,
  FileText,
  Video,
  Zap
} from 'lucide-react'

const SupportPage: React.FC = () => {
  const [ticketForm, setTicketForm] = useState({
    name: '',
    email: '',
    userType: '',
    priority: '',
    category: '',
    subject: '',
    description: ''
  })

  const [isSubmitting, setIsSubmitting] = useState(false)

  const supportChannels = [
    {
      title: 'الدردشة المباشرة',
      description: 'احصل على مساعدة فورية من فريق الدعم',
      icon: MessageCircle,
      status: 'متاح الآن',
      statusColor: 'text-green-600',
      responseTime: 'فوري',
      availability: '24/7',
      color: 'from-green-500 to-green-600'
    },
    {
      title: 'البريد الإلكتروني',
      description: 'أرسل استفسارك وسنرد عليك بأسرع وقت',
      icon: Mail,
      status: 'متاح',
      statusColor: 'text-blue-600',
      responseTime: 'خلال 2-4 ساعات',
      availability: '24/7',
      color: 'from-blue-500 to-blue-600'
    },
    {
      title: 'المكالمات الهاتفية',
      description: 'تحدث مباشرة مع أحد خبراء الدعم',
      icon: Phone,
      status: 'ساعات العمل',
      statusColor: 'text-yellow-600',
      responseTime: 'فوري',
      availability: 'السبت - الخميس 9-18',
      color: 'from-yellow-500 to-yellow-600'
    }
  ]

  const supportCategories = [
    {
      title: 'دعم المؤثرين',
      description: 'مساعدة في إدارة الملف الشخصي والحملات',
      icon: Users,
      topics: ['إعداد الملف الشخصي', 'إدارة الحملات', 'تحديد الأسعار', 'استلام المدفوعات'],
      color: 'from-purple-500 to-purple-600'
    },
    {
      title: 'دعم التجار',
      description: 'مساعدة في إطلاق الحملات والعثور على المؤثرين',
      icon: Store,
      topics: ['البحث عن المؤثرين', 'إطلاق الحملات', 'متابعة النتائج', 'إدارة الميزانية'],
      color: 'from-blue-500 to-blue-600'
    },
    {
      title: 'المدفوعات والفواتير',
      description: 'مساعدة في المعاملات المالية ونظام الضمان',
      icon: CreditCard,
      topics: ['طرق الدفع', 'نظام الضمان', 'الفواتير', 'استرداد الأموال'],
      color: 'from-green-500 to-green-600'
    },
    {
      title: 'الأمان والحماية',
      description: 'مساعدة في أمان الحساب وحماية البيانات',
      icon: Shield,
      topics: ['أمان الحساب', 'التحقق من الهوية', 'الإبلاغ عن مشاكل', 'حماية البيانات'],
      color: 'from-red-500 to-red-600'
    }
  ]

  const quickSolutions = [
    {
      question: 'كيف أعيد تعيين كلمة المرور؟',
      answer: 'اذهب إلى صفحة تسجيل الدخول واضغط على "نسيت كلمة المرور"',
      category: 'حساب'
    },
    {
      question: 'كيف أحدث معلومات الدفع؟',
      answer: 'من لوحة التحكم، اذهب إلى الإعدادات > طرق الدفع',
      category: 'مدفوعات'
    },
    {
      question: 'كيف أتواصل مع مؤثر؟',
      answer: 'اضغط على "تواصل" في ملف المؤثر أو أرسل طلب تعاون',
      category: 'تواصل'
    },
    {
      question: 'متى أحصل على أجري كمؤثر؟',
      answer: 'خلال 24-48 ساعة من موافقة التاجر على المحتوى',
      category: 'مدفوعات'
    }
  ]

  const handleInputChange = (field: string, value: string) => {
    setTicketForm(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    
    // Simulate form submission
    setTimeout(() => {
      setIsSubmitting(false)
      alert('تم إرسال طلب الدعم بنجاح! سنتواصل معك قريباً.')
      setTicketForm({
        name: '',
        email: '',
        userType: '',
        priority: '',
        category: '',
        subject: '',
        description: ''
      })
    }, 2000)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
      <Header />
      
      {/* Hero Section */}
      <section className="py-20 bg-gradient-primary text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6">
            <HeadphonesIcon className="h-8 w-8 text-white" />
          </div>
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            الدعم الفني
          </h1>
          <p className="text-xl mb-8 opacity-90">
            نحن هنا لمساعدتك 24/7. احصل على الدعم الذي تحتاجه لتحقيق أقصى استفادة من المنصة
          </p>
          
          <div className="flex items-center justify-center gap-8 text-sm opacity-90">
            <div className="flex items-center">
              <Clock className="h-5 w-5 ml-2" />
              <span>دعم 24/7</span>
            </div>
            <div className="flex items-center">
              <Zap className="h-5 w-5 ml-2" />
              <span>استجابة سريعة</span>
            </div>
            <div className="flex items-center">
              <CheckCircle className="h-5 w-5 ml-2" />
              <span>خبراء متخصصون</span>
            </div>
          </div>
        </div>
      </section>

      <div className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Support Channels */}
          <div className="mb-16">
            <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
              قنوات الدعم المتاحة
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {supportChannels.map((channel, index) => {
                const Icon = channel.icon
                return (
                  <Card key={index} hover className="text-center overflow-hidden">
                    <div className={`h-2 bg-gradient-to-r ${channel.color}`}></div>
                    <div className="p-6">
                      <div className={`w-16 h-16 rounded-full bg-gradient-to-r ${channel.color} flex items-center justify-center mx-auto mb-4`}>
                        <Icon className="h-8 w-8 text-white" />
                      </div>
                      <h3 className="text-xl font-bold text-gray-900 mb-2">
                        {channel.title}
                      </h3>
                      <p className="text-gray-600 mb-4">
                        {channel.description}
                      </p>
                      
                      <div className="space-y-2 mb-6">
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-500">الحالة:</span>
                          <span className={`font-medium ${channel.statusColor}`}>
                            {channel.status}
                          </span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-500">وقت الاستجابة:</span>
                          <span className="font-medium text-gray-700">
                            {channel.responseTime}
                          </span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-500">التوفر:</span>
                          <span className="font-medium text-gray-700">
                            {channel.availability}
                          </span>
                        </div>
                      </div>
                      
                      <Button className={`w-full bg-gradient-to-r ${channel.color} border-0`}>
                        ابدأ المحادثة
                      </Button>
                    </div>
                  </Card>
                )
              })}
            </div>
          </div>

          {/* Support Categories */}
          <div className="mb-16">
            <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
              أقسام الدعم
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {supportCategories.map((category, index) => {
                const Icon = category.icon
                return (
                  <Card key={index} hover>
                    <div className="flex items-start">
                      <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${category.color} flex items-center justify-center ml-4 flex-shrink-0`}>
                        <Icon className="h-6 w-6 text-white" />
                      </div>
                      <div className="flex-1">
                        <h3 className="text-lg font-bold text-gray-900 mb-2">
                          {category.title}
                        </h3>
                        <p className="text-gray-600 mb-4">
                          {category.description}
                        </p>
                        <div className="space-y-1">
                          {category.topics.map((topic, i) => (
                            <div key={i} className="flex items-center text-sm text-gray-700">
                              <div className="w-1.5 h-1.5 bg-gray-400 rounded-full ml-2 flex-shrink-0"></div>
                              <span>{topic}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </Card>
                )
              })}
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Quick Solutions */}
            <div>
              <Card>
                <h3 className="text-xl font-bold text-gray-900 mb-6">
                  <Search className="h-6 w-6 inline ml-3" />
                  حلول سريعة
                </h3>
                <div className="space-y-4">
                  {quickSolutions.map((solution, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                      <div className="flex items-start justify-between mb-2">
                        <h4 className="font-semibold text-gray-900 flex-1">
                          {solution.question}
                        </h4>
                        <span className="px-2 py-1 bg-primary-100 text-primary-800 text-xs rounded-full">
                          {solution.category}
                        </span>
                      </div>
                      <p className="text-gray-600 text-sm">
                        {solution.answer}
                      </p>
                    </div>
                  ))}
                </div>
                
                <div className="mt-6 text-center">
                  <Link href="/faq">
                    <Button variant="outline" size="sm">
                      <FileText className="h-4 w-4 ml-2" />
                      المزيد من الأسئلة الشائعة
                    </Button>
                  </Link>
                </div>
              </Card>
            </div>

            {/* Support Ticket Form */}
            <div>
              <Card>
                <h3 className="text-xl font-bold text-gray-900 mb-6">
                  <Send className="h-6 w-6 inline ml-3" />
                  إرسال طلب دعم
                </h3>
                
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Input
                      label="الاسم"
                      placeholder="اسمك الكامل"
                      value={ticketForm.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      required
                    />
                    
                    <Input
                      label="البريد الإلكتروني"
                      type="email"
                      placeholder="<EMAIL>"
                      value={ticketForm.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      required
                    />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        نوع المستخدم
                      </label>
                      <select
                        value={ticketForm.userType}
                        onChange={(e) => handleInputChange('userType', e.target.value)}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        required
                      >
                        <option value="">اختر نوع المستخدم</option>
                        <option value="influencer">مؤثر</option>
                        <option value="merchant">تاجر</option>
                        <option value="visitor">زائر</option>
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        الأولوية
                      </label>
                      <select
                        value={ticketForm.priority}
                        onChange={(e) => handleInputChange('priority', e.target.value)}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        required
                      >
                        <option value="">اختر الأولوية</option>
                        <option value="low">منخفضة</option>
                        <option value="medium">متوسطة</option>
                        <option value="high">عالية</option>
                        <option value="urgent">عاجلة</option>
                      </select>
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      فئة المشكلة
                    </label>
                    <select
                      value={ticketForm.category}
                      onChange={(e) => handleInputChange('category', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      required
                    >
                      <option value="">اختر الفئة</option>
                      <option value="account">مشاكل الحساب</option>
                      <option value="payments">المدفوعات</option>
                      <option value="campaigns">الحملات</option>
                      <option value="technical">مشاكل تقنية</option>
                      <option value="other">أخرى</option>
                    </select>
                  </div>
                  
                  <Input
                    label="موضوع المشكلة"
                    placeholder="وصف مختصر للمشكلة"
                    value={ticketForm.subject}
                    onChange={(e) => handleInputChange('subject', e.target.value)}
                    required
                  />
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      تفاصيل المشكلة
                    </label>
                    <textarea
                      value={ticketForm.description}
                      onChange={(e) => handleInputChange('description', e.target.value)}
                      placeholder="اشرح المشكلة بالتفصيل..."
                      rows={4}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      required
                    />
                  </div>
                  
                  <Button
                    type="submit"
                    className="w-full"
                    isLoading={isSubmitting}
                  >
                    {isSubmitting ? 'جاري الإرسال...' : 'إرسال طلب الدعم'}
                    {!isSubmitting && <Send className="h-5 w-5 mr-3" />}
                  </Button>
                </form>
              </Card>
            </div>
          </div>

          {/* Additional Resources */}
          <div className="mt-16">
            <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
              موارد إضافية
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card hover className="text-center">
                <Video className="h-12 w-12 text-primary-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  فيديوهات تعليمية
                </h3>
                <p className="text-gray-600 mb-4">
                  شاهد فيديوهات توضيحية لاستخدام المنصة
                </p>
                <Button variant="outline" size="sm">
                  شاهد الآن
                </Button>
              </Card>

              <Card hover className="text-center">
                <FileText className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  دليل المستخدم
                </h3>
                <p className="text-gray-600 mb-4">
                  دليل شامل لجميع ميزات المنصة
                </p>
                <Link href="/help">
                  <Button variant="outline" size="sm">
                    تحميل الدليل
                  </Button>
                </Link>
              </Card>

              <Card hover className="text-center">
                <MessageCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  مجتمع المستخدمين
                </h3>
                <p className="text-gray-600 mb-4">
                  انضم لمجتمع المؤثرين والتجار
                </p>
                <Button variant="outline" size="sm">
                  انضم الآن
                </Button>
              </Card>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  )
}

export default SupportPage
