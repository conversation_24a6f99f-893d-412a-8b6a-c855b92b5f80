(()=>{var e={};e.id=260,e.ids=[260],e.modules={2643:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});var t=s(60687);s(43210);var l=s(49384);let a=({variant:e="primary",size:r="md",isLoading:s=!1,fullWidth:a=!1,children:i,className:o,disabled:n,...c})=>(0,t.jsxs)("button",{className:(0,l.$)("transition-all duration-200 ease-in-out","focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-slate-900","disabled:opacity-50 disabled:cursor-not-allowed","font-medium rounded-lg border",{primary:"btn-primary",secondary:"btn-secondary",outline:"btn-outline",ghost:"bg-transparent text-slate-300 hover:text-white hover:bg-slate-700/50 border border-slate-600 hover:border-slate-500",danger:"bg-red-600 hover:bg-red-700 text-white border-red-600 shadow-md hover:shadow-lg",success:"bg-emerald-600 hover:bg-emerald-700 text-white border-emerald-600 shadow-md hover:shadow-lg",warning:"bg-amber-600 hover:bg-amber-700 text-white border-amber-600 shadow-md hover:shadow-lg"}[e],{sm:"text-sm px-3 py-2 min-h-[40px]",md:"text-sm px-4 py-3 min-h-[44px]",lg:"text-base px-6 py-3 min-h-[48px]",xl:"text-lg px-8 py-4 min-h-[52px]"}[r],a&&"w-full",n&&"opacity-50 cursor-not-allowed",o),disabled:n||s,...c,children:[s&&(0,t.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),i]})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23689:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(18962).A)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},26936:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>f});var t=s(60687),l=s(43210),a=s(85814),i=s.n(a),o=s(24528),n=s(89683),c=s(28749),d=s(2643),m=s(51907),u=s(58869),x=s(41550),h=s(48340),p=s(66232),g=s(84113),b=s(72575),v=s(23689),j=s(70334);let f=()=>{let[e,r]=(0,l.useState)(1),[s,a]=(0,l.useState)({fullName:"",email:"",phone:"",city:"",instagram:"",instagramFollowers:"",youtube:"",youtubeFollowers:"",twitter:"",twitterFollowers:"",tiktok:"",tiktokFollowers:"",snapchat:"",snapchatFollowers:"",category:"",bio:"",experience:"",storyPrice:"",postPrice:"",videoPrice:"",reelPrice:"",visitPrice:""}),f=[{number:1,title:"المعلومات الشخصية",description:"البيانات الأساسية"},{number:2,title:"وسائل التواصل",description:"حساباتك على المنصات"},{number:3,title:"المعلومات المهنية",description:"تخصصك وخبرتك"},{number:4,title:"الأسعار",description:"تحديد أسعار خدماتك"}],y=["الموضة والجمال","الطعام والمطاعم","السفر والسياحة","التقنية","الرياضة واللياقة","الصحة والعافية","التعليم","الترفيه","الأعمال والمال","نمط الحياة","الأطفال والعائلة","السيارات"],N=["الرياض","جدة","مكة المكرمة","المدينة المنورة","الدمام","الخبر","الطائف","بريدة","تبوك","خميس مشيط","حائل","الجبيل","الأحساء","نجران","ينبع","أبها"],w=(e,r)=>{a(s=>({...s,[e]:r}))};return(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-purple-50 to-pink-50",children:[(0,t.jsx)(o.A,{}),(0,t.jsx)("div",{className:"py-12",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsx)("div",{className:"mb-12",children:(0,t.jsx)("div",{className:"flex items-center justify-between",children:f.map((r,s)=>(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:`flex items-center justify-center w-10 h-10 rounded-full border-2 ${e>=r.number?"bg-primary-600 border-primary-600 text-white":"border-gray-300 text-gray-500"}`,children:e>r.number?(0,t.jsx)(v.A,{className:"h-6 w-6"}):(0,t.jsx)("span",{className:"text-sm font-medium",children:r.number})}),(0,t.jsxs)("div",{className:"mr-4 hidden sm:block",children:[(0,t.jsx)("div",{className:`text-sm font-medium ${e>=r.number?"text-primary-600":"text-gray-500"}`,children:r.title}),(0,t.jsx)("div",{className:"text-xs text-gray-500",children:r.description})]}),s<f.length-1&&(0,t.jsx)("div",{className:`w-12 h-0.5 mx-4 ${e>r.number?"bg-primary-600":"bg-gray-300"}`})]},r.number))})}),(0,t.jsx)(c.A,{className:"mb-8",children:(()=>{switch(e){case 1:return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"المعلومات الشخصية"}),(0,t.jsx)(m.A,{label:"الاسم الكامل",placeholder:"أدخل اسمك الكامل",value:s.fullName,onChange:e=>w("fullName",e.target.value),leftIcon:(0,t.jsx)(u.A,{className:"h-5 w-5"}),required:!0}),(0,t.jsx)(m.A,{label:"البريد الإلكتروني",type:"email",placeholder:"<EMAIL>",value:s.email,onChange:e=>w("email",e.target.value),leftIcon:(0,t.jsx)(x.A,{className:"h-5 w-5"}),required:!0}),(0,t.jsx)(m.A,{label:"رقم الجوال",placeholder:"05xxxxxxxx",value:s.phone,onChange:e=>w("phone",e.target.value),leftIcon:(0,t.jsx)(h.A,{className:"h-5 w-5"}),required:!0}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"المدينة"}),(0,t.jsxs)("select",{value:s.city,onChange:e=>w("city",e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500",required:!0,children:[(0,t.jsx)("option",{value:"",children:"اختر المدينة"}),N.map(e=>(0,t.jsx)("option",{value:e,children:e},e))]})]})]});case 2:return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"حساباتك على وسائل التواصل"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsx)(m.A,{label:"حساب إنستغرام",placeholder:"@username",value:s.instagram,onChange:e=>w("instagram",e.target.value),leftIcon:(0,t.jsx)(p.A,{className:"h-5 w-5"})}),(0,t.jsx)(m.A,{label:"عدد المتابعين (إنستغرام)",placeholder:"10000",type:"number",value:s.instagramFollowers,onChange:e=>w("instagramFollowers",e.target.value)})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsx)(m.A,{label:"قناة يوتيوب",placeholder:"اسم القناة أو الرابط",value:s.youtube,onChange:e=>w("youtube",e.target.value),leftIcon:(0,t.jsx)(g.A,{className:"h-5 w-5"})}),(0,t.jsx)(m.A,{label:"عدد المشتركين (يوتيوب)",placeholder:"5000",type:"number",value:s.youtubeFollowers,onChange:e=>w("youtubeFollowers",e.target.value)})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsx)(m.A,{label:"حساب تويتر",placeholder:"@username",value:s.twitter,onChange:e=>w("twitter",e.target.value),leftIcon:(0,t.jsx)(b.A,{className:"h-5 w-5"})}),(0,t.jsx)(m.A,{label:"عدد المتابعين (تويتر)",placeholder:"3000",type:"number",value:s.twitterFollowers,onChange:e=>w("twitterFollowers",e.target.value)})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsx)(m.A,{label:"حساب تيك توك",placeholder:"@username",value:s.tiktok,onChange:e=>w("tiktok",e.target.value)}),(0,t.jsx)(m.A,{label:"عدد المتابعين (تيك توك)",placeholder:"8000",type:"number",value:s.tiktokFollowers,onChange:e=>w("tiktokFollowers",e.target.value)})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsx)(m.A,{label:"حساب سناب شات",placeholder:"اسم المستخدم",value:s.snapchat,onChange:e=>w("snapchat",e.target.value)}),(0,t.jsx)(m.A,{label:"عدد المتابعين (سناب شات)",placeholder:"12000",type:"number",value:s.snapchatFollowers,onChange:e=>w("snapchatFollowers",e.target.value)})]})]});case 3:return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"المعلومات المهنية"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"التخصص الرئيسي"}),(0,t.jsxs)("select",{value:s.category,onChange:e=>w("category",e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500",required:!0,children:[(0,t.jsx)("option",{value:"",children:"اختر التخصص"}),y.map(e=>(0,t.jsx)("option",{value:e,children:e},e))]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"نبذة عنك"}),(0,t.jsx)("textarea",{value:s.bio,onChange:e=>w("bio",e.target.value),placeholder:"اكتب نبذة مختصرة عن نفسك ونوع المحتوى الذي تقدمه...",rows:4,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"سنوات الخبرة"}),(0,t.jsxs)("select",{value:s.experience,onChange:e=>w("experience",e.target.value),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500",required:!0,children:[(0,t.jsx)("option",{value:"",children:"اختر سنوات الخبرة"}),(0,t.jsx)("option",{value:"أقل من سنة",children:"أقل من سنة"}),(0,t.jsx)("option",{value:"1-2 سنة",children:"1-2 سنة"}),(0,t.jsx)("option",{value:"3-5 سنوات",children:"3-5 سنوات"}),(0,t.jsx)("option",{value:"أكثر من 5 سنوات",children:"أكثر من 5 سنوات"})]})]})]});case 4:return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"تحديد أسعار خدماتك"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:"حدد أسعارك بالريال السعودي لكل نوع من أنواع المحتوى"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsx)(m.A,{label:"سعر الستوري الواحدة (سناب شات/إنستغرام)",placeholder:"100",type:"number",value:s.storyPrice,onChange:e=>w("storyPrice",e.target.value),helperText:"السعر بالريال السعودي"}),(0,t.jsx)(m.A,{label:"سعر المنشور الواحد (إنستغرام/تويتر)",placeholder:"200",type:"number",value:s.postPrice,onChange:e=>w("postPrice",e.target.value),helperText:"السعر بالريال السعودي"}),(0,t.jsx)(m.A,{label:"سعر الفيديو الواحد (تيك توك/يوتيوب)",placeholder:"500",type:"number",value:s.videoPrice,onChange:e=>w("videoPrice",e.target.value),helperText:"السعر بالريال السعودي"}),(0,t.jsx)(m.A,{label:"سعر الريل الواحد (إنستغرام)",placeholder:"300",type:"number",value:s.reelPrice,onChange:e=>w("reelPrice",e.target.value),helperText:"السعر بالريال السعودي"}),(0,t.jsx)(m.A,{label:"سعر زيارة المتجر والتصوير",placeholder:"1000",type:"number",value:s.visitPrice,onChange:e=>w("visitPrice",e.target.value),helperText:"السعر بالريال السعودي"})]}),(0,t.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,t.jsx)("h3",{className:"font-semibold text-blue-900 mb-2",children:"ملاحظة مهمة:"}),(0,t.jsx)("p",{className:"text-blue-800 text-sm",children:"يمكنك تعديل أسعارك في أي وقت من خلال لوحة التحكم الخاصة بك بعد الموافقة على حسابك."})]})]});default:return null}})()}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsxs)(d.A,{variant:"outline",onClick:()=>{e>1&&r(e-1)},disabled:1===e,className:"flex items-center",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 ml-2"}),"السابق"]}),e<4?(0,t.jsxs)(d.A,{onClick:()=>{e<4&&r(e+1)},className:"flex items-center",children:["التالي",(0,t.jsx)(j.A,{className:"h-4 w-4 mr-2 rotate-180"})]}):(0,t.jsxs)(d.A,{onClick:()=>{console.log("Form submitted:",s),alert("تم إرسال طلب التسجيل بنجاح! سيتم مراجعته والرد عليك قريباً.")},variant:"primary",className:"flex items-center",children:["إرسال طلب التسجيل",(0,t.jsx)(v.A,{className:"h-4 w-4 mr-2"})]})]}),(0,t.jsx)("div",{className:"text-center mt-8",children:(0,t.jsxs)("p",{className:"text-gray-600",children:["لديك حساب بالفعل؟"," ",(0,t.jsx)(i(),{href:"/login",className:"text-primary-600 hover:text-primary-700 font-medium",children:"سجل دخولك هنا"})]})})]})}),(0,t.jsx)(n.A,{})]})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},48493:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>c});var t=s(65239),l=s(48088),a=s(88170),i=s.n(a),o=s(30893),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);s.d(r,n);let c={children:["",{children:["register",{children:["influencer",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,68330)),"C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\register\\influencer\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\register\\influencer\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:l.RouteKind.APP_PAGE,page:"/register/influencer/page",pathname:"/register/influencer",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},51907:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});var t=s(60687);s(43210);var l=s(49384);let a=({label:e,error:r,helperText:s,leftIcon:a,rightIcon:i,className:o,id:n,...c})=>{let d=n||`input-${Math.random().toString(36).substr(2,9)}`,m=r?"border-red-500 focus:border-red-500 focus:ring-red-500/20":"focus:border-indigo-500 focus:ring-indigo-500/20",u=a?"pr-12":i?"pl-12":"";return(0,t.jsxs)("div",{className:"w-full",children:[e&&(0,t.jsx)("label",{htmlFor:d,className:"block text-sm font-medium text-slate-200 mb-2",children:e}),(0,t.jsxs)("div",{className:"relative",children:[a&&(0,t.jsx)("div",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400",children:a}),(0,t.jsx)("input",{id:d,className:(0,l.$)("input",m,u,"bg-slate-800/90 border-slate-600 text-slate-100 placeholder-slate-400","focus:bg-slate-800 focus:ring-2 focus:ring-offset-0","transition-all duration-200",o),...c}),i&&(0,t.jsx)("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400",children:i})]}),r&&(0,t.jsx)("p",{className:"mt-2 text-sm text-red-300 font-medium",children:r}),s&&!r&&(0,t.jsx)("p",{className:"mt-2 text-sm text-slate-300",children:s})]})}},58869:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(18962).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68330:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\amshor2\\\\src\\\\app\\\\register\\\\influencer\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\register\\influencer\\page.tsx","default")},70334:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(18962).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},71491:(e,r,s)=>{Promise.resolve().then(s.bind(s,68330))},85563:(e,r,s)=>{Promise.resolve().then(s.bind(s,26936))}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[825,334,468],()=>s(48493));module.exports=t})();