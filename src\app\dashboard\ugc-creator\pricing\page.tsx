'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import {
  ArrowRight,
  DollarSign,
  Save,
  Plus,
  Trash2,
  Edit3,
  Copy,
  TrendingUp,
  Target,
  Zap,
  Clock,
  Star,
  CheckCircle
} from 'lucide-react'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'

const PricingSettingsPage: React.FC = () => {
  const [services, setServices] = useState([
    {
      id: 1,
      title: 'فيديو تيك توك احترافي',
      description: 'فيديو تيك توك مبدع ومؤثر مع مونتاج احترافي',
      category: 'فيديو',
      durations: [
        { duration: '15 ثانية', price: 200, active: true },
        { duration: '30 ثانية', price: 350, active: true },
        { duration: '60 ثانية', price: 500, active: true }
      ],
      deliveryTime: '24-48 ساعة',
      revisions: 2,
      icon: '🎵',
      active: true
    },
    {
      id: 2,
      title: 'ستوري انستقرام تفاعلي',
      description: 'ستوريز انستقرام جذابة مع عناصر تفاعلية',
      category: 'ستوري',
      durations: [
        { duration: '15 ثانية', price: 150, active: true },
        { duration: '30 ثانية', price: 250, active: true }
      ],
      deliveryTime: '24 ساعة',
      revisions: 1,
      icon: '📱',
      active: true
    },
    {
      id: 3,
      title: 'تصوير منتج احترافي',
      description: 'تصوير منتج بجودة عالية مع إضاءة احترافية',
      category: 'تصوير',
      durations: [
        { duration: '5 صور', price: 200, active: true },
        { duration: '10 صور', price: 350, active: true },
        { duration: '20 صور', price: 600, active: true }
      ],
      deliveryTime: '1-2 أيام',
      revisions: 2,
      icon: '📸',
      active: true
    }
  ])

  const [newService, setNewService] = useState({
    title: '',
    description: '',
    category: '',
    durations: [{ duration: '', price: 0, active: true }],
    deliveryTime: '',
    revisions: 1,
    icon: '⚡'
  })

  const [showAddForm, setShowAddForm] = useState(false)
  const [editingService, setEditingService] = useState<number | null>(null)

  const categories = [
    { id: 'video', label: 'فيديو', icon: '🎬' },
    { id: 'photo', label: 'تصوير', icon: '📸' },
    { id: 'design', label: 'تصميم', icon: '🎨' },
    { id: 'editing', label: 'مونتاج', icon: '✂️' },
    { id: 'story', label: 'ستوري', icon: '📱' },
    { id: 'review', label: 'مراجعة', icon: '⭐' }
  ]

  const icons = ['🎵', '📱', '📸', '⭐', '🎨', '✂️', '🎬', '📺', '💄', '👗', '🍔', '🚗', '📱', '💻', '🎧']

  const updateServicePrice = (serviceId: number, durationIndex: number, newPrice: number) => {
    setServices(prev => prev.map(service =>
      service.id === serviceId
        ? {
            ...service,
            durations: service.durations.map((dur, index) =>
              index === durationIndex ? { ...dur, price: newPrice } : dur
            )
          }
        : service
    ))
  }

  const toggleServiceActive = (serviceId: number) => {
    setServices(prev => prev.map(service =>
      service.id === serviceId ? { ...service, active: !service.active } : service
    ))
  }

  const addDuration = (serviceId: number) => {
    setServices(prev => prev.map(service =>
      service.id === serviceId
        ? {
            ...service,
            durations: [...service.durations, { duration: '', price: 0, active: true }]
          }
        : service
    ))
  }

  const removeDuration = (serviceId: number, durationIndex: number) => {
    setServices(prev => prev.map(service =>
      service.id === serviceId
        ? {
            ...service,
            durations: service.durations.filter((_, index) => index !== durationIndex)
          }
        : service
    ))
  }

  const duplicateService = (serviceId: number) => {
    const serviceToDuplicate = services.find(s => s.id === serviceId)
    if (serviceToDuplicate) {
      const newService = {
        ...serviceToDuplicate,
        id: Math.max(...services.map(s => s.id)) + 1,
        title: serviceToDuplicate.title + ' (نسخة)',
        active: false
      }
      setServices(prev => [...prev, newService])
    }
  }

  const deleteService = (serviceId: number) => {
    if (confirm('هل أنت متأكد من حذف هذه الخدمة؟')) {
      setServices(prev => prev.filter(service => service.id !== serviceId))
    }
  }

  const addNewService = () => {
    if (newService.title && newService.category) {
      const service = {
        ...newService,
        id: Math.max(...services.map(s => s.id)) + 1,
        active: true
      }
      setServices(prev => [...prev, service])
      setNewService({
        title: '',
        description: '',
        category: '',
        durations: [{ duration: '', price: 0, active: true }],
        deliveryTime: '',
        revisions: 1,
        icon: '⚡'
      })
      setShowAddForm(false)
    }
  }

  const saveAllChanges = () => {
    console.log('Saving pricing changes:', services)
    alert('تم حفظ التغييرات بنجاح! 🎉')
  }

  return (
    <div className="min-h-screen bg-slate-900">
      {/* Header */}
      <header className="bg-slate-800/80 backdrop-blur-xl shadow-2xl sticky top-0 z-50 border-b border-purple-500/20">
        <div className="max-w-7xl mx-auto mobile-container">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/dashboard/ugc-creator" className="flex items-center text-purple-400 hover:text-purple-300">
                <ArrowRight className="h-5 w-5 ml-2" />
                العودة للوحة التحكم
              </Link>
            </div>

            <div className="flex items-center space-x-4 space-x-reverse">
              <Button onClick={saveAllChanges} className="btn-primary-mobile sparkle-container">
                <Save className="h-5 w-5 ml-2" />
                حفظ التغييرات
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto mobile-container py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">إعدادات الأسعار</h1>
          <p className="text-gray-300">إدارة خدماتك وأسعارك بمرونة كاملة</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="card-mobile text-center">
            <DollarSign className="h-8 w-8 text-green-500 mx-auto mb-2" />
            <div className="text-2xl font-bold text-white">{services.filter(s => s.active).length}</div>
            <div className="text-sm text-gray-400">خدمة نشطة</div>
          </Card>

          <Card className="card-mobile text-center">
            <TrendingUp className="h-8 w-8 text-blue-500 mx-auto mb-2" />
            <div className="text-2xl font-bold text-white">
              {Math.round(services.reduce((acc, service) =>
                acc + service.durations.reduce((durAcc, dur) => durAcc + dur.price, 0) / service.durations.length, 0
              ) / services.length)} ريال
            </div>
            <div className="text-sm text-gray-400">متوسط السعر</div>
          </Card>

          <Card className="card-mobile text-center">
            <Target className="h-8 w-8 text-purple-500 mx-auto mb-2" />
            <div className="text-2xl font-bold text-white">
              {services.reduce((acc, service) => acc + service.durations.length, 0)}
            </div>
            <div className="text-sm text-gray-400">خيار سعر</div>
          </Card>

          <Card className="card-mobile text-center">
            <Star className="h-8 w-8 text-yellow-500 mx-auto mb-2" />
            <div className="text-2xl font-bold text-white">4.9</div>
            <div className="text-sm text-gray-400">تقييم الخدمات</div>
          </Card>
        </div>

        {/* Services List */}
        <div className="space-y-6">
          {services.map((service) => (
            <Card key={service.id} className="card-mobile">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center">
                  <div className="text-3xl ml-4">{service.icon}</div>
                  <div>
                    <div className="flex items-center gap-2">
                      <h3 className="text-xl font-bold text-white">{service.title}</h3>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        service.active ? 'bg-green-500/20 text-green-400' : 'bg-gray-500/20 text-gray-400'
                      }`}>
                        {service.active ? 'نشط' : 'معطل'}
                      </span>
                    </div>
                    <p className="text-gray-400">{service.description}</p>
                    <div className="flex items-center gap-4 mt-2 text-sm text-gray-500">
                      <span>📅 {service.deliveryTime}</span>
                      <span>🔄 {service.revisions} مراجعة</span>
                      <span>📂 {service.category}</span>
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={() => toggleServiceActive(service.id)}
                    className={service.active ? 'btn-secondary-mobile' : 'btn-primary-mobile'}
                  >
                    {service.active ? 'إيقاف' : 'تفعيل'}
                  </Button>

                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={() => duplicateService(service.id)}
                    className="btn-secondary-mobile"
                  >
                    <Copy className="h-4 w-4" />
                  </Button>

                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={() => deleteService(service.id)}
                    className="text-red-400 hover:text-red-300"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Duration and Pricing */}
              <div className="space-y-3">
                <h4 className="font-semibold text-white">الأسعار حسب المدة/الكمية:</h4>
                {service.durations.map((duration, index) => (
                  <div key={index} className="flex items-center gap-4 p-3 bg-white/5 rounded-xl">
                    <div className="flex-1">
                      <input
                        type="text"
                        value={duration.duration}
                        onChange={(e) => {
                          const newDurations = [...service.durations]
                          newDurations[index].duration = e.target.value
                          setServices(prev => prev.map(s =>
                            s.id === service.id ? { ...s, durations: newDurations } : s
                          ))
                        }}
                        className="w-full bg-transparent border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500"
                        placeholder="مثال: 30 ثانية"
                      />
                    </div>

                    <div className="flex items-center gap-2">
                      <input
                        type="number"
                        value={duration.price}
                        onChange={(e) => updateServicePrice(service.id, index, parseInt(e.target.value) || 0)}
                        className="w-24 bg-transparent border border-gray-600 rounded-lg px-3 py-2 text-white text-center focus:outline-none focus:border-purple-500"
                      />
                      <span className="text-gray-400">ريال</span>
                    </div>

                    {service.durations.length > 1 && (
                      <Button
                        variant="secondary"
                        size="sm"
                        onClick={() => removeDuration(service.id, index)}
                        className="text-red-400 hover:text-red-300"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                ))}

                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => addDuration(service.id)}

                >
                  <Plus className="h-4 w-4 ml-2" />
                  إضافة خيار سعر
                </Button>
              </div>
            </Card>
          ))}
        </div>

        {/* Add New Service */}
        <Card className="mt-8">
          <div className="text-center">
            {!showAddForm ? (
              <div>
                <h3 className="text-lg font-bold text-white mb-4">إضافة خدمة جديدة</h3>
                <Button
                  onClick={() => setShowAddForm(true)}
                  variant="primary"
                >
                  <Plus className="h-5 w-5 ml-2" />
                  إضافة خدمة جديدة
                </Button>
              </div>
            ) : (
              <div className="space-y-6">
                <h3 className="text-lg font-bold text-white">خدمة جديدة</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="اسم الخدمة"
                    placeholder="مثال: فيديو يوتيوب احترافي"
                    value={newService.title}
                    onChange={(e) => setNewService(prev => ({ ...prev, title: e.target.value }))}
                  />

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">الفئة</label>
                    <select
                      value={newService.category}
                      onChange={(e) => setNewService(prev => ({ ...prev, category: e.target.value }))}
                      className="input"
                    >
                      <option value="">اختر الفئة</option>
                      {categories.map(cat => (
                        <option key={cat.id} value={cat.label}>{cat.icon} {cat.label}</option>
                      ))}
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">وصف الخدمة</label>
                  <textarea
                    className="input min-h-[80px]"
                    placeholder="وصف مفصل للخدمة..."
                    value={newService.description}
                    onChange={(e) => setNewService(prev => ({ ...prev, description: e.target.value }))}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Input
                    label="وقت التسليم"
                    placeholder="مثال: 2-3 أيام"
                    value={newService.deliveryTime}
                    onChange={(e) => setNewService(prev => ({ ...prev, deliveryTime: e.target.value }))}
                  />

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">عدد المراجعات</label>
                    <select
                      value={newService.revisions}
                      onChange={(e) => setNewService(prev => ({ ...prev, revisions: parseInt(e.target.value) }))}
                      className="input"
                    >
                      <option value={1}>مراجعة واحدة</option>
                      <option value={2}>مراجعتان</option>
                      <option value={3}>3 مراجعات</option>
                      <option value={-1}>غير محدود</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">الأيقونة</label>
                    <select
                      value={newService.icon}
                      onChange={(e) => setNewService(prev => ({ ...prev, icon: e.target.value }))}
                      className="input"
                    >
                      {icons.map(icon => (
                        <option key={icon} value={icon}>{icon}</option>
                      ))}
                    </select>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold text-white mb-3">الأسعار:</h4>
                  {newService.durations.map((duration, index) => (
                    <div key={index} className="flex items-center gap-4 mb-3">
                      <input
                        type="text"
                        placeholder="مثال: 30 ثانية"
                        value={duration.duration}
                        onChange={(e) => {
                          const newDurations = [...newService.durations]
                          newDurations[index].duration = e.target.value
                          setNewService(prev => ({ ...prev, durations: newDurations }))
                        }}
                        className="flex-1 input"
                      />
                      <input
                        type="number"
                        placeholder="السعر"
                        value={duration.price || ''}
                        onChange={(e) => {
                          const newDurations = [...newService.durations]
                          newDurations[index].price = parseInt(e.target.value) || 0
                          setNewService(prev => ({ ...prev, durations: newDurations }))
                        }}
                        className="w-32 input"
                      />
                      <span className="text-gray-400">ريال</span>
                    </div>
                  ))}
                </div>

                <div className="flex gap-4 justify-center">
                  <Button variant="primary" onClick={addNewService}>
                    <CheckCircle className="h-5 w-5 ml-2" />
                    إضافة الخدمة
                  </Button>
                  <Button
                    variant="secondary"
                    onClick={() => setShowAddForm(false)}
                  >
                    إلغاء
                  </Button>
                </div>
              </div>
            )}
          </div>
        </Card>

        {/* Tips */}
        <Card className="mt-8 bg-purple-500/10 border-purple-500/20">
          <div className="text-center">
            <h3 className="text-lg font-bold text-white mb-4">💡 نصائح لتحديد الأسعار</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-300">
              <div>
                <div className="text-2xl mb-2">📊</div>
                <div className="font-medium mb-1">ادرس السوق</div>
                <div>راجع أسعار المنافسين وحدد أسعارك بناءً على جودة عملك</div>
              </div>
              <div>
                <div className="text-2xl mb-2">⏰</div>
                <div className="font-medium mb-1">احسب الوقت</div>
                <div>احسب الوقت المطلوب لكل مشروع واضرب في سعر ساعتك</div>
              </div>
              <div>
                <div className="text-2xl mb-2">📈</div>
                <div className="font-medium mb-1">ارفع تدريجياً</div>
                <div>ابدأ بأسعار معقولة وارفعها تدريجياً مع زيادة خبرتك</div>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  )
}

export default PricingSettingsPage
