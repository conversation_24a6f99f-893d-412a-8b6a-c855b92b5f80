'use client'

import React from 'react'
import { clsx } from 'clsx'

interface BadgeProps {
  children: React.ReactNode
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error'
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

const Badge: React.FC<BadgeProps> = ({ 
  children, 
  variant = 'primary',
  size = 'md',
  className = '' 
}) => {
  const variantClasses = {
    primary: 'badge-primary',
    secondary: 'bg-slate-500/10 text-slate-400 border-slate-500/20',
    success: 'badge-success',
    warning: 'badge-warning',
    error: 'bg-red-500/10 text-red-400 border-red-500/20'
  }

  const sizeClasses = {
    sm: 'text-xs px-2 py-1',
    md: 'text-sm px-3 py-1',
    lg: 'text-base px-4 py-2'
  }

  return (
    <span className={clsx(
      'badge',
      variantClasses[variant],
      sizeClasses[size],
      className
    )}>
      {children}
    </span>
  )
}

export default Badge
