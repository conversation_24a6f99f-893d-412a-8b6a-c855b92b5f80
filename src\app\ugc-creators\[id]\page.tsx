'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import {
  Star,
  Clock,
  DollarSign,
  Video,
  Camera,
  Edit3,
  Play,
  Heart,
  Eye,
  MessageCircle,
  Shield,
  CheckCircle,
  Zap,
  Award,
  TrendingUp,
  Calendar,
  MapPin,
  Languages,
  Settings,
  Share2,
  Bookmark,
  ThumbsUp,
  Download,
  ExternalLink
} from 'lucide-react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import MobileNavigation from '@/components/layout/MobileNavigation'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'

interface UGCCreatorProfileProps {
  params: {
    id: string
  }
}

const UGCCreatorProfile: React.FC<UGCCreatorProfileProps> = ({ params }) => {
  const [activeTab, setActiveTab] = useState('portfolio')
  const [selectedService, setSelectedService] = useState('')

  // Mock data for the creator
  const creator = {
    id: parseInt(params.id),
    name: 'سارة المبدعة',
    avatar: '👩‍🎨',
    coverImage: '🎬',
    rating: 4.9,
    reviewCount: 127,
    completedProjects: 89,
    responseTime: '2 ساعات',
    isVerified: true,
    isOnline: true,
    joinDate: 'انضمت في يناير 2023',
    location: 'الرياض، السعودية',
    languages: ['العربية', 'الإنجليزية'],
    experience: '3+ سنوات',
    equipment: 'معدات احترافية كاملة',
    description: 'مبدعة محتوى متخصصة في فيديوهات الجمال والموضة مع خبرة 3 سنوات في إنتاج محتوى إعلاني عالي الجودة. أعمل مع أشهر البراندات السعودية وأحرص على تقديم محتوى أصيل ومؤثر يحقق أهداف العملاء التسويقية.',
    specialties: ['تيك توك', 'انستقرام ستوريز', 'تصوير منتج', 'مراجعات', 'ريلز'],
    categories: ['جمال', 'موضة', 'لايف ستايل'],

    services: [
      {
        id: 'tiktok-video',
        title: 'فيديو تيك توك احترافي',
        description: 'فيديو تيك توك مبدع ومؤثر مع مونتاج احترافي',
        duration: '15-60 ثانية',
        price: 400,
        deliveryTime: '24-48 ساعة',
        includes: ['سيناريو مبدع', 'تصوير احترافي', 'مونتاج وتأثيرات', 'موسيقى مناسبة', 'مراجعة واحدة مجانية'],
        icon: '🎵'
      },
      {
        id: 'instagram-story',
        title: 'ستوري انستقرام تفاعلي',
        description: 'ستوريز انستقرام جذابة مع عناصر تفاعلية',
        duration: '15 ثانية',
        price: 200,
        deliveryTime: '24 ساعة',
        includes: ['تصوير عالي الجودة', 'تصميم جرافيك', 'نصوص جذابة', 'عناصر تفاعلية'],
        icon: '📱'
      },
      {
        id: 'product-review',
        title: 'مراجعة منتج شاملة',
        description: 'مراجعة صادقة ومفصلة للمنتج مع عرض المميزات',
        duration: '30-90 ثانية',
        price: 600,
        deliveryTime: '2-3 أيام',
        includes: ['تجربة شخصية', 'عرض تفصيلي', 'مقارنات', 'توصيات صادقة', 'مراجعتان مجانيتان'],
        icon: '⭐'
      },
      {
        id: 'product-photography',
        title: 'تصوير منتج احترافي',
        description: 'تصوير منتج بجودة عالية مع إضاءة احترافية',
        duration: 'صور ثابتة',
        price: 300,
        deliveryTime: '1-2 أيام',
        includes: ['10-15 صورة', 'زوايا متنوعة', 'تعديل احترافي', 'خلفيات مختلفة'],
        icon: '📸'
      }
    ],

    portfolio: [
      {
        id: 1,
        type: 'video',
        title: 'مراجعة كريم الوجه الجديد',
        thumbnail: '🧴',
        views: '25K',
        likes: '1.2K',
        duration: '45s',
        platform: 'تيك توك',
        category: 'جمال',
        description: 'مراجعة شاملة لكريم الوجه مع تجربة لمدة أسبوع'
      },
      {
        id: 2,
        type: 'video',
        title: 'ستايلنج فستان سهرة',
        thumbnail: '👗',
        views: '18K',
        likes: '890',
        duration: '30s',
        platform: 'انستقرام',
        category: 'موضة',
        description: 'طرق مختلفة لتنسيق فستان السهرة'
      },
      {
        id: 3,
        type: 'video',
        title: 'روتين العناية اليومي',
        thumbnail: '✨',
        views: '32K',
        likes: '1.8K',
        duration: '60s',
        platform: 'تيك توك',
        category: 'جمال',
        description: 'روتين العناية بالبشرة خطوة بخطوة'
      },
      {
        id: 4,
        type: 'photo',
        title: 'تصوير مجموعة مكياج',
        thumbnail: '💄',
        views: '12K',
        likes: '650',
        platform: 'انستقرام',
        category: 'جمال',
        description: 'تصوير احترافي لمجموعة مكياج جديدة'
      },
      {
        id: 5,
        type: 'video',
        title: 'تجربة عطر جديد',
        thumbnail: '🌸',
        views: '15K',
        likes: '720',
        duration: '20s',
        platform: 'ستوري',
        category: 'جمال',
        description: 'تجربة وتقييم عطر جديد مع وصف الرائحة'
      },
      {
        id: 6,
        type: 'video',
        title: 'أزياء الشتاء العصرية',
        thumbnail: '🧥',
        views: '22K',
        likes: '1.1K',
        duration: '50s',
        platform: 'ريلز',
        category: 'موضة',
        description: 'أحدث صيحات الموضة لفصل الشتاء'
      }
    ],

    reviews: [
      {
        id: 1,
        clientName: 'متجر الأناقة',
        clientAvatar: '🏪',
        rating: 5,
        date: 'منذ أسبوع',
        comment: 'عمل رائع ومحترف! سارة مبدعة حقيقية وتفهم متطلبات البراند بشكل ممتاز. النتائج فاقت التوقعات والتفاعل كان مذهل.',
        project: 'فيديو تيك توك لمجموعة الشتاء'
      },
      {
        id: 2,
        clientName: 'براند الجمال',
        clientAvatar: '💄',
        rating: 5,
        date: 'منذ أسبوعين',
        comment: 'تعامل احترافي جداً والتسليم في الوقت المحدد. المحتوى كان أصيل ومؤثر وحقق نتائج ممتازة في المبيعات.',
        project: 'مراجعة منتجات العناية'
      },
      {
        id: 3,
        clientName: 'شركة الموضة',
        clientAvatar: '👗',
        rating: 4,
        date: 'منذ شهر',
        comment: 'جودة عالية في التصوير والمونتاج. سارة متعاونة جداً وتقبل التعديلات بصدر رحب. أنصح بالتعامل معها.',
        project: 'ستوريز انستقرام للمجموعة الجديدة'
      }
    ],

    stats: {
      totalViews: '2.5M',
      totalLikes: '125K',
      avgEngagement: '8.5%',
      repeatClients: '85%',
      onTimeDelivery: '98%',
      clientSatisfaction: '4.9/5'
    }
  }

  const tabs = [
    { id: 'portfolio', label: 'معرض الأعمال', icon: Video },
    { id: 'services', label: 'الخدمات', icon: Star },
    { id: 'reviews', label: 'التقييمات', icon: MessageCircle },
    { id: 'about', label: 'نبذة', icon: Award }
  ]

  const renderTabContent = () => {
    switch (activeTab) {
      case 'portfolio':
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {creator.portfolio.map((item) => (
              <Card key={item.id} hover className="card-mobile group cursor-pointer">
                <div className="relative">
                  <div className="w-full h-48 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-xl flex items-center justify-center text-4xl mb-4 group-hover:scale-105 transition-transform">
                    {item.thumbnail}
                    <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-xl flex items-center justify-center">
                      <Play className="h-12 w-12 text-white" />
                    </div>
                  </div>
                  <div className="absolute top-2 right-2 bg-black/70 text-white px-2 py-1 rounded-lg text-xs">
                    {item.platform}
                  </div>
                  {item.duration && (
                    <div className="absolute bottom-2 left-2 bg-black/70 text-white px-2 py-1 rounded-lg text-xs">
                      {item.duration}
                    </div>
                  )}
                </div>

                <h3 className="text-white font-semibold mb-2 line-clamp-2">{item.title}</h3>
                <p className="text-gray-400 text-sm mb-3 line-clamp-2">{item.description}</p>

                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center text-gray-400">
                    <Eye className="h-4 w-4 ml-1" />
                    <span>{item.views}</span>
                  </div>
                  <div className="flex items-center text-gray-400">
                    <ThumbsUp className="h-4 w-4 ml-1" />
                    <span>{item.likes}</span>
                  </div>
                  <span className="px-2 py-1 bg-purple-500/20 text-purple-300 rounded-full text-xs">
                    {item.category}
                  </span>
                </div>
              </Card>
            ))}
          </div>
        )

      case 'services':
        return (
          <div className="space-y-6">
            {creator.services.map((service) => (
              <Card key={service.id} className="card-mobile">
                <div className="flex flex-col lg:flex-row lg:items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center mb-3">
                      <div className="text-3xl ml-3">{service.icon}</div>
                      <div>
                        <h3 className="text-white font-bold text-lg">{service.title}</h3>
                        <p className="text-gray-400">{service.description}</p>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-4">
                      <div>
                        <div className="text-gray-400 text-sm">المدة</div>
                        <div className="text-white font-medium">{service.duration}</div>
                      </div>
                      <div>
                        <div className="text-gray-400 text-sm">التسليم</div>
                        <div className="text-white font-medium">{service.deliveryTime}</div>
                      </div>
                      <div>
                        <div className="text-gray-400 text-sm">السعر</div>
                        <div className="text-white font-bold text-lg">{service.price} ريال</div>
                      </div>
                    </div>

                    <div>
                      <div className="text-gray-400 text-sm mb-2">يشمل:</div>
                      <div className="flex flex-wrap gap-2">
                        {service.includes.map((item, i) => (
                          <span key={i} className="px-2 py-1 bg-green-500/20 text-green-300 text-xs rounded-full">
                            ✓ {item}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="mt-4 lg:mt-0 lg:mr-6">
                    <Link href={`/ugc-creators/${creator.id}/order?service=${service.id}`}>
                      <Button className="btn-primary-mobile w-full lg:w-auto">
                        اطلب الخدمة
                        <Zap className="h-4 w-4 mr-2" />
                      </Button>
                    </Link>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        )

      case 'reviews':
        return (
          <div className="space-y-6">
            {creator.reviews.map((review) => (
              <Card key={review.id} className="card-mobile">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-full flex items-center justify-center text-xl ml-3">
                      {review.clientAvatar}
                    </div>
                    <div>
                      <div className="text-white font-semibold">{review.clientName}</div>
                      <div className="flex items-center">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`h-4 w-4 ${i < review.rating ? 'text-yellow-400 fill-current' : 'text-gray-600'}`}
                          />
                        ))}
                        <span className="text-gray-400 text-sm mr-2">{review.date}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <p className="text-gray-300 mb-3 leading-relaxed">{review.comment}</p>

                <div className="text-sm text-gray-400 bg-white/5 rounded-lg p-2">
                  المشروع: {review.project}
                </div>
              </Card>
            ))}
          </div>
        )

      case 'about':
        return (
          <div className="space-y-6">
            <Card className="card-mobile">
              <h3 className="text-white font-bold text-lg mb-4">نبذة شخصية</h3>
              <p className="text-gray-300 leading-relaxed">{creator.description}</p>
            </Card>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="card-mobile">
                <h3 className="text-white font-bold mb-4">المعلومات الأساسية</h3>
                <div className="space-y-3">
                  <div className="flex items-center">
                    <MapPin className="h-4 w-4 text-gray-400 ml-2" />
                    <span className="text-gray-300">{creator.location}</span>
                  </div>
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 text-gray-400 ml-2" />
                    <span className="text-gray-300">{creator.joinDate}</span>
                  </div>
                  <div className="flex items-center">
                    <Languages className="h-4 w-4 text-gray-400 ml-2" />
                    <span className="text-gray-300">{creator.languages.join(', ')}</span>
                  </div>
                  <div className="flex items-center">
                    <Award className="h-4 w-4 text-gray-400 ml-2" />
                    <span className="text-gray-300">{creator.experience}</span>
                  </div>
                  <div className="flex items-center">
                    <Camera className="h-4 w-4 text-gray-400 ml-2" />
                    <span className="text-gray-300">{creator.equipment}</span>
                  </div>
                </div>
              </Card>

              <Card className="card-mobile">
                <h3 className="text-white font-bold mb-4">إحصائيات الأداء</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-cyan-400">{creator.stats.totalViews}</div>
                    <div className="text-gray-400 text-sm">إجمالي المشاهدات</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-pink-400">{creator.stats.totalLikes}</div>
                    <div className="text-gray-400 text-sm">إجمالي الإعجابات</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-400">{creator.stats.avgEngagement}</div>
                    <div className="text-gray-400 text-sm">متوسط التفاعل</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-400">{creator.stats.onTimeDelivery}</div>
                    <div className="text-gray-400 text-sm">التسليم في الوقت</div>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className="min-h-screen bg-slate-900">
      <Header />

      {/* Hero Section */}
      <section className="py-12 bg-gradient-to-r from-purple-600 via-pink-600 to-cyan-600">
        <div className="max-w-7xl mx-auto mobile-container">
          <div className="flex flex-col lg:flex-row items-start gap-8">
            {/* Creator Info */}
            <div className="flex-1">
              <div className="flex items-start gap-6">
                <div className="relative">
                  <div className="w-24 h-24 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-4xl">
                    {creator.avatar}
                  </div>
                  <div className={`absolute -bottom-1 -right-1 w-6 h-6 rounded-full border-2 border-white ${creator.isOnline ? 'bg-green-400' : 'bg-gray-400'}`}></div>
                </div>

                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <h1 className="text-3xl font-bold text-white">{creator.name}</h1>
                    {creator.isVerified && (
                      <CheckCircle className="h-6 w-6 text-cyan-400" />
                    )}
                  </div>

                  <div className="flex items-center gap-4 mb-3">
                    <div className="flex items-center">
                      <Star className="h-5 w-5 text-yellow-400 fill-current ml-1" />
                      <span className="text-white font-semibold">{creator.rating}</span>
                      <span className="text-gray-200 mr-1">({creator.reviewCount} تقييم)</span>
                    </div>
                    <div className="text-gray-200">
                      {creator.completedProjects} مشروع مكتمل
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-2 mb-4">
                    {creator.specialties.map((specialty, i) => (
                      <span key={i} className="px-3 py-1 bg-white/20 text-white text-sm rounded-full">
                        {specialty}
                      </span>
                    ))}
                  </div>

                  <div className="flex items-center gap-4 text-gray-200 text-sm">
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 ml-1" />
                      <span>يرد خلال {creator.responseTime}</span>
                    </div>
                    <div className="flex items-center">
                      <MapPin className="h-4 w-4 ml-1" />
                      <span>{creator.location}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col gap-3 w-full lg:w-auto">
              <Link href={`/ugc-creators/${creator.id}/order`}>
                <Button className="btn-primary-mobile sparkle-container w-full">
                  <MessageCircle className="h-5 w-5 ml-2" />
                  اطلب خدمة الآن
                </Button>
              </Link>
              <div className="flex gap-2">
                <Button variant="secondary" className="btn-secondary-mobile flex-1">
                  <Heart className="h-4 w-4 ml-1" />
                  إعجاب
                </Button>
                <Button variant="secondary" className="btn-secondary-mobile flex-1">
                  <Share2 className="h-4 w-4 ml-1" />
                  مشاركة
                </Button>
                <Button variant="secondary" className="btn-secondary-mobile">
                  <Bookmark className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Trust Badges */}
          <div className="flex flex-wrap items-center gap-4 mt-6">
            <div className="saudi-trust-badge">
              <Shield className="w-4 h-4 ml-2" />
              🛡️ مبدع معتمد وموثوق
            </div>
            <div className="saudi-trust-badge">
              <CheckCircle className="w-4 h-4 ml-2" />
              ✅ ضمان الجودة 100%
            </div>
            <div className="saudi-trust-badge">
              <Clock className="w-4 h-4 ml-2" />
              ⏰ تسليم في الوقت المحدد
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <div className="py-12">
        <div className="max-w-7xl mx-auto mobile-container">
          {/* Tabs */}
          <div className="mb-8">
            <div className="flex flex-wrap gap-2">
              {tabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center px-6 py-3 rounded-2xl font-medium transition-all duration-300 ${
                      activeTab === tab.id
                        ? 'bg-purple-500 text-white'
                        : 'bg-white/10 text-gray-300 hover:bg-white/20'
                    }`}
                  >
                    <Icon className="h-5 w-5 ml-2" />
                    {tab.label}
                  </button>
                )
              })}
            </div>
          </div>

          {/* Tab Content */}
          {renderTabContent()}
        </div>
      </div>

      <Footer />
      <MobileNavigation />
    </div>
  )
}

export default UGCCreatorProfile
