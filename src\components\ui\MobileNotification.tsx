'use client'

import React, { useState, useEffect } from 'react'
import { X, CheckCircle, AlertCircle, Info, Star, Shield } from 'lucide-react'

interface NotificationProps {
  type?: 'success' | 'error' | 'info' | 'warning' | 'trust'
  title: string
  message: string
  duration?: number
  onClose?: () => void
  showCloseButton?: boolean
}

const MobileNotification: React.FC<NotificationProps> = ({
  type = 'info',
  title,
  message,
  duration = 5000,
  onClose,
  showCloseButton = true
}) => {
  const [isVisible, setIsVisible] = useState(true)

  useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(() => {
        handleClose()
      }, duration)

      return () => clearTimeout(timer)
    }
  }, [duration])

  const handleClose = () => {
    setIsVisible(false)
    setTimeout(() => {
      onClose?.()
    }, 300)
  }

  const getNotificationConfig = () => {
    switch (type) {
      case 'success':
        return {
          icon: CheckCircle,
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          iconColor: 'text-green-600',
          titleColor: 'text-green-800',
          messageColor: 'text-green-700'
        }
      case 'error':
        return {
          icon: AlertCircle,
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          iconColor: 'text-red-600',
          titleColor: 'text-red-800',
          messageColor: 'text-red-700'
        }
      case 'warning':
        return {
          icon: AlertCircle,
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          iconColor: 'text-yellow-600',
          titleColor: 'text-yellow-800',
          messageColor: 'text-yellow-700'
        }
      case 'trust':
        return {
          icon: Shield,
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          iconColor: 'text-blue-600',
          titleColor: 'text-blue-800',
          messageColor: 'text-blue-700'
        }
      default:
        return {
          icon: Info,
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          iconColor: 'text-gray-600',
          titleColor: 'text-gray-800',
          messageColor: 'text-gray-700'
        }
    }
  }

  const config = getNotificationConfig()
  const Icon = config.icon

  if (!isVisible) return null

  return (
    <div className={`fixed top-4 left-4 right-4 z-50 transition-all duration-300 ${
      isVisible ? 'translate-y-0 opacity-100' : '-translate-y-full opacity-0'
    } md:max-w-md md:left-auto md:right-4`}>
      <div className={`${config.bgColor} ${config.borderColor} border-2 rounded-2xl p-4 shadow-lg backdrop-blur-sm`}>
        <div className="flex items-start">
          <div className={`flex-shrink-0 ${config.iconColor}`}>
            <Icon className="h-6 w-6" />
          </div>
          
          <div className="mr-3 flex-1">
            <h3 className={`text-sm font-semibold ${config.titleColor}`}>
              {title}
            </h3>
            <p className={`text-sm mt-1 ${config.messageColor}`}>
              {message}
            </p>
          </div>
          
          {showCloseButton && (
            <button
              onClick={handleClose}
              className="flex-shrink-0 text-gray-400 hover:text-gray-600 transition-colors touch-target"
            >
              <X className="h-5 w-5" />
            </button>
          )}
        </div>
        
        {/* شريط التقدم */}
        {duration > 0 && (
          <div className="mt-3 w-full bg-gray-200 rounded-full h-1">
            <div 
              className={`h-1 rounded-full transition-all ease-linear ${
                type === 'success' ? 'bg-green-500' :
                type === 'error' ? 'bg-red-500' :
                type === 'warning' ? 'bg-yellow-500' :
                type === 'trust' ? 'bg-blue-500' :
                'bg-gray-500'
              }`}
              style={{
                width: '100%',
                animation: `shrink ${duration}ms linear forwards`
              }}
            />
          </div>
        )}
      </div>
      
      <style jsx>{`
        @keyframes shrink {
          from { width: 100%; }
          to { width: 0%; }
        }
      `}</style>
    </div>
  )
}

// مكون مدير الإشعارات
interface NotificationManagerProps {
  notifications: Array<{
    id: string
    type?: 'success' | 'error' | 'info' | 'warning' | 'trust'
    title: string
    message: string
    duration?: number
  }>
  onRemove: (id: string) => void
}

export const NotificationManager: React.FC<NotificationManagerProps> = ({
  notifications,
  onRemove
}) => {
  return (
    <div className="fixed top-0 left-0 right-0 z-50 pointer-events-none">
      {notifications.map((notification, index) => (
        <div
          key={notification.id}
          className="pointer-events-auto"
          style={{ 
            transform: `translateY(${index * 80}px)`,
            zIndex: 50 - index
          }}
        >
          <MobileNotification
            {...notification}
            onClose={() => onRemove(notification.id)}
          />
        </div>
      ))}
    </div>
  )
}

// Hook لإدارة الإشعارات
export const useNotifications = () => {
  const [notifications, setNotifications] = useState<Array<{
    id: string
    type?: 'success' | 'error' | 'info' | 'warning' | 'trust'
    title: string
    message: string
    duration?: number
  }>>([])

  const addNotification = (notification: Omit<typeof notifications[0], 'id'>) => {
    const id = Math.random().toString(36).substr(2, 9)
    setNotifications(prev => [...prev, { ...notification, id }])
  }

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id))
  }

  const showSuccess = (title: string, message: string) => {
    addNotification({ type: 'success', title, message })
  }

  const showError = (title: string, message: string) => {
    addNotification({ type: 'error', title, message })
  }

  const showInfo = (title: string, message: string) => {
    addNotification({ type: 'info', title, message })
  }

  const showWarning = (title: string, message: string) => {
    addNotification({ type: 'warning', title, message })
  }

  const showTrust = (title: string, message: string) => {
    addNotification({ type: 'trust', title, message })
  }

  return {
    notifications,
    addNotification,
    removeNotification,
    showSuccess,
    showError,
    showInfo,
    showWarning,
    showTrust
  }
}

export default MobileNotification
