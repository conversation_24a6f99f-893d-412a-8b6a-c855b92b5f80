'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import { 
  Mail, 
  ArrowLeft, 
  Shield,
  CheckCircle,
  Clock
} from 'lucide-react'

const ForgotPasswordPage: React.FC = () => {
  const [email, setEmail] = useState('')
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false)
      setIsSubmitted(true)
    }, 2000)
  }

  if (isSubmitted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
        <Header />
        
        <div className="py-20">
          <div className="max-w-md mx-auto px-4 sm:px-6 lg:px-8">
            <Card className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              
              <h1 className="text-2xl font-bold text-gray-900 mb-4">
                تم إرسال الرابط بنجاح!
              </h1>
              
              <p className="text-gray-600 mb-6 leading-relaxed">
                تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني:
              </p>
              
              <div className="bg-gray-50 rounded-lg p-4 mb-6">
                <p className="font-medium text-gray-900">{email}</p>
              </div>
              
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <div className="flex items-start">
                  <Clock className="h-5 w-5 text-blue-600 mt-0.5 ml-3 flex-shrink-0" />
                  <div className="text-sm text-blue-800">
                    <p className="font-medium mb-1">ملاحظة مهمة:</p>
                    <p>الرابط صالح لمدة 24 ساعة فقط. إذا لم تجد الرسالة، تحقق من مجلد الرسائل غير المرغوب فيها.</p>
                  </div>
                </div>
              </div>
              
              <div className="space-y-3">
                <Button 
                  onClick={() => setIsSubmitted(false)}
                  variant="outline" 
                  className="w-full"
                >
                  إرسال مرة أخرى
                </Button>
                
                <Link href="/login">
                  <Button variant="ghost" className="w-full">
                    العودة لتسجيل الدخول
                  </Button>
                </Link>
              </div>
            </Card>
          </div>
        </div>
        
        <Footer />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
      <Header />
      
      <div className="py-20">
        <div className="max-w-md mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
              <Shield className="h-8 w-8 text-white" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              نسيت كلمة المرور؟
            </h1>
            <p className="text-gray-600">
              لا تقلق، سنرسل لك رابط إعادة تعيين كلمة المرور
            </p>
          </div>

          {/* Reset Form */}
          <Card>
            <form onSubmit={handleSubmit} className="space-y-6">
              <Input
                label="البريد الإلكتروني"
                type="email"
                placeholder="أدخل بريدك الإلكتروني"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                leftIcon={<Mail className="h-5 w-5" />}
                helperText="سنرسل رابط إعادة التعيين إلى هذا البريد"
                required
              />

              <Button
                type="submit"
                size="lg"
                className="w-full"
                isLoading={isLoading}
              >
                {isLoading ? 'جاري الإرسال...' : 'إرسال رابط إعادة التعيين'}
                {!isLoading && <ArrowLeft className="h-5 w-5 mr-3" />}
              </Button>
            </form>
          </Card>

          {/* Back to Login */}
          <div className="text-center mt-6">
            <Link href="/login" className="text-primary-600 hover:text-primary-700 font-medium">
              العودة إلى تسجيل الدخول
            </Link>
          </div>

          {/* Help Section */}
          <Card className="mt-8 bg-gray-50 border-gray-200">
            <h3 className="font-semibold text-gray-900 mb-3">
              تحتاج مساعدة؟
            </h3>
            <div className="space-y-2 text-sm text-gray-600">
              <p>• تأكد من كتابة البريد الإلكتروني بشكل صحيح</p>
              <p>• تحقق من مجلد الرسائل غير المرغوب فيها</p>
              <p>• إذا لم تتلق الرسالة، جرب مرة أخرى بعد دقائق</p>
            </div>
            <div className="mt-4 pt-4 border-t border-gray-300">
              <p className="text-sm text-gray-600">
                لا تزال تواجه مشكلة؟{' '}
                <Link href="/contact" className="text-primary-600 hover:text-primary-700 font-medium">
                  تواصل مع الدعم الفني
                </Link>
              </p>
            </div>
          </Card>
        </div>
      </div>

      <Footer />
    </div>
  )
}

export default ForgotPasswordPage
