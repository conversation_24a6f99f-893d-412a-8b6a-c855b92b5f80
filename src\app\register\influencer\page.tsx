'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Instagram, 
  Youtube, 
  Twitter,
  Camera,
  Upload,
  ArrowRight,
  CheckCircle
} from 'lucide-react'

const InfluencerRegisterPage: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(1)
  const [formData, setFormData] = useState({
    // Personal Info
    fullName: '',
    email: '',
    phone: '',
    city: '',
    
    // Social Media
    instagram: '',
    instagramFollowers: '',
    youtube: '',
    youtubeFollowers: '',
    twitter: '',
    twitterFollowers: '',
    tiktok: '',
    tiktokFollowers: '',
    snapchat: '',
    snapchatFollowers: '',
    
    // Professional Info
    category: '',
    bio: '',
    experience: '',
    
    // Pricing
    storyPrice: '',
    postPrice: '',
    videoPrice: '',
    reelPrice: '',
    visitPrice: ''
  })

  const steps = [
    { number: 1, title: 'المعلومات الشخصية', description: 'البيانات الأساسية' },
    { number: 2, title: 'وسائل التواصل', description: 'حساباتك على المنصات' },
    { number: 3, title: 'المعلومات المهنية', description: 'تخصصك وخبرتك' },
    { number: 4, title: 'الأسعار', description: 'تحديد أسعار خدماتك' }
  ]

  const categories = [
    'الموضة والجمال',
    'الطعام والمطاعم',
    'السفر والسياحة',
    'التقنية',
    'الرياضة واللياقة',
    'الصحة والعافية',
    'التعليم',
    'الترفيه',
    'الأعمال والمال',
    'نمط الحياة',
    'الأطفال والعائلة',
    'السيارات'
  ]

  const cities = [
    'الرياض',
    'جدة',
    'مكة المكرمة',
    'المدينة المنورة',
    'الدمام',
    'الخبر',
    'الطائف',
    'بريدة',
    'تبوك',
    'خميس مشيط',
    'حائل',
    'الجبيل',
    'الأحساء',
    'نجران',
    'ينبع',
    'أبها'
  ]

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleNext = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleSubmit = () => {
    // Handle form submission
    console.log('Form submitted:', formData)
    alert('تم إرسال طلب التسجيل بنجاح! سيتم مراجعته والرد عليك قريباً.')
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">المعلومات الشخصية</h2>
            
            <Input
              label="الاسم الكامل"
              placeholder="أدخل اسمك الكامل"
              value={formData.fullName}
              onChange={(e) => handleInputChange('fullName', e.target.value)}
              leftIcon={<User className="h-5 w-5" />}
              required
            />
            
            <Input
              label="البريد الإلكتروني"
              type="email"
              placeholder="<EMAIL>"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              leftIcon={<Mail className="h-5 w-5" />}
              required
            />
            
            <Input
              label="رقم الجوال"
              placeholder="05xxxxxxxx"
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              leftIcon={<Phone className="h-5 w-5" />}
              required
            />
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">المدينة</label>
              <select
                value={formData.city}
                onChange={(e) => handleInputChange('city', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              >
                <option value="">اختر المدينة</option>
                {cities.map((city) => (
                  <option key={city} value={city}>{city}</option>
                ))}
              </select>
            </div>
          </div>
        )

      case 2:
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">حساباتك على وسائل التواصل</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="حساب إنستغرام"
                placeholder="@username"
                value={formData.instagram}
                onChange={(e) => handleInputChange('instagram', e.target.value)}
                leftIcon={<Instagram className="h-5 w-5" />}
              />
              <Input
                label="عدد المتابعين (إنستغرام)"
                placeholder="10000"
                type="number"
                value={formData.instagramFollowers}
                onChange={(e) => handleInputChange('instagramFollowers', e.target.value)}
              />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="قناة يوتيوب"
                placeholder="اسم القناة أو الرابط"
                value={formData.youtube}
                onChange={(e) => handleInputChange('youtube', e.target.value)}
                leftIcon={<Youtube className="h-5 w-5" />}
              />
              <Input
                label="عدد المشتركين (يوتيوب)"
                placeholder="5000"
                type="number"
                value={formData.youtubeFollowers}
                onChange={(e) => handleInputChange('youtubeFollowers', e.target.value)}
              />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="حساب تويتر"
                placeholder="@username"
                value={formData.twitter}
                onChange={(e) => handleInputChange('twitter', e.target.value)}
                leftIcon={<Twitter className="h-5 w-5" />}
              />
              <Input
                label="عدد المتابعين (تويتر)"
                placeholder="3000"
                type="number"
                value={formData.twitterFollowers}
                onChange={(e) => handleInputChange('twitterFollowers', e.target.value)}
              />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="حساب تيك توك"
                placeholder="@username"
                value={formData.tiktok}
                onChange={(e) => handleInputChange('tiktok', e.target.value)}
              />
              <Input
                label="عدد المتابعين (تيك توك)"
                placeholder="8000"
                type="number"
                value={formData.tiktokFollowers}
                onChange={(e) => handleInputChange('tiktokFollowers', e.target.value)}
              />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="حساب سناب شات"
                placeholder="اسم المستخدم"
                value={formData.snapchat}
                onChange={(e) => handleInputChange('snapchat', e.target.value)}
              />
              <Input
                label="عدد المتابعين (سناب شات)"
                placeholder="12000"
                type="number"
                value={formData.snapchatFollowers}
                onChange={(e) => handleInputChange('snapchatFollowers', e.target.value)}
              />
            </div>
          </div>
        )

      case 3:
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">المعلومات المهنية</h2>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">التخصص الرئيسي</label>
              <select
                value={formData.category}
                onChange={(e) => handleInputChange('category', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              >
                <option value="">اختر التخصص</option>
                {categories.map((category) => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">نبذة عنك</label>
              <textarea
                value={formData.bio}
                onChange={(e) => handleInputChange('bio', e.target.value)}
                placeholder="اكتب نبذة مختصرة عن نفسك ونوع المحتوى الذي تقدمه..."
                rows={4}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">سنوات الخبرة</label>
              <select
                value={formData.experience}
                onChange={(e) => handleInputChange('experience', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              >
                <option value="">اختر سنوات الخبرة</option>
                <option value="أقل من سنة">أقل من سنة</option>
                <option value="1-2 سنة">1-2 سنة</option>
                <option value="3-5 سنوات">3-5 سنوات</option>
                <option value="أكثر من 5 سنوات">أكثر من 5 سنوات</option>
              </select>
            </div>
          </div>
        )

      case 4:
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">تحديد أسعار خدماتك</h2>
            <p className="text-gray-600 mb-6">حدد أسعارك بالريال السعودي لكل نوع من أنواع المحتوى</p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="سعر الستوري الواحدة (سناب شات/إنستغرام)"
                placeholder="100"
                type="number"
                value={formData.storyPrice}
                onChange={(e) => handleInputChange('storyPrice', e.target.value)}
                helperText="السعر بالريال السعودي"
              />
              
              <Input
                label="سعر المنشور الواحد (إنستغرام/تويتر)"
                placeholder="200"
                type="number"
                value={formData.postPrice}
                onChange={(e) => handleInputChange('postPrice', e.target.value)}
                helperText="السعر بالريال السعودي"
              />
              
              <Input
                label="سعر الفيديو الواحد (تيك توك/يوتيوب)"
                placeholder="500"
                type="number"
                value={formData.videoPrice}
                onChange={(e) => handleInputChange('videoPrice', e.target.value)}
                helperText="السعر بالريال السعودي"
              />
              
              <Input
                label="سعر الريل الواحد (إنستغرام)"
                placeholder="300"
                type="number"
                value={formData.reelPrice}
                onChange={(e) => handleInputChange('reelPrice', e.target.value)}
                helperText="السعر بالريال السعودي"
              />
              
              <Input
                label="سعر زيارة المتجر والتصوير"
                placeholder="1000"
                type="number"
                value={formData.visitPrice}
                onChange={(e) => handleInputChange('visitPrice', e.target.value)}
                helperText="السعر بالريال السعودي"
              />
            </div>
            
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="font-semibold text-blue-900 mb-2">ملاحظة مهمة:</h3>
              <p className="text-blue-800 text-sm">
                يمكنك تعديل أسعارك في أي وقت من خلال لوحة التحكم الخاصة بك بعد الموافقة على حسابك.
              </p>
            </div>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-pink-50">
      <Header />
      
      <div className="py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Progress Steps */}
          <div className="mb-12">
            <div className="flex items-center justify-between">
              {steps.map((step, index) => (
                <div key={step.number} className="flex items-center">
                  <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                    currentStep >= step.number 
                      ? 'bg-primary-600 border-primary-600 text-white' 
                      : 'border-gray-300 text-gray-500'
                  }`}>
                    {currentStep > step.number ? (
                      <CheckCircle className="h-6 w-6" />
                    ) : (
                      <span className="text-sm font-medium">{step.number}</span>
                    )}
                  </div>
                  <div className="mr-4 hidden sm:block">
                    <div className={`text-sm font-medium ${
                      currentStep >= step.number ? 'text-primary-600' : 'text-gray-500'
                    }`}>
                      {step.title}
                    </div>
                    <div className="text-xs text-gray-500">{step.description}</div>
                  </div>
                  {index < steps.length - 1 && (
                    <div className={`w-12 h-0.5 mx-4 ${
                      currentStep > step.number ? 'bg-primary-600' : 'bg-gray-300'
                    }`}></div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Form Content */}
          <Card className="mb-8">
            {renderStepContent()}
          </Card>

          {/* Navigation Buttons */}
          <div className="flex justify-between">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentStep === 1}
              className="flex items-center"
            >
              <ArrowRight className="h-4 w-4 ml-2" />
              السابق
            </Button>

            {currentStep < 4 ? (
              <Button onClick={handleNext} className="flex items-center">
                التالي
                <ArrowRight className="h-4 w-4 mr-2 rotate-180" />
              </Button>
            ) : (
              <Button onClick={handleSubmit} variant="primary" className="flex items-center">
                إرسال طلب التسجيل
                <CheckCircle className="h-4 w-4 mr-2" />
              </Button>
            )}
          </div>

          {/* Login Link */}
          <div className="text-center mt-8">
            <p className="text-gray-600">
              لديك حساب بالفعل؟{' '}
              <Link href="/login" className="text-primary-600 hover:text-primary-700 font-medium">
                سجل دخولك هنا
              </Link>
            </p>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  )
}

export default InfluencerRegisterPage
