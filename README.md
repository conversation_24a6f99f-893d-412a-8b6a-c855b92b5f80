# منصة المؤثرين السعودية

منصة احترافية لربط المؤثرين بالتجار في المملكة العربية السعودية مع ضمان الأموال ونظام الدفع الآمن.

## المميزات الرئيسية

### للتجار
- **تصفح المؤثرين**: استكشف آلاف المؤثرين المعتمدين
- **فلترة متقدمة**: ابحث حسب المدينة، التخصص، عدد المتابعين، والأسعار
- **ضمان الأموال**: نظام ضمان آمن يحمي استثمارك
- **تقارير مفصلة**: احصل على إحصائيات شاملة عن أداء حملاتك
- **دعم فني 24/7**: فريق دعم متخصص متاح دائماً

### للمؤثرين
- **ملف شخصي احترافي**: اعرض خبراتك ومحتواك بطريقة جذابة
- **تحكم في الأسعار**: حدد أسعارك لكل نوع من أنواع المحتوى
- **دفع سريع وآمن**: احصل على أجرك فور اكتمال الحملة
- **إدارة الطلبات**: نظام سهل لإدارة طلبات التعاون
- **بناء السمعة**: نظام تقييم يساعدك في بناء سمعة إيجابية

## التقنيات المستخدمة

- **Frontend**: Next.js 14, React 18, TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Radix UI, Lucide React
- **Forms**: React Hook Form, Zod
- **Language**: Arabic (RTL Support)

## هيكل المشروع

```
src/
├── app/                    # Next.js App Router
│   ├── globals.css        # الأنماط العامة
│   ├── layout.tsx         # التخطيط الرئيسي
│   ├── page.tsx           # الصفحة الرئيسية
│   ├── login/             # صفحة تسجيل الدخول
│   ├── register/          # صفحات التسجيل
│   ├── influencers/       # صفحات المؤثرين
│   └── how-it-works/      # صفحة كيف يعمل
├── components/            # المكونات القابلة لإعادة الاستخدام
│   ├── ui/               # مكونات الواجهة الأساسية
│   └── layout/           # مكونات التخطيط
```

## الصفحات المتاحة

### الصفحات العامة
- `/` - الصفحة الرئيسية
- `/how-it-works` - كيف تعمل المنصة
- `/influencers` - تصفح المؤثرين
- `/influencers/[id]` - صفحة المؤثر الفردية
- `/merchants` - صفحة للتجار
- `/pricing` - صفحة الأسعار
- `/about` - من نحن
- `/contact` - اتصل بنا

### صفحات التسجيل والدخول
- `/register` - اختيار نوع الحساب
- `/register/influencer` - تسجيل المؤثرين
- `/register/merchant` - تسجيل التجار
- `/login` - تسجيل الدخول
- `/forgot-password` - نسيت كلمة المرور

### صفحات الدعم والمساعدة
- `/help` - مركز المساعدة
- `/faq` - الأسئلة الشائعة
- `/support` - الدعم الفني

### الصفحات القانونية
- `/terms` - شروط الاستخدام
- `/privacy` - سياسة الخصوصية
- `/refund` - سياسة الاسترداد

### لوحات التحكم
- `/dashboard/influencer` - لوحة تحكم المؤثر
- `/dashboard/merchant` - لوحة تحكم التاجر

## المكونات الرئيسية

### مكونات الواجهة
- `Button` - أزرار بتصاميم متنوعة
- `Card` - بطاقات لعرض المحتوى
- `Input` - حقول الإدخال مع التحقق
- `Header` - شريط التنقل العلوي
- `Footer` - تذييل الصفحة

### الميزات المطبقة
- ✅ تصميم متجاوب (Responsive Design)
- ✅ دعم اللغة العربية (RTL)
- ✅ نظام التنقل الكامل
- ✅ صفحات التسجيل للمؤثرين والتجار
- ✅ صفحة عرض المؤثرين مع الفلترة
- ✅ صفحة الملف الشخصي للمؤثر الفردي
- ✅ صفحات الدعم والمساعدة (مركز المساعدة، الأسئلة الشائعة، الدعم الفني)
- ✅ الصفحات القانونية (شروط الاستخدام، سياسة الخصوصية، سياسة الاسترداد)
- ✅ صفحة الأسعار والباقات
- ✅ صفحة "من نحن" و "اتصل بنا"
- ✅ صفحة للتجار مع عرض المميزات
- ✅ لوحات تحكم أساسية للمؤثرين والتجار
- ✅ صفحة نسيت كلمة المرور
- ✅ تصميم احترافي وجذاب
- ✅ مكونات واجهة قابلة لإعادة الاستخدام
- ✅ ربط جميع الأزرار والروابط بالصفحات المناسبة

### الميزات المخططة (المرحلة التالية)
- 🔄 قاعدة البيانات والـ Backend
- 🔄 نظام المصادقة الكامل
- 🔄 لوحات التحكم للمؤثرين والتجار
- 🔄 نظام الدفع (Apple Pay, Google Pay)
- 🔄 نظام الرسائل والإشعارات
- 🔄 تقارير الأداء والإحصائيات
- 🔄 نظام التقييم والمراجعات

## التشغيل المحلي

1. **تثبيت التبعيات**:
```bash
npm install
```

2. **تشغيل الخادم المحلي**:
```bash
npm run dev
```

3. **فتح المتصفح**:
افتح [http://localhost:3000](http://localhost:3000) لعرض المشروع.

## البناء للإنتاج

```bash
npm run build
npm start
```

## المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع جديد (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push للفرع (`git push origin feature/amazing-feature`)
5. فتح Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## التواصل

- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966 11 123 4567
- **العنوان**: الرياض، المملكة العربية السعودية

---

**ملاحظة**: هذا المشروع في مرحلة التطوير. الميزات المتقدمة مثل قاعدة البيانات ونظام الدفع ستتم إضافتها في المراحل القادمة.
