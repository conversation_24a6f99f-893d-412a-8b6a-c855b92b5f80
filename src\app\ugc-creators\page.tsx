'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import {
  Search,
  Filter,
  Star,
  Clock,
  DollarSign,
  Video,
  Camera,
  Edit3,
  Play,
  Heart,
  Eye,
  MessageCircle,
  Shield,
  CheckCircle,
  Zap,
  Award,
  TrendingUp
} from 'lucide-react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import MobileNavigation from '@/components/layout/MobileNavigation'
import FloatingActionButton from '@/components/ui/FloatingActionButton'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'

const UGCCreatorsPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedFilters, setSelectedFilters] = useState({
    contentType: '',
    priceRange: '',
    duration: '',
    rating: '',
    deliveryTime: '',
    category: ''
  })

  // Mock data for UGC creators
  const ugcCreators = [
    {
      id: 1,
      name: 'سارة المبدعة',
      avatar: '👩‍🎨',
      rating: 4.9,
      reviewCount: 127,
      completedProjects: 89,
      specialties: ['تيك توك', 'انستقرام', 'تصوير منتج'],
      categories: ['جمال', 'موضة'],
      priceRange: '200-800',
      deliveryTime: '24-48 ساعة',
      languages: ['العربية', 'الإنجليزية'],
      portfolioCount: 15,
      isVerified: true,
      isOnline: true,
      experience: '3+ سنوات',
      equipment: 'معدات احترافية',
      description: 'مبدعة محتوى متخصصة في فيديوهات الجمال والموضة مع خبرة 3 سنوات',
      recentWork: [
        { type: 'video', thumbnail: '🎬', title: 'مراجعة منتج جمال', views: '12K' },
        { type: 'video', thumbnail: '📱', title: 'ستوري انستقرام', views: '8K' },
        { type: 'video', thumbnail: '🎵', title: 'فيديو تيك توك', views: '25K' }
      ]
    },
    {
      id: 2,
      name: 'أحمد المصور',
      avatar: '👨‍💻',
      rating: 4.8,
      reviewCount: 95,
      completedProjects: 67,
      specialties: ['تصوير طعام', 'مونتاج', 'تصميم'],
      categories: ['مطاعم', 'أكل'],
      priceRange: '150-600',
      deliveryTime: '1-3 أيام',
      languages: ['العربية'],
      portfolioCount: 22,
      isVerified: true,
      isOnline: false,
      experience: '2+ سنوات',
      equipment: 'كاميرا DSLR',
      description: 'مصور ومونتير متخصص في المحتوى الغذائي والمطاعم',
      recentWork: [
        { type: 'video', thumbnail: '🍕', title: 'فيديو مطعم', views: '15K' },
        { type: 'video', thumbnail: '🍔', title: 'تصوير برجر', views: '9K' },
        { type: 'video', thumbnail: '☕', title: 'قهوة مختصة', views: '11K' }
      ]
    },
    {
      id: 3,
      name: 'نورا الفنانة',
      avatar: '🎨',
      rating: 5.0,
      reviewCount: 203,
      completedProjects: 156,
      specialties: ['تصميم براند', 'جرافيك', 'موشن'],
      categories: ['تقنية', 'براند'],
      priceRange: '300-1200',
      deliveryTime: '2-5 أيام',
      languages: ['العربية', 'الإنجليزية', 'الفرنسية'],
      portfolioCount: 31,
      isVerified: true,
      isOnline: true,
      experience: '5+ سنوات',
      equipment: 'استوديو مجهز',
      description: 'مصممة جرافيك ومبدعة محتوى بصري مع خبرة واسعة في البراندنج',
      recentWork: [
        { type: 'design', thumbnail: '🎨', title: 'تصميم لوجو', views: '5K' },
        { type: 'video', thumbnail: '🎬', title: 'موشن جرافيك', views: '18K' },
        { type: 'design', thumbnail: '📱', title: 'تصميم تطبيق', views: '7K' }
      ]
    },
    {
      id: 4,
      name: 'خالد التقني',
      avatar: '👨‍💼',
      rating: 4.7,
      reviewCount: 78,
      completedProjects: 45,
      specialties: ['مراجعات تقنية', 'يوتيوب', 'تعليمي'],
      categories: ['تقنية', 'تعليم'],
      priceRange: '400-1000',
      deliveryTime: '3-7 أيام',
      languages: ['العربية', 'الإنجليزية'],
      portfolioCount: 18,
      isVerified: true,
      isOnline: true,
      experience: '4+ سنوات',
      equipment: 'معدات احترافية',
      description: 'خبير تقني ومراجع منتجات مع قناة يوتيوب ناجحة',
      recentWork: [
        { type: 'video', thumbnail: '📱', title: 'مراجعة جوال', views: '45K' },
        { type: 'video', thumbnail: '💻', title: 'مراجعة لابتوب', views: '32K' },
        { type: 'video', thumbnail: '🎧', title: 'مراجعة سماعات', views: '28K' }
      ]
    },
    {
      id: 5,
      name: 'ريم المسافرة',
      avatar: '✈️',
      rating: 4.9,
      reviewCount: 134,
      completedProjects: 92,
      specialties: ['سفر', 'لايف ستايل', 'ريلز'],
      categories: ['سفر', 'لايف ستايل'],
      priceRange: '250-700',
      deliveryTime: '1-4 أيام',
      languages: ['العربية', 'الإنجليزية'],
      portfolioCount: 26,
      isVerified: true,
      isOnline: false,
      experience: '3+ سنوات',
      equipment: 'جوال احترافي',
      description: 'مبدعة محتوى سفر ولايف ستايل مع متابعين مخلصين',
      recentWork: [
        { type: 'video', thumbnail: '🏖️', title: 'رحلة شاطئية', views: '22K' },
        { type: 'video', thumbnail: '🏔️', title: 'مغامرة جبلية', views: '19K' },
        { type: 'video', thumbnail: '🏨', title: 'مراجعة فندق', views: '16K' }
      ]
    },
    {
      id: 6,
      name: 'عبدالله الرياضي',
      avatar: '💪',
      rating: 4.8,
      reviewCount: 89,
      completedProjects: 56,
      specialties: ['رياضة', 'فتنس', 'تحفيزي'],
      categories: ['رياضة', 'صحة'],
      priceRange: '200-600',
      deliveryTime: '24-72 ساعة',
      languages: ['العربية'],
      portfolioCount: 20,
      isVerified: true,
      isOnline: true,
      experience: '2+ سنوات',
      equipment: 'كاميرا رياضية',
      description: 'مدرب رياضي ومبدع محتوى تحفيزي للياقة البدنية',
      recentWork: [
        { type: 'video', thumbnail: '🏋️', title: 'تمارين منزلية', views: '35K' },
        { type: 'video', thumbnail: '🥗', title: 'وجبة صحية', views: '12K' },
        { type: 'video', thumbnail: '🏃', title: 'تحدي جري', views: '28K' }
      ]
    }
  ]

  const contentTypeFilters = [
    { id: 'all', label: 'جميع الأنواع', icon: '🎬' },
    { id: 'tiktok', label: 'تيك توك', icon: '🎵' },
    { id: 'instagram', label: 'انستقرام', icon: '📱' },
    { id: 'youtube', label: 'يوتيوب', icon: '📺' },
    { id: 'photography', label: 'تصوير', icon: '📸' },
    { id: 'design', label: 'تصميم', icon: '🎨' },
    { id: 'editing', label: 'مونتاج', icon: '✂️' }
  ]

  const priceRanges = [
    { id: 'all', label: 'جميع الأسعار' },
    { id: '0-200', label: 'أقل من 200 ريال' },
    { id: '200-500', label: '200-500 ريال' },
    { id: '500-1000', label: '500-1000 ريال' },
    { id: '1000+', label: 'أكثر من 1000 ريال' }
  ]

  const deliveryTimeFilters = [
    { id: 'all', label: 'جميع الأوقات' },
    { id: '24h', label: '24 ساعة' },
    { id: '48h', label: '48 ساعة' },
    { id: '3d', label: '3 أيام' },
    { id: '7d', label: 'أسبوع' }
  ]

  const categoryFilters = [
    { id: 'all', label: 'جميع الفئات' },
    { id: 'beauty', label: 'جمال' },
    { id: 'food', label: 'طعام' },
    { id: 'tech', label: 'تقنية' },
    { id: 'fashion', label: 'موضة' },
    { id: 'travel', label: 'سفر' },
    { id: 'fitness', label: 'رياضة' }
  ]

  const filteredCreators = ugcCreators.filter(creator => {
    const matchesSearch = creator.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         creator.specialties.some(s => s.toLowerCase().includes(searchTerm.toLowerCase()))

    // Add more filter logic here
    return matchesSearch
  })

  return (
    <div className="min-h-screen bg-slate-900">
      <Header />

      {/* Hero Section */}
      <section className="py-16 bg-gradient-to-r from-purple-600 via-pink-600 to-cyan-600 text-white sparkle-container">
        <div className="max-w-7xl mx-auto mobile-container text-center">
          <div className="text-6xl mb-6">🎬</div>
          <h1 className="heading-mobile text-white mb-4">
            مبدعو المحتوى UGC
          </h1>
          <p className="subheading-mobile text-gray-200 max-w-2xl mx-auto mb-8">
            اكتشف أفضل المبدعين السعوديين المتخصصين في إنتاج محتوى إعلاني عالي الجودة 🇸🇦
          </p>

          {/* Trust Badges */}
          <div className="flex flex-wrap items-center justify-center gap-4 mb-8">
            <div className="saudi-trust-badge">
              <Shield className="w-4 h-4 ml-2" />
              🛡️ مبدعون معتمدون وموثوقون
            </div>
            <div className="saudi-trust-badge">
              <Star className="w-4 h-4 ml-2" />
              ⭐ جودة مضمونة 100%
            </div>
            <div className="saudi-trust-badge">
              <CheckCircle className="w-4 h-4 ml-2" />
              ✅ تسليم في الوقت المحدد
            </div>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-2xl mx-auto">
            <div className="card-mobile text-center">
              <div className="text-3xl mb-2">👥</div>
              <div className="text-2xl font-bold text-white">500+</div>
              <div className="text-gray-300 text-sm">مبدع محتوى</div>
            </div>
            <div className="card-mobile text-center">
              <div className="text-3xl mb-2">🎯</div>
              <div className="text-2xl font-bold text-white">2000+</div>
              <div className="text-gray-300 text-sm">مشروع مكتمل</div>
            </div>
            <div className="card-mobile text-center">
              <div className="text-3xl mb-2">⭐</div>
              <div className="text-2xl font-bold text-white">4.8</div>
              <div className="text-gray-300 text-sm">متوسط التقييم</div>
            </div>
          </div>
        </div>
      </section>

      <div className="py-12">
        <div className="max-w-7xl mx-auto mobile-container">
          {/* Search and Filters */}
          <div className="mb-8">
            <Card className="card-mobile">
              <div className="flex flex-col lg:flex-row gap-4">
                {/* Search */}
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                    <input
                      type="text"
                      placeholder="ابحث عن مبدع أو نوع محتوى..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="input-mobile pr-12"
                    />
                  </div>
                </div>

                {/* Filter Button */}
                <Button variant="secondary" className="btn-secondary-mobile">
                  <Filter className="h-5 w-5 ml-2" />
                  فلترة متقدمة
                </Button>
              </div>

              {/* Quick Filters */}
              <div className="mt-6">
                <h3 className="text-white font-semibold mb-3">نوع المحتوى:</h3>
                <div className="flex flex-wrap gap-2">
                  {contentTypeFilters.map((filter) => (
                    <button
                      key={filter.id}
                      onClick={() => setSelectedFilters(prev => ({ ...prev, contentType: filter.id }))}
                      className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                        selectedFilters.contentType === filter.id
                          ? 'bg-purple-500 text-white'
                          : 'bg-white/10 text-gray-300 hover:bg-white/20'
                      }`}
                    >
                      {filter.icon} {filter.label}
                    </button>
                  ))}
                </div>
              </div>
            </Card>
          </div>

          {/* Results Count */}
          <div className="mb-6">
            <p className="text-gray-300">
              تم العثور على <span className="text-white font-semibold">{filteredCreators.length}</span> مبدع محتوى
            </p>
          </div>

          {/* Creators Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredCreators.map((creator, index) => (
              <Card key={creator.id} hover className="card-mobile slide-up" style={{ animationDelay: `${index * 0.1}s` }}>
                {/* Creator Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-2xl mr-3">
                      {creator.avatar}
                    </div>
                    <div>
                      <div className="flex items-center">
                        <h3 className="text-white font-bold">{creator.name}</h3>
                        {creator.isVerified && (
                          <CheckCircle className="h-4 w-4 text-cyan-400 mr-2" />
                        )}
                      </div>
                      <div className="flex items-center text-sm text-gray-400">
                        <Star className="h-4 w-4 text-yellow-400 fill-current ml-1" />
                        <span>{creator.rating}</span>
                        <span className="mr-2">({creator.reviewCount} تقييم)</span>
                      </div>
                    </div>
                  </div>
                  <div className={`w-3 h-3 rounded-full ${creator.isOnline ? 'bg-green-400' : 'bg-gray-400'}`}></div>
                </div>

                {/* Description */}
                <p className="text-gray-300 text-sm mb-4 line-clamp-2">
                  {creator.description}
                </p>

                {/* Specialties */}
                <div className="mb-4">
                  <div className="flex flex-wrap gap-1">
                    {creator.specialties.slice(0, 3).map((specialty, i) => (
                      <span key={i} className="px-2 py-1 bg-purple-500/20 text-purple-300 text-xs rounded-full">
                        {specialty}
                      </span>
                    ))}
                    {creator.specialties.length > 3 && (
                      <span className="px-2 py-1 bg-gray-500/20 text-gray-400 text-xs rounded-full">
                        +{creator.specialties.length - 3}
                      </span>
                    )}
                  </div>
                </div>

                {/* Stats */}
                <div className="grid grid-cols-3 gap-4 mb-4 text-center">
                  <div>
                    <div className="text-white font-semibold text-sm">{creator.completedProjects}</div>
                    <div className="text-gray-400 text-xs">مشروع</div>
                  </div>
                  <div>
                    <div className="text-white font-semibold text-sm">{creator.portfolioCount}</div>
                    <div className="text-gray-400 text-xs">عمل</div>
                  </div>
                  <div>
                    <div className="text-white font-semibold text-sm">{creator.deliveryTime}</div>
                    <div className="text-gray-400 text-xs">تسليم</div>
                  </div>
                </div>

                {/* Recent Work Preview */}
                <div className="mb-4">
                  <h4 className="text-white text-sm font-semibold mb-2">أعمال حديثة:</h4>
                  <div className="grid grid-cols-3 gap-2">
                    {creator.recentWork.map((work, i) => (
                      <div key={i} className="relative bg-white/10 rounded-lg p-2 text-center hover:bg-white/20 transition-colors cursor-pointer">
                        <div className="text-lg mb-1">{work.thumbnail}</div>
                        <div className="text-xs text-gray-300 truncate">{work.title}</div>
                        <div className="text-xs text-gray-400">{work.views}</div>
                        <div className="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
                          <Play className="h-4 w-4 text-white" />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Price and Action */}
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-white font-bold">من {creator.priceRange.split('-')[0]} ريال</div>
                    <div className="text-gray-400 text-xs">حسب نوع المحتوى</div>
                  </div>
                  <div className="flex gap-2">
                    <Link href={`/ugc-creators/${creator.id}`}>
                      <Button size="sm" variant="secondary" className="btn-secondary-mobile">
                        <Eye className="h-4 w-4 ml-1" />
                        عرض
                      </Button>
                    </Link>
                    <Link href={`/ugc-creators/${creator.id}/order`}>
                      <Button size="sm" className="btn-primary-mobile">
                        <MessageCircle className="h-4 w-4 ml-1" />
                        اطلب خدمة
                      </Button>
                    </Link>
                  </div>
                </div>
              </Card>
            ))}
          </div>

          {/* Load More */}
          <div className="text-center mt-12">
            <Button className="btn-primary-mobile">
              عرض المزيد من المبدعين
              <TrendingUp className="h-5 w-5 mr-2" />
            </Button>
          </div>

          {/* CTA Section */}
          <Card className="card-mobile mt-16 text-center gradient-bg sparkle-container">
            <div className="text-6xl mb-4">🚀</div>
            <h2 className="text-2xl font-bold text-white mb-4">
              هل أنت مبدع محتوى؟
            </h2>
            <p className="text-gray-200 mb-6 max-w-2xl mx-auto">
              انضم إلى منصتنا واحصل على فرص عمل حصرية مع أفضل البراندات السعودية
            </p>
            <Link href="/register/ugc-creator">
              <Button className="btn-primary-mobile sparkle-container">
                🎬 انضم كمبدع UGC
                <Zap className="h-5 w-5 mr-2" />
              </Button>
            </Link>
          </Card>
        </div>
      </div>

      <Footer />
      <MobileNavigation />
      <FloatingActionButton variant="merchant" />
    </div>
  )
}

export default UGCCreatorsPage
