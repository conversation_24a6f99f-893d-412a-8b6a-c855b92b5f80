'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Building,
  Globe,
  FileText,
  ArrowRight,
  CheckCircle,
  Store
} from 'lucide-react'

const MerchantRegisterPage: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(1)
  const [formData, setFormData] = useState({
    // Personal Info
    fullName: '',
    email: '',
    phone: '',
    
    // Business Info
    businessName: '',
    businessType: '',
    city: '',
    address: '',
    website: '',
    description: '',
    
    // Additional Info
    targetAudience: '',
    budget: '',
    goals: ''
  })

  const steps = [
    { number: 1, title: 'المعلومات الشخصية', description: 'بياناتك الأساسية' },
    { number: 2, title: 'معلومات العمل', description: 'تفاصيل نشاطك التجاري' },
    { number: 3, title: 'أهدافك التسويقية', description: 'ما تريد تحقيقه' }
  ]

  const businessTypes = [
    'متجر إلكتروني',
    'متجر تقليدي',
    'مطعم أو مقهى',
    'صالون تجميل',
    'مركز طبي',
    'شركة خدمات',
    'شركة تقنية',
    'وكالة سفر',
    'مركز تدريب',
    'شركة عقارات',
    'متجر ملابس',
    'أخرى'
  ]

  const cities = [
    'الرياض',
    'جدة',
    'مكة المكرمة',
    'المدينة المنورة',
    'الدمام',
    'الخبر',
    'الطائف',
    'بريدة',
    'تبوك',
    'خميس مشيط',
    'حائل',
    'الجبيل',
    'الأحساء',
    'نجران',
    'ينبع',
    'أبها'
  ]

  const budgetRanges = [
    'أقل من 1,000 ريال شهرياً',
    '1,000 - 5,000 ريال شهرياً',
    '5,000 - 10,000 ريال شهرياً',
    '10,000 - 25,000 ريال شهرياً',
    '25,000 - 50,000 ريال شهرياً',
    'أكثر من 50,000 ريال شهرياً'
  ]

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleNext = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleSubmit = () => {
    // Handle form submission
    console.log('Form submitted:', formData)
    alert('تم إرسال طلب التسجيل بنجاح! سيتم مراجعته والرد عليك قريباً.')
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">المعلومات الشخصية</h2>
            
            <Input
              label="الاسم الكامل"
              placeholder="أدخل اسمك الكامل"
              value={formData.fullName}
              onChange={(e) => handleInputChange('fullName', e.target.value)}
              leftIcon={<User className="h-5 w-5" />}
              required
            />
            
            <Input
              label="البريد الإلكتروني"
              type="email"
              placeholder="<EMAIL>"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              leftIcon={<Mail className="h-5 w-5" />}
              required
            />
            
            <Input
              label="رقم الجوال"
              placeholder="05xxxxxxxx"
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              leftIcon={<Phone className="h-5 w-5" />}
              required
            />
          </div>
        )

      case 2:
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">معلومات النشاط التجاري</h2>
            
            <Input
              label="اسم النشاط التجاري"
              placeholder="اسم متجرك أو شركتك"
              value={formData.businessName}
              onChange={(e) => handleInputChange('businessName', e.target.value)}
              leftIcon={<Building className="h-5 w-5" />}
              required
            />
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">نوع النشاط التجاري</label>
              <select
                value={formData.businessType}
                onChange={(e) => handleInputChange('businessType', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              >
                <option value="">اختر نوع النشاط</option>
                {businessTypes.map((type) => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">المدينة</label>
              <select
                value={formData.city}
                onChange={(e) => handleInputChange('city', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              >
                <option value="">اختر المدينة</option>
                {cities.map((city) => (
                  <option key={city} value={city}>{city}</option>
                ))}
              </select>
            </div>
            
            <Input
              label="العنوان (اختياري)"
              placeholder="عنوان المتجر أو المكتب"
              value={formData.address}
              onChange={(e) => handleInputChange('address', e.target.value)}
              leftIcon={<MapPin className="h-5 w-5" />}
            />
            
            <Input
              label="الموقع الإلكتروني (اختياري)"
              placeholder="https://example.com"
              value={formData.website}
              onChange={(e) => handleInputChange('website', e.target.value)}
              leftIcon={<Globe className="h-5 w-5" />}
            />
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">وصف النشاط التجاري</label>
              <textarea
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="اكتب وصفاً مختصراً عن نشاطك التجاري والمنتجات أو الخدمات التي تقدمها..."
                rows={4}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              />
            </div>
          </div>
        )

      case 3:
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">أهدافك التسويقية</h2>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">الجمهور المستهدف</label>
              <textarea
                value={formData.targetAudience}
                onChange={(e) => handleInputChange('targetAudience', e.target.value)}
                placeholder="صف جمهورك المستهدف (العمر، الجنس، الاهتمامات، إلخ)..."
                rows={3}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">الميزانية الشهرية المتوقعة</label>
              <select
                value={formData.budget}
                onChange={(e) => handleInputChange('budget', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              >
                <option value="">اختر الميزانية</option>
                {budgetRanges.map((range) => (
                  <option key={range} value={range}>{range}</option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">أهدافك من التسويق بالمؤثرين</label>
              <textarea
                value={formData.goals}
                onChange={(e) => handleInputChange('goals', e.target.value)}
                placeholder="ما الذي تريد تحقيقه؟ (زيادة المبيعات، الوعي بالعلامة التجارية، إلخ)..."
                rows={4}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              />
            </div>
            
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h3 className="font-semibold text-green-900 mb-2">مميزات حسابك التجاري:</h3>
              <ul className="text-green-800 text-sm space-y-1">
                <li>• وصول لآلاف المؤثرين المعتمدين</li>
                <li>• نظام ضمان الأموال</li>
                <li>• تقارير مفصلة عن أداء الحملات</li>
                <li>• دعم فني متخصص 24/7</li>
                <li>• أدوات تحليل متقدمة</li>
              </ul>
            </div>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-cyan-50">
      <Header />
      
      <div className="py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-12">
            <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <Store className="h-10 w-10 text-white" />
            </div>
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              انضم كتاجر
            </h1>
            <p className="text-xl text-gray-600">
              ابدأ حملاتك الإعلانية مع أفضل المؤثرين في السعودية
            </p>
          </div>

          {/* Progress Steps */}
          <div className="mb-12">
            <div className="flex items-center justify-between">
              {steps.map((step, index) => (
                <div key={step.number} className="flex items-center">
                  <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                    currentStep >= step.number 
                      ? 'bg-primary-600 border-primary-600 text-white' 
                      : 'border-gray-300 text-gray-500'
                  }`}>
                    {currentStep > step.number ? (
                      <CheckCircle className="h-6 w-6" />
                    ) : (
                      <span className="text-sm font-medium">{step.number}</span>
                    )}
                  </div>
                  <div className="mr-4 hidden sm:block">
                    <div className={`text-sm font-medium ${
                      currentStep >= step.number ? 'text-primary-600' : 'text-gray-500'
                    }`}>
                      {step.title}
                    </div>
                    <div className="text-xs text-gray-500">{step.description}</div>
                  </div>
                  {index < steps.length - 1 && (
                    <div className={`w-12 h-0.5 mx-4 ${
                      currentStep > step.number ? 'bg-primary-600' : 'bg-gray-300'
                    }`}></div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Form Content */}
          <Card className="mb-8">
            {renderStepContent()}
          </Card>

          {/* Navigation Buttons */}
          <div className="flex justify-between">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentStep === 1}
              className="flex items-center"
            >
              <ArrowRight className="h-4 w-4 ml-2" />
              السابق
            </Button>

            {currentStep < 3 ? (
              <Button onClick={handleNext} className="flex items-center">
                التالي
                <ArrowRight className="h-4 w-4 mr-2 rotate-180" />
              </Button>
            ) : (
              <Button onClick={handleSubmit} variant="primary" className="flex items-center">
                إرسال طلب التسجيل
                <CheckCircle className="h-4 w-4 mr-2" />
              </Button>
            )}
          </div>

          {/* Login Link */}
          <div className="text-center mt-8">
            <p className="text-gray-600">
              لديك حساب بالفعل؟{' '}
              <Link href="/login" className="text-primary-600 hover:text-primary-700 font-medium">
                سجل دخولك هنا
              </Link>
            </p>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  )
}

export default MerchantRegisterPage
