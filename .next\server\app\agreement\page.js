(()=>{var e={};e.id=531,e.ids=[531],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},1877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},5319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},5666:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>o,pages:()=>x,routeModule:()=>h,tree:()=>d});var r=s(482),a=s(9108),i=s(2563),l=s.n(i),n=s(8300),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);s.d(t,c);let d=["",{children:["agreement",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,2303)),"C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\agreement\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,1342)),"C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,9361,23)),"next/dist/client/components/not-found-error"]}],x=["C:\\Users\\<USER>\\Desktop\\amshor2\\src\\app\\agreement\\page.tsx"],o="/agreement/page",m={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/agreement/page",pathname:"/agreement",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},4057:(e,t,s)=>{Promise.resolve().then(s.bind(s,6728))},5961:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(9224).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},5794:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(9224).Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},7060:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(9224).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},8411:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(9224).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},7121:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(9224).Z)("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]])},5851:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(9224).Z)("MessageCircle",[["path",{d:"m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z",key:"v2veuj"}]])},8765:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(9224).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},8822:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(9224).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},9895:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(9224).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},6728:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>N});var r=s(2295);s(3729);var a=s(783),i=s.n(a),l=s(7121),n=s(9895),c=s(3485),d=s(8411);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let x=(0,s(9224).Z)("Scale",[["path",{d:"m16 16 3-8 3 8c-.87.65-1.92 1-3 1s-2.13-.35-3-1Z",key:"7g6ntu"}],["path",{d:"m2 16 3-8 3 8c-.87.65-1.92 1-3 1s-2.13-.35-3-1Z",key:"ijws7r"}],["path",{d:"M7 21h10",key:"1b0cd5"}],["path",{d:"M12 3v18",key:"108xh3"}],["path",{d:"M3 7h2c2 0 5-1 7-2 2 1 5 2 7 2h2",key:"3gwbw2"}]]);var o=s(5961),m=s(7060),h=s(1206),p=s(626),u=s(508),g=s(5794),y=s(1859),j=s(7514),v=s(3507),f=s(9956),b=s(793),Z=s(5356);let N=()=>{let e=[{id:"definitions",title:"التعريفات والمصطلحات",icon:l.Z,content:["المنصة: تشير إلى موقع وتطبيق منصة المؤثرين السعودية","المستخدم: أي شخص يستخدم المنصة سواء كان مؤثراً أو تاجراً أو مبدع محتوى","المؤثر: الشخص الذي يقدم خدمات التسويق والترويج عبر وسائل التواصل الاجتماعي","التاجر: الشخص أو الشركة التي تطلب خدمات التسويق والترويج","مبدع UGC: الشخص المتخصص في إنتاج المحتوى المولد من المستخدمين","الخدمة: أي خدمة تسويقية أو إعلانية تقدم عبر المنصة"]},{id:"user-obligations",title:"التزامات المستخدم",icon:n.Z,content:["تقديم معلومات صحيحة ودقيقة عند التسجيل","الحفاظ على سرية بيانات الحساب وكلمة المرور","عدم استخدام المنصة لأغراض غير قانونية أو ضارة","احترام حقوق الملكية الفكرية للآخرين","الالتزام بالقوانين واللوائح المعمول بها في المملكة العربية السعودية","عدم نشر محتوى مسيء أو غير لائق أو مخالف للآداب العامة"]},{id:"platform-obligations",title:"التزامات المنصة",icon:c.Z,content:["توفير خدمة آمنة وموثوقة للمستخدمين","حماية البيانات الشخصية وفقاً لسياسة الخصوصية","توفير نظام دفع آمن ومضمون","تقديم الدعم الفني والمساعدة عند الحاجة","ضمان جودة الخدمات المقدمة عبر المنصة","التحديث المستمر للمنصة وتحسين الخدمات"]},{id:"payment-terms",title:"شروط الدفع والمالية",icon:d.Z,content:["جميع المدفوعات تتم بالريال السعودي","يتم الاحتفاظ بالأموال في نظام ضمان آمن حتى اكتمال الخدمة","يحق للمنصة خصم عمولة من كل معاملة مالية","يتم تحويل الأموال للمؤثر خلال 24-48 ساعة من موافقة التاجر","في حالة النزاع، تحتفظ المنصة بالحق في تجميد الأموال حتى حل النزاع","جميع الرسوم والضرائب المطبقة واضحة ومعلنة مسبقاً"]},{id:"content-rights",title:"حقوق المحتوى والملكية الفكرية",icon:x,content:["يحتفظ المؤثر بحقوق المحتوى الذي ينتجه","يحق للتاجر استخدام المحتوى المتفق عليه وفقاً للشروط المحددة","لا يحق للمنصة استخدام المحتوى دون إذن صريح من المنتج","يجب احترام حقوق الطبع والنشر والعلامات التجارية","في حالة انتهاك حقوق الملكية الفكرية، يتحمل المنتهك المسؤولية كاملة","تحتفظ المنصة بالحق في إزالة أي محتوى ينتهك حقوق الآخرين"]},{id:"dispute-resolution",title:"حل النزاعات",icon:o.Z,content:["في حالة النزاع، يجب التواصل أولاً مع فريق الدعم الفني","تقدم المنصة خدمة وساطة مجانية لحل النزاعات","يحق لأي طرف طلب التحكيم في حالة فشل الوساطة","تطبق القوانين السعودية في حل جميع النزاعات","المحاكم السعودية هي المختصة بالنظر في أي نزاع قانوني","يتحمل الطرف المخطئ تكاليف التحكيم والرسوم القانونية"]}],t=[{icon:m.Z,title:"ضمان الجودة",description:"نضمن جودة الخدمات المقدمة عبر المنصة"},{icon:c.Z,title:"الأمان والحماية",description:"نوفر أعلى مستويات الأمان لحماية بياناتك"},{icon:d.Z,title:"الدفع الآمن",description:"نظام دفع آمن مع ضمان استرداد الأموال"},{icon:n.Z,title:"الدعم المستمر",description:"فريق دعم متاح 24/7 لمساعدتك"}],s=[{icon:h.Z,label:"البريد الإلكتروني",value:"<EMAIL>"},{icon:p.Z,label:"الهاتف",value:"+966 11 123 4567"},{icon:u.Z,label:"العنوان",value:"الرياض، المملكة العربية السعودية"}];return(0,r.jsxs)("div",{className:"min-h-screen bg-slate-900",children:[r.jsx(y.Z,{}),r.jsx("div",{className:"py-16",children:(0,r.jsxs)(Z.Z,{size:"lg",children:[(0,r.jsxs)("div",{className:"text-center mb-12",children:[r.jsx("h1",{className:"text-4xl md:text-5xl font-bold text-white mb-6",children:"اتفاقية المستخدم"}),r.jsx("p",{className:"text-xl text-slate-300 max-w-3xl mx-auto mb-6",children:"تحدد هذه الاتفاقية الشروط والأحكام التي تحكم استخدامك لمنصة المؤثرين السعودية"}),(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2 text-slate-400",children:[r.jsx(g.Z,{className:"h-5 w-5"}),(0,r.jsxs)("span",{children:["آخر تحديث: ","15 يناير 2024"]})]})]}),r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12",children:t.map((e,t)=>r.jsx(f.Z,{children:(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("div",{className:"w-12 h-12 bg-indigo-600 rounded-full flex items-center justify-center mx-auto mb-4",children:r.jsx(e.icon,{className:"h-6 w-6 text-white"})}),r.jsx("h3",{className:"text-lg font-bold text-white mb-2",children:e.title}),r.jsx("p",{className:"text-slate-300 text-sm",children:e.description})]})},t))}),r.jsx("div",{className:"space-y-8 mb-12",children:e.map((e,t)=>(0,r.jsxs)(f.Z,{children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-6",children:[r.jsx("div",{className:"w-10 h-10 bg-indigo-600 rounded-lg flex items-center justify-center",children:r.jsx(e.icon,{className:"h-5 w-5 text-white"})}),r.jsx("h2",{className:"text-xl font-bold text-white",children:e.title}),r.jsx(b.Z,{variant:"primary",size:"sm",children:t+1})]}),r.jsx("div",{className:"space-y-3",children:e.content.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[r.jsx("div",{className:"w-2 h-2 bg-indigo-400 rounded-full mt-2 flex-shrink-0"}),r.jsx("p",{className:"text-slate-300 leading-relaxed",children:e})]},t))})]},e.id))}),r.jsx(f.Z,{className:"bg-amber-500/10 border-amber-500/20 mb-12",children:(0,r.jsxs)("div",{className:"flex items-start gap-4",children:[r.jsx("div",{className:"w-8 h-8 bg-amber-500 rounded-full flex items-center justify-center flex-shrink-0",children:r.jsx(o.Z,{className:"h-5 w-5 text-white"})}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-lg font-bold text-amber-400 mb-2",children:"تنبيه مهم"}),r.jsx("p",{className:"text-slate-300 leading-relaxed",children:"باستخدامك لهذه المنصة، فإنك توافق على جميع الشروط والأحكام المذكورة في هذه الاتفاقية. إذا كنت لا توافق على أي من هذه الشروط، يرجى عدم استخدام المنصة. نحتفظ بالحق في تعديل هذه الاتفاقية في أي وقت، وسيتم إشعارك بأي تغييرات مهمة."})]})]})}),(0,r.jsxs)(f.Z,{children:[r.jsx("h2",{className:"text-xl font-bold text-white mb-6",children:"للاستفسارات القانونية"}),r.jsx("p",{className:"text-slate-300 mb-6",children:"إذا كان لديك أي استفسارات حول هذه الاتفاقية أو تحتاج إلى توضيحات إضافية، يمكنك التواصل معنا عبر القنوات التالية:"}),r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:s.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center gap-3 p-4 bg-slate-800 rounded-lg",children:[r.jsx("div",{className:"w-10 h-10 bg-indigo-600 rounded-full flex items-center justify-center",children:r.jsx(e.icon,{className:"h-5 w-5 text-white"})}),(0,r.jsxs)("div",{children:[r.jsx("div",{className:"text-slate-400 text-sm",children:e.label}),r.jsx("div",{className:"text-white font-medium",children:e.value})]})]},t))})]}),(0,r.jsxs)("div",{className:"mt-12 text-center",children:[r.jsx("h3",{className:"text-lg font-bold text-white mb-6",children:"وثائق ذات صلة"}),(0,r.jsxs)("div",{className:"flex flex-wrap justify-center gap-4",children:[r.jsx(i(),{href:"/terms",children:r.jsx(b.Z,{variant:"secondary",className:"cursor-pointer hover:bg-slate-600",children:"شروط الاستخدام"})}),r.jsx(i(),{href:"/privacy",children:r.jsx(b.Z,{variant:"secondary",className:"cursor-pointer hover:bg-slate-600",children:"سياسة الخصوصية"})}),r.jsx(i(),{href:"/refund",children:r.jsx(b.Z,{variant:"secondary",className:"cursor-pointer hover:bg-slate-600",children:"سياسة الاسترداد"})}),r.jsx(i(),{href:"/help",children:r.jsx(b.Z,{variant:"secondary",className:"cursor-pointer hover:bg-slate-600",children:"مركز المساعدة"})})]})]})]})}),r.jsx(j.Z,{}),r.jsx(v.Z,{})]})}},3507:(e,t,s)=>{"use strict";s.d(t,{Z:()=>m});var r=s(2295);s(3729);var a=s(783),i=s.n(a),l=s(2254);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,s(9224).Z)("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]]);var c=s(9895),d=s(8765),x=s(5851),o=s(8822);let m=()=>{let e=(0,l.usePathname)(),t=[{name:"الرئيسية",href:"/",icon:n,color:"text-green-600"},{name:"المؤثرين",href:"/influencers",icon:c.Z,color:"text-blue-600"},{name:"البحث",href:"/search",icon:d.Z,color:"text-purple-600"},{name:"الرسائل",href:"/messages",icon:x.Z,color:"text-orange-600"},{name:"حسابي",href:"/profile",icon:o.Z,color:"text-gray-600"}],s=t=>"/"===t?"/"===e:e.startsWith(t);return(0,r.jsxs)(r.Fragment,{children:[r.jsx("div",{className:"h-20 md:hidden"}),r.jsx("nav",{className:"md:hidden fixed bottom-0 left-0 right-0 bg-slate-900/95 backdrop-blur-md border-t border-slate-700/50 z-50",children:r.jsx("div",{className:"flex items-center justify-around py-2",children:t.map(e=>{let t=e.icon,a=s(e.href);return r.jsx(i(),{href:e.href,className:"flex-1",children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center py-3",children:[r.jsx("div",{className:`p-2 rounded-lg transition-colors ${a?"bg-indigo-500/10":"hover:bg-slate-800"}`,children:r.jsx(t,{className:`h-5 w-5 transition-colors ${a?"text-indigo-400":"text-slate-400"}`})}),r.jsx("span",{className:`text-xs font-medium mt-1 transition-colors ${a?"text-indigo-400":"text-slate-400"}`,children:e.name})]})},e.name)})})})]})}},793:(e,t,s)=>{"use strict";s.d(t,{Z:()=>i});var r=s(2295);s(3729);var a=s(6815);let i=({children:e,variant:t="primary",size:s="md",className:i=""})=>r.jsx("span",{className:(0,a.W)("badge",{primary:"badge-primary",secondary:"bg-slate-500/10 text-slate-400 border-slate-500/20",success:"badge-success",warning:"badge-warning",error:"bg-red-500/10 text-red-400 border-red-500/20"}[t],{sm:"text-xs px-2 py-1",md:"text-sm px-3 py-1",lg:"text-base px-4 py-2"}[s],i),children:e})},5356:(e,t,s)=>{"use strict";s.d(t,{Z:()=>i});var r=s(2295);s(3729);var a=s(6815);let i=({children:e,size:t="lg",className:s=""})=>r.jsx("div",{className:(0,a.W)("mx-auto px-4 sm:px-6 lg:px-8",{sm:"max-w-2xl",md:"max-w-4xl",lg:"max-w-6xl",xl:"max-w-7xl",full:"max-w-full"}[t],s),children:e})},2303:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>a,default:()=>l});let r=(0,s(6843).createProxy)(String.raw`C:\Users\<USER>\Desktop\amshor2\src\app\agreement\page.tsx`),{__esModule:a,$$typeof:i}=r,l=r.default}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[73,801,424],()=>s(5666));module.exports=r})();