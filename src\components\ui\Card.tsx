'use client'

import React from 'react'
import { clsx } from 'clsx'

interface CardProps {
  children: React.ReactNode
  className?: string
  hover?: boolean
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl'
  shadow?: 'none' | 'sm' | 'md' | 'lg' | 'xl'
  variant?: 'default' | 'elevated' | 'outlined' | 'glass'
  background?: 'primary' | 'secondary' | 'surface'
}

const Card: React.FC<CardProps> = ({
  children,
  className,
  hover = false,
  padding = 'md',
  shadow = 'md',
  variant = 'default',
  background = 'primary'
}) => {
  const baseClasses = 'card'

  const paddingClasses = {
    none: 'p-0',
    sm: 'p-3',
    md: 'p-5',
    lg: 'p-6',
    xl: 'p-8'
  }

  const shadowClasses = {
    none: '',
    sm: 'shadow-sm',
    md: 'shadow-md',
    lg: 'shadow-lg',
    xl: 'shadow-xl'
  }

  const variantClasses = {
    default: 'bg-slate-800/95 border border-slate-700/80',
    elevated: 'bg-slate-800/98 border border-slate-600/60 shadow-2xl',
    outlined: 'bg-slate-900/50 border-2 border-slate-600',
    glass: 'bg-slate-800/30 backdrop-blur-xl border border-slate-700/50'
  }

  const backgroundClasses = {
    primary: 'text-slate-100',
    secondary: 'text-slate-200 bg-slate-700/95',
    surface: 'text-slate-50 bg-slate-600/95'
  }

  const hoverClasses = hover ? 'hover:transform hover:-translate-y-1 hover:shadow-xl transition-all duration-200' : ''

  return (
    <div
      className={clsx(
        baseClasses,
        variantClasses[variant],
        backgroundClasses[background],
        paddingClasses[padding],
        shadowClasses[shadow],
        hoverClasses,
        'rounded-xl transition-all duration-200',
        className
      )}
    >
      {children}
    </div>
  )
}

export default Card
