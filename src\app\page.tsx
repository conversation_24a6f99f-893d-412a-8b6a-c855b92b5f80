'use client'

import React from 'react'
import Link from 'next/link'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import MobileNavigation from '@/components/layout/MobileNavigation'
import FloatingActionButton from '@/components/ui/FloatingActionButton'
import Button from '@/components/ui/Button'
import Card from '@/components/ui/Card'
import {
  Shield,
  DollarSign,
  Users,
  TrendingUp,
  Star,
  CheckCircle,
  ArrowLeft,
  Play,
  Instagram,
  Youtube,
  Twitter,
  Video
} from 'lucide-react'

const HomePage: React.FC = () => {
  const features = [
    {
      icon: Shield,
      title: 'ضمان الأموال',
      description: 'نحتفظ بأموالك في نظام ضمان آمن حتى اكتمال الإعلان بنجاح'
    },
    {
      icon: DollarSign,
      title: 'استرداد مضمون',
      description: 'ضمان استرداد الأموال 100% في حالة عدم تنفيذ الإعلان كما هو متفق عليه'
    },
    {
      icon: Users,
      title: 'مؤثرين معتمدين',
      description: 'جميع المؤثرين في منصتنا معتمدين ومتحققين من هويتهم'
    },
    {
      icon: TrendingUp,
      title: 'نتائج مضمونة',
      description: 'احصل على أفضل النتائج من خلال مؤثرين متخصصين في مجالك'
    }
  ]

  const stats = [
    { number: '10,000+', label: 'مؤثر معتمد' },
    { number: '50,000+', label: 'حملة ناجحة' },
    { number: '95%', label: 'معدل الرضا' },
    { number: '24/7', label: 'دعم فني' }
  ]

  const testimonials = [
    {
      name: 'أحمد محمد',
      role: 'صاحب متجر إلكتروني',
      content: 'منصة رائعة ساعدتني في الوصول لعملاء جدد بطريقة فعالة وآمنة',
      rating: 5
    },
    {
      name: 'سارة العلي',
      role: 'مؤثرة على إنستغرام',
      content: 'أفضل منصة للعمل مع التجار، الدفع سريع والتعامل احترافي',
      rating: 5
    },
    {
      name: 'خالد السعد',
      role: 'مدير تسويق',
      content: 'حققنا نتائج ممتازة من خلال المنصة، أنصح بها بشدة',
      rating: 5
    }
  ]

  return (
    <div className="min-h-screen">
      <Header />

      {/* Hero Section */}
      <section className="relative bg-slate-950 text-white py-16 md:py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            {/* Trust Badge */}
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-emerald-500/20 text-emerald-300 border border-emerald-500/30 rounded-full text-sm font-medium mb-8 shadow-lg">
              <Shield className="w-4 h-4" />
              منصة سعودية موثوقة ومرخصة
            </div>

            <h1 className="heading text-white mb-6 text-shadow">
              المنصة الأولى للتسويق بالمؤثرين
              <span className="block text-indigo-300 mt-2">في المملكة العربية السعودية</span>
            </h1>

            <p className="subheading text-slate-200 max-w-2xl mx-auto mb-8">
              اربط علامتك التجارية مع أفضل المؤثرين السعوديين المعتمدين وحقق نتائج استثنائية
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <Link href="/register/merchant">
                <button className="btn-primary w-full sm:w-auto">
                  ابدأ حملتك الآن
                </button>
              </Link>
              <Link href="/register/influencer">
                <button className="btn-secondary w-full sm:w-auto">
                  انضم كمؤثر
                </button>
              </Link>
              <Link href="/register/ugc-creator">
                <button className="btn-outline w-full sm:w-auto">
                  انضم كمبدع UGC
                </button>
              </Link>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 max-w-2xl mx-auto">
              <div className="text-center bg-slate-800/50 rounded-lg p-4 border border-slate-700/50">
                <div className="text-2xl font-bold text-white mb-1 text-shadow">10,000+</div>
                <div className="text-sm text-slate-200">مؤثر معتمد</div>
              </div>
              <div className="text-center bg-slate-800/50 rounded-lg p-4 border border-slate-700/50">
                <div className="text-2xl font-bold text-white mb-1 text-shadow">50,000+</div>
                <div className="text-sm text-slate-200">حملة ناجحة</div>
              </div>
              <div className="text-center bg-slate-800/50 rounded-lg p-4 border border-slate-700/50">
                <div className="text-2xl font-bold text-white mb-1 text-shadow">95%</div>
                <div className="text-sm text-slate-200">معدل الرضا</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* UGC Creators Section */}
      <section className="py-16 md:py-20 bg-slate-800">
        <div className="max-w-7xl mx-auto mobile-container">
          <div className="text-center mb-12">
            <h2 className="heading-mobile text-gradient-secondary mb-4">
              🎬 مبدعو المحتوى UGC
            </h2>
            <p className="subheading-mobile text-gray-300 max-w-2xl mx-auto">
              اكتشف أفضل المبدعين السعوديين المتخصصين في إنتاج محتوى إعلاني عالي الجودة 🇸🇦
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            <Card className="text-center card-mobile glow-effect">
              <div className="text-5xl mb-4">🎵</div>
              <h3 className="text-xl font-bold text-white mb-3">فيديوهات تيك توك</h3>
              <p className="text-gray-300 mb-4">محتوى إبداعي وترندنج يجذب الجمهور</p>
              <div className="text-purple-400 font-semibold">من 200 ريال</div>
            </Card>

            <Card className="text-center">
              <div className="text-5xl mb-4">📸</div>
              <h3 className="text-xl font-bold text-white mb-3">تصوير منتجات</h3>
              <p className="text-gray-300 mb-4">تصوير احترافي يبرز جمال منتجاتك</p>
              <div className="text-cyan-400 font-semibold">من 300 ريال</div>
            </Card>

            <Card className="text-center">
              <div className="text-5xl mb-4">⭐</div>
              <h3 className="text-xl font-bold text-white mb-3">مراجعات صادقة</h3>
              <p className="text-gray-300 mb-4">مراجعات مفصلة تبني الثقة مع العملاء</p>
              <div className="text-pink-400 font-semibold">من 500 ريال</div>
            </Card>
          </div>

          <div className="text-center">
            <Link href="/ugc-creators">
              <Button className="btn-primary-mobile sparkle-container">
                🎬 تصفح جميع المبدعين
                <ArrowLeft className="h-5 w-5 mr-2" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 md:py-20 bg-slate-900">
        <div className="max-w-7xl mx-auto mobile-container">
          <div className="text-center mb-12">
            <h2 className="heading-mobile text-gradient-primary mb-4">
              🌟 لماذا تختار منصتنا؟
            </h2>
            <p className="subheading-mobile text-gray-300 max-w-2xl mx-auto">
              نوفر لك بيئة آمنة وموثوقة للتعاون مع أفضل المؤثرين المعتمدين في المملكة العربية السعودية ✨
            </p>

            {/* شارة إضافية للثقة */}
            <div className="flex items-center justify-center gap-2 mt-6">
              <div className="trust-indicator">
                <Shield className="w-4 h-4" />
                🏛️ مرخصة من وزارة التجارة السعودية
              </div>
              <div className="trust-indicator">
                <Star className="w-4 h-4" />
                🏆 حاصلة على شهادة الجودة ISO
              </div>
            </div>
          </div>

          <div className="grid-mobile">
            {features.map((feature, index) => {
              const Icon = feature.icon
              const emojis = ['🚀', '💎', '🛡️', '⚡', '🎯', '👑', '🌟', '💰']
              return (
                <Card key={index} hover className="text-center">
                  <div className="w-20 h-20 bg-gradient-to-r from-purple-500 to-pink-500 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-2xl glow-effect">
                    <div className="text-3xl">{emojis[index % emojis.length]}</div>
                  </div>
                  <h3 className="text-xl font-bold text-white mb-4">
                    {feature.title}
                  </h3>
                  <p className="text-gray-300 leading-relaxed">
                    {feature.description}
                  </p>

                  {/* مؤشر إضافي للثقة */}
                  <div className="mt-4 pt-4 border-t border-purple-500/20">
                    <div className="flex items-center justify-center text-sm text-cyan-400 font-medium">
                      <CheckCircle className="w-4 h-4 ml-2" />
                      ✅ مضمون ومؤكد
                    </div>
                  </div>
                </Card>
              )
            })}
          </div>

          {/* قسم الضمانات الإضافية */}
          <div className="mt-16 text-center">
            <div className="card-mobile gradient-bg p-8 sparkle-container">
              <h3 className="text-2xl font-bold text-white mb-4">
                🇸🇦 ضماناتنا لك كعميل سعودي
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="flex items-center justify-center text-white">
                  <div className="text-2xl ml-3">🛡️</div>
                  <span className="font-medium">حماية كاملة للأموال</span>
                </div>
                <div className="flex items-center justify-center text-white">
                  <div className="text-2xl ml-3">⭐</div>
                  <span className="font-medium">جودة مضمونة 100%</span>
                </div>
                <div className="flex items-center justify-center text-white">
                  <div className="text-2xl ml-3">🎧</div>
                  <span className="font-medium">دعم فني سعودي 24/7</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 md:py-20 gradient-bg text-white sparkle-container">
        <div className="max-w-7xl mx-auto mobile-container">
          <div className="text-center mb-12">
            <h2 className="heading-mobile text-white mb-4">
              📊 أرقام تتحدث عن نجاحنا
            </h2>
            <p className="subheading-mobile text-gray-200">
              إنجازات حقيقية من منصة سعودية موثوقة 🇸🇦
            </p>
          </div>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
            {stats.map((stat, index) => {
              const statEmojis = ['👥', '🎯', '💰', '⭐']
              return (
                <Card key={index} className="text-center card-mobile glow-effect">
                  <div className="text-4xl mb-3">{statEmojis[index % statEmojis.length]}</div>
                  <div className="text-3xl md:text-4xl font-bold text-gradient-gold mb-2 bounce-in" style={{ animationDelay: `${index * 0.2}s` }}>
                    {stat.number}
                  </div>
                  <div className="text-white font-medium text-sm md:text-base">
                    {stat.label}
                  </div>
                  <div className="mt-2 text-xs text-gray-300">
                    في المملكة العربية السعودية 🇸🇦
                  </div>
                </Card>
              )
            })}
          </div>

          {/* شارة إضافية */}
          <div className="text-center mt-12">
            <div className="inline-flex items-center gap-2 card-mobile px-8 py-4 glow-effect">
              <div className="text-2xl">🏆</div>
              <span className="text-white font-bold">المنصة الأولى والأكثر ثقة في السعودية</span>
              <div className="text-2xl">🏆</div>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 bg-slate-800">
        <div className="max-w-7xl mx-auto mobile-container">
          <div className="text-center mb-16">
            <h2 className="heading-mobile text-gradient-secondary mb-4">
              🔄 كيف تعمل المنصة؟
            </h2>
            <p className="subheading-mobile text-gray-300">
              خطوات بسيطة للبدء في رحلتك مع المؤثرين ✨
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card className="text-center relative card-mobile glow-effect float-animation">
              <div className="text-4xl mb-4">📝</div>
              <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold shadow-lg">
                1
              </div>
              <h3 className="text-xl font-bold text-white mb-3">اختر المؤثر المناسب</h3>
              <p className="text-gray-300">تصفح قائمة المؤثرين واختر الأنسب لعلامتك التجارية 🎯</p>
            </Card>

            <Card className="text-center relative">
              <div className="text-4xl mb-4">🤝</div>
              <div className="w-12 h-12 bg-gradient-to-r from-cyan-500 to-blue-500 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold shadow-lg">
                2
              </div>
              <h3 className="text-xl font-bold text-white mb-3">ادفع بأمان</h3>
              <p className="text-gray-300">ادفع المبلغ المتفق عليه، سنحتفظ به في نظام الضمان 🛡️</p>
            </Card>

            <Card className="text-center relative">
              <div className="text-4xl mb-4">💰</div>
              <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold shadow-lg">
                3
              </div>
              <h3 className="text-xl font-bold text-white mb-3">احصل على النتائج</h3>
              <p className="text-gray-300">بعد تنفيذ الإعلان، سنحول المبلغ للمؤثر ✅</p>
            </Card>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-slate-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4 text-shadow">
              ماذا يقول عملاؤنا؟
            </h2>
            <p className="text-slate-300 text-lg">
              تجارب حقيقية من عملائنا في المملكة العربية السعودية
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="text-center bg-slate-800/90 border-slate-700">
                <div className="flex justify-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-slate-200 mb-6 italic text-lg leading-relaxed">&ldquo;{testimonial.content}&rdquo;</p>
                <div>
                  <div className="font-semibold text-white text-lg">{testimonial.name}</div>
                  <div className="text-sm text-slate-300">{testimonial.role}</div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-indigo-600 via-purple-600 to-indigo-800 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-shadow">
            ابدأ رحلتك اليوم
          </h2>
          <p className="text-xl mb-8 text-slate-100">
            انضم إلى آلاف المؤثرين والتجار الذين يحققون النجاح من خلال منصتنا
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/register/influencer">
              <Button size="xl" variant="secondary" className="w-full sm:w-auto bg-emerald-600 hover:bg-emerald-700 text-white border-emerald-600">
                انضم كمؤثر
              </Button>
            </Link>
            <Link href="/register/merchant">
              <Button size="xl" className="w-full sm:w-auto bg-white text-indigo-700 hover:bg-slate-100 border-white font-bold">
                ابدأ حملتك الإعلانية
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <Footer />

      {/* مكونات الجوال */}
      <MobileNavigation />
      <FloatingActionButton />
    </div>
  )
}

export default HomePage
