'use client'

import React from 'react'
import Link from 'next/link'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import MobileNavigation from '@/components/layout/MobileNavigation'
import FloatingActionButton from '@/components/ui/FloatingActionButton'
import Button from '@/components/ui/Button'
import Card from '@/components/ui/Card'
import {
  Shield,
  DollarSign,
  Users,
  TrendingUp,
  Star,
  CheckCircle,
  ArrowLeft,
  Play,
  Instagram,
  Youtube,
  Twitter
} from 'lucide-react'

const HomePage: React.FC = () => {
  const features = [
    {
      icon: Shield,
      title: 'ضمان الأموال',
      description: 'نحتفظ بأموالك في نظام ضمان آمن حتى اكتمال الإعلان بنجاح'
    },
    {
      icon: DollarSign,
      title: 'استرداد مضمون',
      description: 'ضمان استرداد الأموال 100% في حالة عدم تنفيذ الإعلان كما هو متفق عليه'
    },
    {
      icon: Users,
      title: 'مؤثرين معتمدين',
      description: 'جميع المؤثرين في منصتنا معتمدين ومتحققين من هويتهم'
    },
    {
      icon: TrendingUp,
      title: 'نتائج مضمونة',
      description: 'احصل على أفضل النتائج من خلال مؤثرين متخصصين في مجالك'
    }
  ]

  const stats = [
    { number: '10,000+', label: 'مؤثر معتمد' },
    { number: '50,000+', label: 'حملة ناجحة' },
    { number: '95%', label: 'معدل الرضا' },
    { number: '24/7', label: 'دعم فني' }
  ]

  const testimonials = [
    {
      name: 'أحمد محمد',
      role: 'صاحب متجر إلكتروني',
      content: 'منصة رائعة ساعدتني في الوصول لعملاء جدد بطريقة فعالة وآمنة',
      rating: 5
    },
    {
      name: 'سارة العلي',
      role: 'مؤثرة على إنستغرام',
      content: 'أفضل منصة للعمل مع التجار، الدفع سريع والتعامل احترافي',
      rating: 5
    },
    {
      name: 'خالد السعد',
      role: 'مدير تسويق',
      content: 'حققنا نتائج ممتازة من خلال المنصة، أنصح بها بشدة',
      rating: 5
    }
  ]

  return (
    <div className="min-h-screen">
      <Header />

      {/* Hero Section */}
      <section className="relative gradient-bg text-white py-16 md:py-24 overflow-hidden sparkle-container">
        <div className="absolute inset-0 bg-black/20"></div>

        {/* خلفية متحركة مع تأثيرات جميلة */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-500/30 rounded-full blur-3xl float-animation"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-pink-500/30 rounded-full blur-3xl float-animation" style={{ animationDelay: '1s' }}></div>
          <div className="absolute top-1/2 left-1/2 w-60 h-60 bg-cyan-500/20 rounded-full blur-3xl float-animation" style={{ animationDelay: '2s' }}></div>
        </div>

        <div className="relative max-w-7xl mx-auto mobile-container">
          <div className="text-center">
            {/* شارات الثقة */}
            <div className="flex flex-wrap items-center justify-center gap-3 mb-8 fade-in">
              <div className="saudi-trust-badge glow-effect">
                <Shield className="w-4 h-4 ml-2" />
                🇸🇦 منصة سعودية موثوقة ومرخصة
              </div>
              <div className="saudi-trust-badge glow-effect" style={{ animationDelay: '0.5s' }}>
                <Star className="w-4 h-4 ml-2" />
                ⭐ أكثر من 10,000 مؤثر معتمد
              </div>
              <div className="saudi-trust-badge glow-effect" style={{ animationDelay: '1s' }}>
                <CheckCircle className="w-4 h-4 ml-2" />
                💎 ضمان استرداد الأموال 100%
              </div>
            </div>

            <h1 className="heading-mobile text-white mb-6 bounce-in">
              🚀 المنصة الأولى للتسويق بالمؤثرين
              <span className="block text-gradient-gold mt-2">في المملكة العربية السعودية 🇸🇦</span>
            </h1>

            <p className="subheading-mobile text-gray-200 max-w-3xl mx-auto mb-8 slide-up">
              اربط علامتك التجارية مع أفضل المؤثرين السعوديين المعتمدين وحقق نتائج استثنائية
              مع ضمان الأموال ونظام الدفع الآمن المطابق للمعايير السعودية ✨
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12 slide-up">
              <Link href="/register/merchant">
                <button className="btn-primary-mobile w-full sm:w-auto sparkle-container">
                  🎯 ابدأ حملتك الآن
                  <TrendingUp className="h-5 w-5 mr-3" />
                </button>
              </Link>
              <Link href="/register/influencer">
                <button className="btn-secondary-mobile w-full sm:w-auto">
                  👑 انضم كمؤثر
                  <Users className="h-5 w-5 mr-3" />
                </button>
              </Link>
            </div>

            {/* مؤشرات الثقة المحسنة */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 max-w-2xl mx-auto">
              <div className="card-mobile text-center glow-effect">
                <div className="text-4xl mb-3">🛡️</div>
                <div className="text-sm font-bold text-white">ضمان الأموال</div>
                <div className="text-xs text-gray-300">نظام حماية متقدم</div>
              </div>
              <div className="card-mobile text-center glow-effect" style={{ animationDelay: '0.3s' }}>
                <div className="text-4xl mb-3">💰</div>
                <div className="text-sm font-bold text-white">استرداد مضمون</div>
                <div className="text-xs text-gray-300">100% ضمان الاسترداد</div>
              </div>
              <div className="card-mobile text-center glow-effect" style={{ animationDelay: '0.6s' }}>
                <div className="text-4xl mb-3">⭐</div>
                <div className="text-sm font-bold text-white">مؤثرين معتمدين</div>
                <div className="text-xs text-gray-300">متحققين ومرخصين</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 md:py-20 bg-slate-900">
        <div className="max-w-7xl mx-auto mobile-container">
          <div className="text-center mb-12">
            <h2 className="heading-mobile text-gradient-primary mb-4">
              🌟 لماذا تختار منصتنا؟
            </h2>
            <p className="subheading-mobile text-gray-300 max-w-2xl mx-auto">
              نوفر لك بيئة آمنة وموثوقة للتعاون مع أفضل المؤثرين المعتمدين في المملكة العربية السعودية ✨
            </p>

            {/* شارة إضافية للثقة */}
            <div className="flex items-center justify-center gap-2 mt-6">
              <div className="trust-indicator">
                <Shield className="w-4 h-4" />
                🏛️ مرخصة من وزارة التجارة السعودية
              </div>
              <div className="trust-indicator">
                <Star className="w-4 h-4" />
                🏆 حاصلة على شهادة الجودة ISO
              </div>
            </div>
          </div>

          <div className="grid-mobile">
            {features.map((feature, index) => {
              const Icon = feature.icon
              const emojis = ['🚀', '💎', '🛡️', '⚡', '🎯', '👑', '🌟', '💰']
              return (
                <Card key={index} hover className="text-center slide-up sparkle-container" style={{ animationDelay: `${index * 0.1}s` }}>
                  <div className="w-20 h-20 bg-gradient-to-r from-purple-500 to-pink-500 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-2xl glow-effect">
                    <div className="text-3xl">{emojis[index % emojis.length]}</div>
                  </div>
                  <h3 className="text-xl font-bold text-white mb-4">
                    {feature.title}
                  </h3>
                  <p className="text-gray-300 leading-relaxed">
                    {feature.description}
                  </p>

                  {/* مؤشر إضافي للثقة */}
                  <div className="mt-4 pt-4 border-t border-purple-500/20">
                    <div className="flex items-center justify-center text-sm text-cyan-400 font-medium">
                      <CheckCircle className="w-4 h-4 ml-2" />
                      ✅ مضمون ومؤكد
                    </div>
                  </div>
                </Card>
              )
            })}
          </div>

          {/* قسم الضمانات الإضافية */}
          <div className="mt-16 text-center">
            <div className="card-mobile gradient-bg p-8 sparkle-container">
              <h3 className="text-2xl font-bold text-white mb-4">
                🇸🇦 ضماناتنا لك كعميل سعودي
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="flex items-center justify-center text-white">
                  <div className="text-2xl ml-3">🛡️</div>
                  <span className="font-medium">حماية كاملة للأموال</span>
                </div>
                <div className="flex items-center justify-center text-white">
                  <div className="text-2xl ml-3">⭐</div>
                  <span className="font-medium">جودة مضمونة 100%</span>
                </div>
                <div className="flex items-center justify-center text-white">
                  <div className="text-2xl ml-3">🎧</div>
                  <span className="font-medium">دعم فني سعودي 24/7</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 md:py-20 gradient-bg text-white sparkle-container">
        <div className="max-w-7xl mx-auto mobile-container">
          <div className="text-center mb-12">
            <h2 className="heading-mobile text-white mb-4">
              📊 أرقام تتحدث عن نجاحنا
            </h2>
            <p className="subheading-mobile text-gray-200">
              إنجازات حقيقية من منصة سعودية موثوقة 🇸🇦
            </p>
          </div>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
            {stats.map((stat, index) => {
              const statEmojis = ['👥', '🎯', '💰', '⭐']
              return (
                <Card key={index} className="text-center card-mobile glow-effect">
                  <div className="text-4xl mb-3">{statEmojis[index % statEmojis.length]}</div>
                  <div className="text-3xl md:text-4xl font-bold text-gradient-gold mb-2 bounce-in" style={{ animationDelay: `${index * 0.2}s` }}>
                    {stat.number}
                  </div>
                  <div className="text-white font-medium text-sm md:text-base">
                    {stat.label}
                  </div>
                  <div className="mt-2 text-xs text-gray-300">
                    في المملكة العربية السعودية 🇸🇦
                  </div>
                </Card>
              )
            })}
          </div>

          {/* شارة إضافية */}
          <div className="text-center mt-12">
            <div className="inline-flex items-center gap-2 card-mobile px-8 py-4 glow-effect">
              <div className="text-2xl">🏆</div>
              <span className="text-white font-bold">المنصة الأولى والأكثر ثقة في السعودية</span>
              <div className="text-2xl">🏆</div>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 bg-slate-800">
        <div className="max-w-7xl mx-auto mobile-container">
          <div className="text-center mb-16">
            <h2 className="heading-mobile text-gradient-secondary mb-4">
              🔄 كيف تعمل المنصة؟
            </h2>
            <p className="subheading-mobile text-gray-300">
              خطوات بسيطة للبدء في رحلتك مع المؤثرين ✨
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card className="text-center relative card-mobile glow-effect float-animation">
              <div className="text-4xl mb-4">📝</div>
              <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold shadow-lg">
                1
              </div>
              <h3 className="text-xl font-bold text-white mb-3">اختر المؤثر المناسب</h3>
              <p className="text-gray-300">تصفح قائمة المؤثرين واختر الأنسب لعلامتك التجارية 🎯</p>
            </Card>

            <Card className="text-center relative card-mobile glow-effect float-animation" style={{ animationDelay: '0.5s' }}>
              <div className="text-4xl mb-4">🤝</div>
              <div className="w-12 h-12 bg-gradient-to-r from-cyan-500 to-blue-500 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold shadow-lg">
                2
              </div>
              <h3 className="text-xl font-bold text-white mb-3">ادفع بأمان</h3>
              <p className="text-gray-300">ادفع المبلغ المتفق عليه، سنحتفظ به في نظام الضمان 🛡️</p>
            </Card>

            <Card className="text-center relative card-mobile glow-effect float-animation" style={{ animationDelay: '1s' }}>
              <div className="text-4xl mb-4">💰</div>
              <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold shadow-lg">
                3
              </div>
              <h3 className="text-xl font-bold text-white mb-3">احصل على النتائج</h3>
              <p className="text-gray-300">بعد تنفيذ الإعلان، سنحول المبلغ للمؤثر ✅</p>
            </Card>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              ماذا يقول عملاؤنا؟
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="text-center">
                <div className="flex justify-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-gray-600 mb-6 italic">"{testimonial.content}"</p>
                <div>
                  <div className="font-semibold text-gray-900">{testimonial.name}</div>
                  <div className="text-sm text-gray-500">{testimonial.role}</div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-primary text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            ابدأ رحلتك اليوم
          </h2>
          <p className="text-xl mb-8 opacity-90">
            انضم إلى آلاف المؤثرين والتجار الذين يحققون النجاح من خلال منصتنا
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/register/influencer">
              <Button size="xl" variant="secondary" className="w-full sm:w-auto">
                انضم كمؤثر
              </Button>
            </Link>
            <Link href="/register/merchant">
              <Button size="xl" className="w-full sm:w-auto bg-white text-primary-600 hover:bg-gray-100">
                ابدأ حملتك الإعلانية
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <Footer />

      {/* مكونات الجوال */}
      <MobileNavigation />
      <FloatingActionButton />
    </div>
  )
}

export default HomePage
