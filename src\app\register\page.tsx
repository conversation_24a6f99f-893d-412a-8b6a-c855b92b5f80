'use client'

import React from 'react'
import Link from 'next/link'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import MobileNavigation from '@/components/layout/MobileNavigation'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { Users, Store, ArrowLeft, CheckCircle } from 'lucide-react'

const RegisterPage: React.FC = () => {
  const userTypes = [
    {
      type: 'influencer',
      title: 'مؤثر',
      subtitle: 'انضم كمؤثر واربح من المحتوى',
      icon: Users,
      features: [
        'اربح من المحتوى الخاص بك',
        'تحكم كامل في أسعارك',
        'دفع سريع وآمن',
        'دعم فني متواصل',
        'إحصائيات مفصلة'
      ],
      buttonText: 'سجل كمؤثر',
      href: '/register/influencer',
      gradient: 'from-purple-500 to-pink-500'
    },
    {
      type: 'merchant',
      title: 'تاجر',
      subtitle: 'أطلق حملاتك الإعلانية مع المؤثرين',
      icon: Store,
      features: [
        'وصول لآلاف المؤثرين',
        'ضمان الأموال',
        'نتائج مضمونة',
        'تقارير مفصلة',
        'دعم فني 24/7'
      ],
      buttonText: 'سجل كتاجر',
      href: '/register/merchant',
      gradient: 'from-blue-500 to-cyan-500'
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
      <Header />

      <div className="py-20">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              انضم إلى منصة المؤثرين السعودية
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              اختر نوع حسابك وابدأ رحلتك في عالم التسويق بالمحتوى
            </p>
          </div>

          {/* User Type Selection */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
            {userTypes.map((userType) => {
              const Icon = userType.icon
              return (
                <Card key={userType.type} hover className="relative overflow-hidden">
                  {/* Gradient Background */}
                  <div className={`absolute top-0 left-0 w-full h-2 bg-gradient-to-r ${userType.gradient}`}></div>

                  <div className="p-8">
                    {/* Icon and Title */}
                    <div className="flex items-center mb-6">
                      <div className={`w-16 h-16 rounded-full bg-gradient-to-r ${userType.gradient} flex items-center justify-center`}>
                        <Icon className="h-8 w-8 text-white" />
                      </div>
                      <div className="mr-4">
                        <h2 className="text-2xl font-bold text-gray-900">{userType.title}</h2>
                        <p className="text-gray-600">{userType.subtitle}</p>
                      </div>
                    </div>

                    {/* Features */}
                    <div className="mb-8">
                      <h3 className="text-lg font-semibold text-gray-900 mb-4">المميزات:</h3>
                      <ul className="space-y-3">
                        {userType.features.map((feature, index) => (
                          <li key={index} className="flex items-center text-gray-700">
                            <CheckCircle className="h-5 w-5 text-green-500 ml-3 flex-shrink-0" />
                            <span>{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* CTA Button */}
                    <Link href={userType.href} className="block">
                      <Button
                        size="lg"
                        className={`w-full bg-gradient-to-r ${userType.gradient} hover:opacity-90 border-0`}
                      >
                        {userType.buttonText}
                        <ArrowLeft className="h-5 w-5 mr-3" />
                      </Button>
                    </Link>
                  </div>
                </Card>
              )
            })}
          </div>

          {/* Additional Info */}
          <div className="text-center">
            <Card className="max-w-4xl mx-auto">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
                <div>
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <CheckCircle className="h-8 w-8 text-green-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">تسجيل مجاني</h3>
                  <p className="text-gray-600">إنشاء الحساب مجاني تماماً</p>
                </div>

                <div>
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Users className="h-8 w-8 text-blue-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">مجتمع نشط</h3>
                  <p className="text-gray-600">انضم لآلاف المؤثرين والتجار</p>
                </div>

                <div>
                  <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Store className="h-8 w-8 text-purple-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">دعم متواصل</h3>
                  <p className="text-gray-600">فريق دعم متاح 24/7</p>
                </div>
              </div>
            </Card>
          </div>

          {/* Login Link */}
          <div className="text-center mt-12">
            <p className="text-gray-600">
              لديك حساب بالفعل؟{' '}
              <Link href="/login" className="text-primary-600 hover:text-primary-700 font-medium">
                سجل دخولك هنا
              </Link>
            </p>
          </div>
        </div>
      </div>

      <Footer />

      {/* مكونات الجوال */}
      <MobileNavigation />
    </div>
  )
}

export default RegisterPage
