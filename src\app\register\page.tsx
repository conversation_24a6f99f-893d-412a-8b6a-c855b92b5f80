'use client'

import React from 'react'
import Link from 'next/link'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import MobileNavigation from '@/components/layout/MobileNavigation'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { Users, Store, ArrowLeft, CheckCircle, Video } from 'lucide-react'

const RegisterPage: React.FC = () => {
  const userTypes = [
    {
      type: 'influencer',
      title: 'مؤثر',
      subtitle: 'انضم كمؤثر واربح من المحتوى',
      icon: Users,
      features: [
        'اربح من المحتوى الخاص بك',
        'تحكم كامل في أسعارك',
        'دفع سريع وآمن',
        'دعم فني متواصل',
        'إحصائيات مفصلة'
      ],
      buttonText: 'سجل كمؤثر',
      href: '/register/influencer',
      gradient: 'from-purple-500 to-pink-500',
      emoji: '👑'
    },
    {
      type: 'ugc-creator',
      title: 'مبدع UGC',
      subtitle: 'أنتج محتوى إعلاني عالي الجودة للبراندات',
      icon: Video,
      features: [
        'فرص عمل حصرية مع البراندات',
        'أسعار مرنة حسب نوع المحتوى',
        'أدوات إنتاج احترافية',
        'تدريب على أحدث الترندات',
        'دعم تقني متخصص'
      ],
      buttonText: 'سجل كمبدع UGC',
      href: '/register/ugc-creator',
      gradient: 'from-pink-500 to-cyan-500',
      emoji: '🎬'
    },
    {
      type: 'merchant',
      title: 'تاجر',
      subtitle: 'أطلق حملاتك الإعلانية مع المؤثرين',
      icon: Store,
      features: [
        'وصول لآلاف المؤثرين',
        'ضمان الأموال',
        'نتائج مضمونة',
        'تقارير مفصلة',
        'دعم فني 24/7'
      ],
      buttonText: 'سجل كتاجر',
      href: '/register/merchant',
      gradient: 'from-blue-500 to-cyan-500',
      emoji: '🏪'
    }
  ]

  return (
    <div className="min-h-screen bg-slate-900">
      <Header />

      <div className="py-16">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-12">
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-emerald-500/10 text-emerald-400 rounded-full text-sm font-medium mb-8">
              <CheckCircle className="w-4 h-4" />
              منصة سعودية موثوقة ومرخصة
            </div>

            <h1 className="heading text-white mb-6">
              انضم إلى منصة المؤثرين السعودية
            </h1>
            <p className="subheading text-slate-400 max-w-2xl mx-auto">
              اختر نوع حسابك وابدأ رحلتك في عالم التسويق بالمحتوى
            </p>
          </div>

          {/* User Type Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {userTypes.map((userType) => {
              const Icon = userType.icon
              return (
                <Card key={userType.type} interactive className="relative">
                  <div className="p-6">
                    {/* Icon and Title */}
                    <div className="text-center mb-6">
                      <div className="w-16 h-16 rounded-full bg-indigo-600 flex items-center justify-center mx-auto mb-4">
                        <span className="text-2xl">{userType.emoji}</span>
                      </div>
                      <div>
                        <h2 className="text-xl font-bold text-white">{userType.title}</h2>
                        <p className="text-slate-400">{userType.subtitle}</p>
                      </div>
                    </div>

                    {/* Features */}
                    <div className="mb-6">
                      <h3 className="text-sm font-medium text-slate-300 mb-3">المميزات:</h3>
                      <ul className="space-y-2">
                        {userType.features.map((feature, index) => (
                          <li key={index} className="flex items-center text-slate-400 text-sm">
                            <CheckCircle className="h-4 w-4 text-emerald-400 ml-2 flex-shrink-0" />
                            <span>{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* CTA Button */}
                    <Link href={userType.href} className="block">
                      <Button variant="primary" className="w-full">
                        {userType.buttonText}
                        <ArrowLeft className="h-4 w-4 mr-2" />
                      </Button>
                    </Link>
                  </div>
                </Card>
              )
            })}
          </div>

          {/* Additional Info */}
          <div className="text-center">
            <Card className="max-w-4xl mx-auto">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                <div>
                  <div className="w-12 h-12 bg-emerald-500/10 rounded-full flex items-center justify-center mx-auto mb-3">
                    <CheckCircle className="h-6 w-6 text-emerald-400" />
                  </div>
                  <h3 className="text-base font-semibold text-white mb-2">تسجيل مجاني</h3>
                  <p className="text-slate-400 text-sm">إنشاء الحساب مجاني تماماً</p>
                </div>

                <div>
                  <div className="w-12 h-12 bg-indigo-500/10 rounded-full flex items-center justify-center mx-auto mb-3">
                    <Users className="h-6 w-6 text-indigo-400" />
                  </div>
                  <h3 className="text-base font-semibold text-white mb-2">مجتمع نشط</h3>
                  <p className="text-slate-400 text-sm">انضم لآلاف المؤثرين والتجار</p>
                </div>

                <div>
                  <div className="w-12 h-12 bg-amber-500/10 rounded-full flex items-center justify-center mx-auto mb-3">
                    <Store className="h-6 w-6 text-amber-400" />
                  </div>
                  <h3 className="text-base font-semibold text-white mb-2">دعم متواصل</h3>
                  <p className="text-slate-400 text-sm">فريق دعم متاح 24/7</p>
                </div>
              </div>
            </Card>
          </div>

          {/* Login Link */}
          <div className="text-center mt-8">
            <p className="text-slate-400">
              لديك حساب بالفعل؟{' '}
              <Link href="/login" className="text-indigo-400 hover:text-indigo-300 font-medium">
                سجل دخولك هنا
              </Link>
            </p>
          </div>
        </div>
      </div>

      <Footer />

      {/* مكونات الجوال */}
      <MobileNavigation />
    </div>
  )
}

export default RegisterPage
