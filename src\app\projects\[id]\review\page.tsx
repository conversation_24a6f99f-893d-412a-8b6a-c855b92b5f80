'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { 
  ArrowRight, 
  Star, 
  Download, 
  MessageCircle, 
  CheckCircle, 
  XCircle,
  Edit3,
  ThumbsUp,
  ThumbsDown,
  Send,
  Upload,
  Play,
  Eye,
  Heart,
  Share2
} from 'lucide-react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import MobileNavigation from '@/components/layout/MobileNavigation'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'

interface ProjectReviewPageProps {
  params: {
    id: string
  }
}

const ProjectReviewPage: React.FC<ProjectReviewPageProps> = ({ params }) => {
  const [rating, setRating] = useState(0)
  const [review, setReview] = useState('')
  const [revisionRequest, setRevisionRequest] = useState('')
  const [showRevisionForm, setShowRevisionForm] = useState(false)

  // Mock project data
  const project = {
    id: parseInt(params.id),
    title: 'فيديو تيك توك لمنتج العناية الجديد',
    creator: {
      name: 'سارة المبدعة',
      avatar: '👩‍🎨',
      id: 1
    },
    client: {
      name: 'براند الجمال',
      avatar: '💄'
    },
    status: 'مكتمل - في انتظار المراجعة',
    deliveredAt: '2024-01-20',
    budget: 800,
    description: 'إنتاج فيديو تيك توك إبداعي لإطلاق منتج العناية الجديد مع التركيز على المميزات الفريدة',
    deliverables: [
      {
        id: 1,
        type: 'video',
        title: 'الفيديو النهائي - تيك توك',
        filename: 'tiktok_final_v1.mp4',
        size: '25.4 MB',
        duration: '30 ثانية',
        thumbnail: '🎬',
        downloadUrl: '#'
      },
      {
        id: 2,
        type: 'video',
        title: 'نسخة بدون موسيقى',
        filename: 'tiktok_no_music.mp4',
        size: '22.1 MB',
        duration: '30 ثانية',
        thumbnail: '🔇',
        downloadUrl: '#'
      },
      {
        id: 3,
        type: 'image',
        title: 'صور خلف الكواليس',
        filename: 'behind_scenes.zip',
        size: '15.8 MB',
        count: '8 صور',
        thumbnail: '📸',
        downloadUrl: '#'
      }
    ],
    requirements: [
      'مدة 30 ثانية',
      'مونتاج احترافي',
      'موسيقى ترندنج',
      'إبراز مميزات المنتج',
      'دعوة للعمل واضحة'
    ],
    revisions: [
      {
        id: 1,
        date: '2024-01-18',
        request: 'يرجى تغيير الموسيقى إلى شيء أكثر حيوية',
        response: 'تم تغيير الموسيقى كما طلبت',
        status: 'مكتمل'
      }
    ]
  }

  const handleApprove = () => {
    if (rating === 0) {
      alert('يرجى تقييم العمل أولاً')
      return
    }
    
    console.log('Project approved with rating:', rating, 'and review:', review)
    alert(`تم قبول المشروع بنجاح! 🎉
    
التقييم: ${rating} نجوم
${review ? `التعليق: ${review}` : ''}

سيتم تحويل المبلغ للمبدع خلال 24 ساعة.`)
  }

  const handleRequestRevision = () => {
    if (!revisionRequest.trim()) {
      alert('يرجى كتابة تفاصيل التعديل المطلوب')
      return
    }
    
    console.log('Revision requested:', revisionRequest)
    alert('تم إرسال طلب التعديل للمبدع. سيتم التواصل معك قريباً.')
    setRevisionRequest('')
    setShowRevisionForm(false)
  }

  const handleReject = () => {
    if (confirm('هل أنت متأكد من رفض المشروع؟ سيتم فتح نزاع للمراجعة.')) {
      console.log('Project rejected')
      alert('تم رفض المشروع. سيتم التواصل معك من فريق الدعم لحل النزاع.')
    }
  }

  return (
    <div className="min-h-screen bg-slate-900">
      <Header />
      
      <div className="py-12">
        <div className="max-w-6xl mx-auto mobile-container">
          {/* Header */}
          <div className="mb-8">
            <Link href="/dashboard" className="inline-flex items-center text-purple-400 hover:text-purple-300 mb-4">
              <ArrowRight className="h-5 w-5 ml-2" />
              العودة للوحة التحكم
            </Link>
            
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-2xl font-bold text-white mb-2">{project.title}</h1>
                <div className="flex items-center gap-4 text-gray-300">
                  <span>🎬 {project.creator.name}</span>
                  <span>💰 {project.budget} ريال</span>
                  <span>📅 تم التسليم في {project.deliveredAt}</span>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <span className="px-4 py-2 bg-yellow-500/20 text-yellow-400 rounded-full text-sm font-medium">
                  {project.status}
                </span>
              </div>
            </div>

            {/* Project Info */}
            <Card className="card-mobile">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-bold text-white mb-3">تفاصيل المشروع</h3>
                  <p className="text-gray-300 mb-4">{project.description}</p>
                  
                  <h4 className="font-semibold text-white mb-2">المتطلبات:</h4>
                  <ul className="space-y-1">
                    {project.requirements.map((req, i) => (
                      <li key={i} className="text-gray-300 text-sm flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-400 ml-2" />
                        {req}
                      </li>
                    ))}
                  </ul>
                </div>
                
                <div>
                  <h3 className="font-bold text-white mb-3">المشاركون</h3>
                  <div className="space-y-3">
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-lg ml-3">
                        {project.creator.avatar}
                      </div>
                      <div>
                        <div className="text-white font-medium">{project.creator.name}</div>
                        <div className="text-gray-400 text-sm">المبدع</div>
                      </div>
                    </div>
                    
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center text-lg ml-3">
                        {project.client.avatar}
                      </div>
                      <div>
                        <div className="text-white font-medium">{project.client.name}</div>
                        <div className="text-gray-400 text-sm">العميل</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Deliverables */}
            <div className="lg:col-span-2 space-y-6">
              <Card className="card-mobile">
                <h2 className="text-xl font-bold text-white mb-6">الملفات المسلمة</h2>
                
                <div className="space-y-4">
                  {project.deliverables.map((file) => (
                    <div key={file.id} className="flex items-center justify-between p-4 bg-white/5 rounded-2xl hover:bg-white/10 transition-colors">
                      <div className="flex items-center">
                        <div className="text-3xl ml-4">{file.thumbnail}</div>
                        <div>
                          <h3 className="font-semibold text-white">{file.title}</h3>
                          <div className="text-gray-400 text-sm">
                            {file.filename} • {file.size}
                            {file.duration && ` • ${file.duration}`}
                            {file.count && ` • ${file.count}`}
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        {file.type === 'video' && (
                          <Button variant="secondary" size="sm" className="btn-secondary-mobile">
                            <Play className="h-4 w-4 ml-1" />
                            معاينة
                          </Button>
                        )}
                        <Button variant="secondary" size="sm" className="btn-secondary-mobile">
                          <Download className="h-4 w-4 ml-1" />
                          تحميل
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </Card>

              {/* Previous Revisions */}
              {project.revisions.length > 0 && (
                <Card className="card-mobile">
                  <h3 className="text-lg font-bold text-white mb-4">التعديلات السابقة</h3>
                  <div className="space-y-3">
                    {project.revisions.map((revision) => (
                      <div key={revision.id} className="p-4 bg-white/5 rounded-xl">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm text-gray-400">{revision.date}</span>
                          <span className="px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded-full">
                            {revision.status}
                          </span>
                        </div>
                        <div className="text-white text-sm mb-1">
                          <strong>الطلب:</strong> {revision.request}
                        </div>
                        <div className="text-gray-300 text-sm">
                          <strong>الرد:</strong> {revision.response}
                        </div>
                      </div>
                    ))}
                  </div>
                </Card>
              )}
            </div>

            {/* Review Panel */}
            <div className="lg:col-span-1">
              <Card className="card-mobile sticky top-8">
                <h3 className="text-lg font-bold text-white mb-6">مراجعة المشروع</h3>
                
                {/* Rating */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-300 mb-3">
                    تقييم العمل (مطلوب)
                  </label>
                  <div className="flex items-center gap-1">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <button
                        key={star}
                        onClick={() => setRating(star)}
                        className={`text-2xl transition-colors ${
                          star <= rating ? 'text-yellow-400' : 'text-gray-600 hover:text-yellow-300'
                        }`}
                      >
                        <Star className={`h-8 w-8 ${star <= rating ? 'fill-current' : ''}`} />
                      </button>
                    ))}
                  </div>
                  {rating > 0 && (
                    <div className="text-sm text-gray-400 mt-1">
                      {rating === 5 ? 'ممتاز! 🌟' : 
                       rating === 4 ? 'جيد جداً 👍' : 
                       rating === 3 ? 'جيد 👌' : 
                       rating === 2 ? 'مقبول 😐' : 'يحتاج تحسين 😕'}
                    </div>
                  )}
                </div>

                {/* Review Comment */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    تعليق على العمل (اختياري)
                  </label>
                  <textarea
                    className="input-mobile min-h-[100px]"
                    placeholder="شاركنا رأيك في العمل..."
                    value={review}
                    onChange={(e) => setReview(e.target.value)}
                  />
                </div>

                {/* Action Buttons */}
                <div className="space-y-3">
                  <Button 
                    onClick={handleApprove}
                    className="btn-primary-mobile w-full sparkle-container"
                    disabled={rating === 0}
                  >
                    <CheckCircle className="h-5 w-5 ml-2" />
                    قبول العمل والدفع
                  </Button>
                  
                  <Button 
                    variant="secondary"
                    onClick={() => setShowRevisionForm(!showRevisionForm)}
                    className="btn-secondary-mobile w-full"
                  >
                    <Edit3 className="h-5 w-5 ml-2" />
                    طلب تعديل
                  </Button>
                  
                  <Button 
                    variant="secondary"
                    onClick={handleReject}
                    className="w-full bg-red-500/20 text-red-400 hover:bg-red-500/30 border-red-500/30"
                  >
                    <XCircle className="h-5 w-5 ml-2" />
                    رفض العمل
                  </Button>
                </div>

                {/* Revision Form */}
                {showRevisionForm && (
                  <div className="mt-6 p-4 bg-white/5 rounded-xl">
                    <h4 className="font-semibold text-white mb-3">طلب تعديل</h4>
                    <textarea
                      className="input-mobile min-h-[80px] mb-3"
                      placeholder="اكتب تفاصيل التعديل المطلوب..."
                      value={revisionRequest}
                      onChange={(e) => setRevisionRequest(e.target.value)}
                    />
                    <div className="flex gap-2">
                      <Button 
                        onClick={handleRequestRevision}
                        size="sm"
                        className="btn-primary-mobile flex-1"
                      >
                        <Send className="h-4 w-4 ml-1" />
                        إرسال
                      </Button>
                      <Button 
                        variant="secondary"
                        onClick={() => setShowRevisionForm(false)}
                        size="sm"
                        className="btn-secondary-mobile"
                      >
                        إلغاء
                      </Button>
                    </div>
                  </div>
                )}

                {/* Contact Creator */}
                <div className="mt-6 pt-6 border-t border-gray-600">
                  <Link href={`/messages?chat=${project.creator.id}`}>
                    <Button variant="secondary" className="btn-secondary-mobile w-full">
                      <MessageCircle className="h-5 w-5 ml-2" />
                      تواصل مع المبدع
                    </Button>
                  </Link>
                </div>

                {/* Help */}
                <div className="mt-4 text-center">
                  <p className="text-xs text-gray-400">
                    تحتاج مساعدة؟ 
                    <Link href="/support" className="text-purple-400 hover:text-purple-300 mr-1">
                      تواصل مع الدعم
                    </Link>
                  </p>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </div>

      <Footer />
      <MobileNavigation />
    </div>
  )
}

export default ProjectReviewPage
