'use client'

import React from 'react'
import Link from 'next/link'
import { Facebook, Twitter, Instagram, Youtube, Mail, Phone, MapPin, Shield, Star } from 'lucide-react'

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear()

  const footerLinks = {
    platform: [
      { name: 'كيف يعمل', href: '/how-it-works' },
      { name: 'الأسعار', href: '/pricing' },
      { name: 'المؤثرين', href: '/influencers' },
      { name: 'للتجار', href: '/merchants' },
    ],
    support: [
      { name: 'مركز المساعدة', href: '/help' },
      { name: 'اتصل بنا', href: '/contact' },
      { name: 'الأسئلة الشائعة', href: '/faq' },
      { name: 'الدعم الفني', href: '/support' },
    ],
    legal: [
      { name: 'شروط الاستخدام', href: '/terms' },
      { name: 'سياسة الخصوصية', href: '/privacy' },
      { name: 'سياسة الاسترداد', href: '/refund' },
      { name: 'اتفاقية المستخدم', href: '/agreement' },
    ],
    company: [
      { name: 'من نحن', href: '/about' },
      { name: 'فريق العمل', href: '/team' },
      { name: 'الوظائف', href: '/careers' },
      { name: 'الأخبار', href: '/news' },
    ],
  }

  const socialLinks = [
    { name: 'Facebook', icon: Facebook, href: '#' },
    { name: 'Twitter', icon: Twitter, href: '#' },
    { name: 'Instagram', icon: Instagram, href: '#' },
    { name: 'YouTube', icon: Youtube, href: '#' },
  ]

  return (
    <footer className="bg-slate-900 text-white border-t border-purple-500/20">
      <div className="max-w-7xl mx-auto mobile-container py-12">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-2">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center glow-effect">
                <span className="text-2xl">🚀</span>
              </div>
              <span className="mr-3 text-xl font-bold">منصة المؤثرين السعودية</span>
            </div>
            <p className="text-gray-300 mb-6 leading-relaxed">
              منصة احترافية تربط المؤثرين بالتجار في المملكة العربية السعودية 🇸🇦
              نوفر بيئة آمنة وموثوقة للتعاون التجاري مع ضمان الأموال ونظام الدفع الآمن ✨
            </p>

            {/* شارات الثقة */}
            <div className="flex flex-wrap gap-2 mb-6">
              <div className="trust-indicator">
                <Shield className="w-3 h-3" />
                🛡️ منصة سعودية موثوقة
              </div>
              <div className="trust-indicator">
                <Star className="w-3 h-3" />
                ⭐ معتمدة ومرخصة
              </div>
            </div>

            {/* Contact Info */}
            <div className="space-y-3">
              <div className="flex items-center text-gray-300">
                <Mail className="h-5 w-5 ml-3" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center text-gray-300">
                <Phone className="h-5 w-5 ml-3" />
                <span>+966 11 123 4567</span>
              </div>
              <div className="flex items-center text-gray-300">
                <MapPin className="h-5 w-5 ml-3" />
                <span>الرياض، المملكة العربية السعودية</span>
              </div>
            </div>
          </div>

          {/* Platform Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">المنصة</h3>
            <ul className="space-y-3">
              {footerLinks.platform.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-300 hover:text-white transition-colors duration-200"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Support Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">الدعم</h3>
            <ul className="space-y-3">
              {footerLinks.support.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-300 hover:text-white transition-colors duration-200"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Legal Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">قانوني</h3>
            <ul className="space-y-3">
              {footerLinks.legal.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-300 hover:text-white transition-colors duration-200"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Social Media & Newsletter */}
        <div className="border-t border-gray-800 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            {/* Social Media */}
            <div className="flex items-center space-x-6 space-x-reverse mb-4 md:mb-0">
              <span className="text-gray-300 font-medium">تابعنا على:</span>
              {socialLinks.map((social) => {
                const Icon = social.icon
                return (
                  <Link
                    key={social.name}
                    href={social.href}
                    className="text-gray-400 hover:text-white transition-colors duration-200"
                  >
                    <Icon className="h-6 w-6" />
                  </Link>
                )
              })}
            </div>

            {/* Newsletter */}
            <div className="flex items-center space-x-4 space-x-reverse">
              <span className="text-gray-300 font-medium">اشترك في النشرة الإخبارية:</span>
              <div className="flex">
                <input
                  type="email"
                  placeholder="البريد الإلكتروني"
                  className="px-4 py-2 bg-gray-800 border border-gray-700 rounded-r-lg focus:outline-none focus:ring-2 focus:ring-primary-500 text-white"
                />
                <button className="px-4 py-2 bg-primary-600 hover:bg-primary-700 rounded-l-lg transition-colors duration-200">
                  اشتراك
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Copyright */}
        <div className="border-t border-gray-800 mt-8 pt-8 text-center">
          <p className="text-gray-400">
            © {currentYear} منصة المؤثرين السعودية. جميع الحقوق محفوظة.
          </p>
        </div>
      </div>
    </footer>
  )
}

export default Footer
