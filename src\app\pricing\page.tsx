'use client'

import React from 'react'
import Link from 'next/link'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { 
  Check, 
  Star, 
  Crown, 
  Zap,
  Shield,
  Users,
  TrendingUp,
  ArrowLeft
} from 'lucide-react'

const PricingPage: React.FC = () => {
  const plans = [
    {
      name: 'الباقة الأساسية',
      price: 'مجاني',
      period: '',
      description: 'مثالية للمؤثرين المبتدئين',
      icon: Users,
      color: 'from-gray-500 to-gray-600',
      features: [
        'إنشاء ملف شخصي',
        'عرض في قائمة المؤثرين',
        'استقبال 5 طلبات شهرياً',
        'دعم فني أساسي',
        'عمولة 10% على كل صفقة'
      ],
      limitations: [
        'محدود بـ 5 طلبات شهرياً',
        'لا يشمل الترويج المميز'
      ],
      buttonText: 'ابدأ مجاناً',
      popular: false
    },
    {
      name: 'الباقة المتقدمة',
      price: '99',
      period: 'شهرياً',
      description: 'الأفضل للمؤثرين النشطين',
      icon: Star,
      color: 'from-blue-500 to-blue-600',
      features: [
        'جميع مميزات الباقة الأساسية',
        'طلبات غير محدودة',
        'ترويج مميز في النتائج',
        'إحصائيات مفصلة',
        'دعم فني أولوية',
        'عمولة 7% على كل صفقة',
        'أدوات تحليل متقدمة'
      ],
      limitations: [],
      buttonText: 'اشترك الآن',
      popular: true
    },
    {
      name: 'الباقة الاحترافية',
      price: '199',
      period: 'شهرياً',
      description: 'للمؤثرين المحترفين والوكالات',
      icon: Crown,
      color: 'from-purple-500 to-purple-600',
      features: [
        'جميع مميزات الباقة المتقدمة',
        'مدير حساب مخصص',
        'ترويج مميز في الصفحة الرئيسية',
        'تقارير مخصصة',
        'دعم فني 24/7',
        'عمولة 5% على كل صفقة',
        'إدارة حملات متعددة',
        'أولوية في عرض الملف'
      ],
      limitations: [],
      buttonText: 'اشترك الآن',
      popular: false
    }
  ]

  const merchantPlans = [
    {
      name: 'باقة البداية',
      price: 'مجاني',
      period: '',
      description: 'للتجار الجدد',
      features: [
        'إنشاء حساب تاجر',
        'تصفح المؤثرين',
        '3 حملات شهرياً',
        'دعم فني أساسي',
        'تقارير أساسية'
      ],
      buttonText: 'ابدأ مجاناً'
    },
    {
      name: 'باقة النمو',
      price: '149',
      period: 'شهرياً',
      description: 'للأعمال المتنامية',
      features: [
        'حملات غير محدودة',
        'أدوات تحليل متقدمة',
        'دعم فني أولوية',
        'تقارير مفصلة',
        'إدارة حملات متعددة',
        'فلاتر بحث متقدمة'
      ],
      buttonText: 'اشترك الآن',
      popular: true
    },
    {
      name: 'باقة المؤسسات',
      price: 'حسب الطلب',
      period: '',
      description: 'للشركات الكبيرة',
      features: [
        'جميع المميزات',
        'مدير حساب مخصص',
        'تكامل API',
        'تقارير مخصصة',
        'دعم فني 24/7',
        'تدريب الفريق'
      ],
      buttonText: 'تواصل معنا'
    }
  ]

  const faqs = [
    {
      question: 'هل يمكنني تغيير الباقة في أي وقت؟',
      answer: 'نعم، يمكنك ترقية أو تخفيض باقتك في أي وقت. التغييرات ستطبق في دورة الفوترة التالية.'
    },
    {
      question: 'ما هي طرق الدفع المتاحة؟',
      answer: 'نقبل جميع البطاقات الائتمانية الرئيسية، Apple Pay، Google Pay، والتحويل البنكي.'
    },
    {
      question: 'هل هناك رسوم إضافية؟',
      answer: 'لا، جميع الأسعار شاملة ولا توجد رسوم خفية. العمولة المذكورة هي الوحيدة المطبقة على الصفقات.'
    },
    {
      question: 'هل يمكنني إلغاء الاشتراك؟',
      answer: 'نعم، يمكنك إلغاء اشتراكك في أي وقت. ستستمر في الاستفادة من الخدمة حتى نهاية الفترة المدفوعة.'
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
      <Header />
      
      {/* Hero Section */}
      <section className="py-20 bg-gradient-primary text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            اختر الباقة المناسبة لك
          </h1>
          <p className="text-xl mb-8 opacity-90">
            باقات مرنة تناسب جميع احتياجاتك مع ضمان أفضل قيمة مقابل المال
          </p>
        </div>
      </section>

      {/* Influencer Plans */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              باقات المؤثرين
            </h2>
            <p className="text-xl text-gray-600">
              اختر الباقة التي تناسب مستوى نشاطك كمؤثر
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {plans.map((plan, index) => {
              const Icon = plan.icon
              return (
                <Card key={index} className={`relative overflow-hidden ${plan.popular ? 'ring-2 ring-primary-500' : ''}`}>
                  {plan.popular && (
                    <div className="absolute top-0 left-0 right-0 bg-primary-500 text-white text-center py-2 text-sm font-medium">
                      الأكثر شعبية
                    </div>
                  )}
                  
                  <div className={`h-2 bg-gradient-to-r ${plan.color} ${plan.popular ? 'mt-8' : ''}`}></div>
                  
                  <div className="p-8">
                    <div className="flex items-center mb-4">
                      <div className={`w-12 h-12 rounded-full bg-gradient-to-r ${plan.color} flex items-center justify-center`}>
                        <Icon className="h-6 w-6 text-white" />
                      </div>
                      <div className="mr-4">
                        <h3 className="text-xl font-bold text-gray-900">{plan.name}</h3>
                        <p className="text-gray-600 text-sm">{plan.description}</p>
                      </div>
                    </div>

                    <div className="mb-6">
                      <div className="flex items-baseline">
                        <span className="text-4xl font-bold text-gray-900">{plan.price}</span>
                        {plan.period && (
                          <span className="text-gray-600 mr-2">ريال {plan.period}</span>
                        )}
                      </div>
                    </div>

                    <ul className="space-y-3 mb-8">
                      {plan.features.map((feature, i) => (
                        <li key={i} className="flex items-center text-gray-700">
                          <Check className="h-5 w-5 text-green-500 ml-3 flex-shrink-0" />
                          <span>{feature}</span>
                        </li>
                      ))}
                    </ul>

                    <Button 
                      className={`w-full ${plan.popular ? 'bg-primary-600 hover:bg-primary-700' : ''}`}
                      variant={plan.popular ? 'primary' : 'outline'}
                    >
                      {plan.buttonText}
                    </Button>
                  </div>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      {/* Merchant Plans */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              باقات التجار
            </h2>
            <p className="text-xl text-gray-600">
              حلول تسويقية متكاملة لجميع أحجام الأعمال
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {merchantPlans.map((plan, index) => (
              <Card key={index} className={`${plan.popular ? 'ring-2 ring-blue-500' : ''}`}>
                {plan.popular && (
                  <div className="bg-blue-500 text-white text-center py-2 text-sm font-medium">
                    الأكثر اختياراً
                  </div>
                )}
                
                <div className="p-8">
                  <h3 className="text-xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                  <p className="text-gray-600 text-sm mb-4">{plan.description}</p>

                  <div className="mb-6">
                    <div className="flex items-baseline">
                      <span className="text-4xl font-bold text-gray-900">{plan.price}</span>
                      {plan.period && (
                        <span className="text-gray-600 mr-2">ريال {plan.period}</span>
                      )}
                    </div>
                  </div>

                  <ul className="space-y-3 mb-8">
                    {plan.features.map((feature, i) => (
                      <li key={i} className="flex items-center text-gray-700">
                        <Check className="h-5 w-5 text-green-500 ml-3 flex-shrink-0" />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>

                  <Button 
                    className="w-full"
                    variant={plan.popular ? 'primary' : 'outline'}
                  >
                    {plan.buttonText}
                  </Button>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              الأسئلة الشائعة
            </h2>
          </div>

          <div className="space-y-6">
            {faqs.map((faq, index) => (
              <Card key={index}>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  {faq.question}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {faq.answer}
                </p>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-primary text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            جاهز للبدء؟
          </h2>
          <p className="text-xl mb-8 opacity-90">
            اختر باقتك وابدأ رحلتك في عالم التسويق بالمحتوى اليوم
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/register/influencer">
              <Button size="xl" variant="secondary" className="w-full sm:w-auto">
                انضم كمؤثر
              </Button>
            </Link>
            <Link href="/register/merchant">
              <Button size="xl" className="w-full sm:w-auto bg-white text-primary-600 hover:bg-gray-100">
                انضم كتاجر
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}

export default PricingPage
