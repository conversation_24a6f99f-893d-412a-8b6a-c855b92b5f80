'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import { 
  Search, 
  ChevronDown, 
  ChevronUp,
  Users,
  Store,
  Shield,
  CreditCard,
  HelpCircle,
  MessageCircle
} from 'lucide-react'

const FAQPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [activeCategory, setActiveCategory] = useState('all')
  const [expandedItems, setExpandedItems] = useState<number[]>([])

  const categories = [
    { id: 'all', name: 'جميع الأسئلة', icon: HelpCircle },
    { id: 'influencers', name: 'للمؤثرين', icon: Users },
    { id: 'merchants', name: 'للتجار', icon: Store },
    { id: 'payments', name: 'المدفوعات', icon: CreditCard },
    { id: 'security', name: 'الأمان', icon: Shield }
  ]

  const faqs = [
    {
      id: 1,
      category: 'influencers',
      question: 'كيف يمكنني التسجيل كمؤثر في المنصة؟',
      answer: 'يمكنك التسجيل كمؤثر من خلال النقر على "سجل كمؤثر" في الصفحة الرئيسية، ثم ملء النموذج المطلوب بمعلوماتك الشخصية وحساباتك على وسائل التواصل الاجتماعي وتحديد أسعارك.'
    },
    {
      id: 2,
      category: 'influencers',
      question: 'ما هي الشروط المطلوبة للانضمام كمؤثر؟',
      answer: 'يجب أن يكون لديك حساب نشط على إحدى منصات التواصل الاجتماعي مع عدد متابعين لا يقل عن 1000 متابع، وأن تكون مقيماً في المملكة العربية السعودية، وأن تلتزم بمعايير المحتوى الخاصة بالمنصة.'
    },
    {
      id: 3,
      category: 'influencers',
      question: 'كيف أحدد أسعاري للخدمات المختلفة؟',
      answer: 'يمكنك تحديد أسعارك بناءً على عدد متابعيك ومعدل التفاعل وجودة المحتوى. ننصح بمراجعة أسعار المؤثرين الآخرين في نفس تخصصك لتحديد أسعار تنافسية.'
    },
    {
      id: 4,
      category: 'influencers',
      question: 'متى أحصل على أجري بعد إكمال الحملة؟',
      answer: 'ستحصل على أجرك خلال 24-48 ساعة من موافقة التاجر على المحتوى المسلم وإكمال الحملة بنجاح.'
    },
    {
      id: 5,
      category: 'merchants',
      question: 'كيف يمكنني العثور على المؤثر المناسب لعلامتي التجارية؟',
      answer: 'يمكنك استخدام فلاتر البحث المتقدمة للعثور على المؤثرين حسب التخصص والموقع وعدد المتابعين والأسعار. كما يمكنك مراجعة ملفاتهم الشخصية وأعمالهم السابقة.'
    },
    {
      id: 6,
      category: 'merchants',
      question: 'ما هو نظام ضمان الأموال؟',
      answer: 'نحتفظ بأموالك في نظام ضمان آمن حتى يكمل المؤثر الحملة ويسلم المحتوى المطلوب. لن يتم تحويل المبلغ للمؤثر إلا بعد موافقتك على النتائج.'
    },
    {
      id: 7,
      category: 'merchants',
      question: 'هل يمكنني طلب تعديلات على المحتوى؟',
      answer: 'نعم، يمكنك طلب تعديلات معقولة على المحتوى قبل الموافقة النهائية. ننصح بتوضيح متطلباتك بدقة منذ البداية لتجنب سوء الفهم.'
    },
    {
      id: 8,
      category: 'payments',
      question: 'ما هي طرق الدفع المتاحة؟',
      answer: 'نقبل جميع البطاقات الائتمانية الرئيسية (فيزا، ماستركارد)، Apple Pay، Google Pay، والتحويل البنكي المحلي.'
    },
    {
      id: 9,
      category: 'payments',
      question: 'هل هناك رسوم إضافية على المعاملات؟',
      answer: 'نتقاضى عمولة بسيطة على كل معاملة ناجحة (5-10% حسب نوع الحساب). لا توجد رسوم خفية أخرى.'
    },
    {
      id: 10,
      category: 'payments',
      question: 'هل يمكنني استرداد أموالي؟',
      answer: 'نعم، يمكنك استرداد أموالك بالكامل إذا لم يلتزم المؤثر بتسليم المحتوى في الموعد المحدد أو إذا لم يكن المحتوى مطابقاً للمتفق عليه.'
    },
    {
      id: 11,
      category: 'security',
      question: 'كيف تحمون معلوماتي الشخصية؟',
      answer: 'نستخدم أحدث تقنيات التشفير لحماية بياناتك. جميع المعلومات الشخصية والمالية محمية وفقاً لأعلى معايير الأمان العالمية.'
    },
    {
      id: 12,
      category: 'security',
      question: 'هل المنصة آمنة للاستخدام؟',
      answer: 'نعم، المنصة آمنة تماماً. نحن مرخصون ومنظمون، ونستخدم نظام ضمان الأموال لحماية جميع الأطراف.'
    }
  ]

  const toggleExpanded = (id: number) => {
    setExpandedItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    )
  }

  const filteredFAQs = faqs.filter(faq => {
    const matchesCategory = activeCategory === 'all' || faq.category === activeCategory
    const matchesSearch = faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         faq.answer.toLowerCase().includes(searchTerm.toLowerCase())
    return matchesCategory && matchesSearch
  })

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
      <Header />
      
      {/* Hero Section */}
      <section className="py-20 bg-gradient-primary text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            الأسئلة الشائعة
          </h1>
          <p className="text-xl mb-8 opacity-90">
            ابحث عن إجابات لأسئلتك الأكثر شيوعاً حول استخدام المنصة
          </p>
          
          {/* Search */}
          <div className="max-w-md mx-auto">
            <Input
              placeholder="ابحث في الأسئلة الشائعة..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              leftIcon={<Search className="h-5 w-5" />}
              className="bg-white"
            />
          </div>
        </div>
      </section>

      <div className="py-20">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Categories Sidebar */}
            <div className="lg:col-span-1">
              <Card>
                <h3 className="text-lg font-bold text-gray-900 mb-4">التصنيفات</h3>
                <div className="space-y-2">
                  {categories.map((category) => {
                    const Icon = category.icon
                    return (
                      <button
                        key={category.id}
                        onClick={() => setActiveCategory(category.id)}
                        className={`w-full flex items-center px-3 py-2 rounded-lg text-right transition-colors ${
                          activeCategory === category.id
                            ? 'bg-primary-100 text-primary-700'
                            : 'text-gray-700 hover:bg-gray-100'
                        }`}
                      >
                        <Icon className="h-5 w-5 ml-3" />
                        <span>{category.name}</span>
                      </button>
                    )
                  })}
                </div>
              </Card>

              {/* Contact Support */}
              <Card className="mt-6">
                <h3 className="text-lg font-bold text-gray-900 mb-4">
                  لم تجد إجابتك؟
                </h3>
                <p className="text-gray-600 mb-4 text-sm">
                  تواصل مع فريق الدعم للحصول على مساعدة شخصية
                </p>
                <Link href="/contact">
                  <Button variant="outline" size="sm" className="w-full">
                    <MessageCircle className="h-4 w-4 ml-2" />
                    تواصل معنا
                  </Button>
                </Link>
              </Card>
            </div>

            {/* FAQ Content */}
            <div className="lg:col-span-3">
              {filteredFAQs.length === 0 ? (
                <Card className="text-center py-12">
                  <HelpCircle className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    لم نجد أي نتائج
                  </h3>
                  <p className="text-gray-600">
                    جرب البحث بكلمات مختلفة أو تصفح التصنيفات المختلفة
                  </p>
                </Card>
              ) : (
                <div className="space-y-4">
                  {filteredFAQs.map((faq) => (
                    <Card key={faq.id} className="overflow-hidden">
                      <button
                        onClick={() => toggleExpanded(faq.id)}
                        className="w-full flex items-center justify-between p-6 text-right hover:bg-gray-50 transition-colors"
                      >
                        <h3 className="text-lg font-semibold text-gray-900 flex-1">
                          {faq.question}
                        </h3>
                        {expandedItems.includes(faq.id) ? (
                          <ChevronUp className="h-5 w-5 text-gray-500 mr-4 flex-shrink-0" />
                        ) : (
                          <ChevronDown className="h-5 w-5 text-gray-500 mr-4 flex-shrink-0" />
                        )}
                      </button>
                      
                      {expandedItems.includes(faq.id) && (
                        <div className="px-6 pb-6 border-t border-gray-100">
                          <p className="text-gray-700 leading-relaxed pt-4">
                            {faq.answer}
                          </p>
                        </div>
                      )}
                    </Card>
                  ))}
                </div>
              )}

              {/* Quick Actions */}
              <div className="mt-12 grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card hover className="text-center">
                  <Users className="h-12 w-12 text-primary-600 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    دليل المؤثرين
                  </h3>
                  <p className="text-gray-600 mb-4">
                    تعلم كيفية تحقيق أقصى استفادة من المنصة كمؤثر
                  </p>
                  <Link href="/help">
                    <Button variant="outline" size="sm">
                      اقرأ المزيد
                    </Button>
                  </Link>
                </Card>

                <Card hover className="text-center">
                  <Store className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    دليل التجار
                  </h3>
                  <p className="text-gray-600 mb-4">
                    اكتشف كيفية إطلاق حملات ناجحة مع المؤثرين
                  </p>
                  <Link href="/help">
                    <Button variant="outline" size="sm">
                      اقرأ المزيد
                    </Button>
                  </Link>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  )
}

export default FAQPage
