'use client'

import React from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { 
  Home, 
  Users, 
  Search, 
  MessageCircle, 
  User,
  Store,
  TrendingUp,
  Heart,
  Bell
} from 'lucide-react'

const MobileNavigation: React.FC = () => {
  const pathname = usePathname()

  const navigationItems = [
    {
      name: 'الرئيسية',
      href: '/',
      icon: Home,
      color: 'text-green-600'
    },
    {
      name: 'المؤثرين',
      href: '/influencers',
      icon: Users,
      color: 'text-blue-600'
    },
    {
      name: 'البحث',
      href: '/search',
      icon: Search,
      color: 'text-purple-600'
    },
    {
      name: 'الرسائل',
      href: '/messages',
      icon: MessageCircle,
      color: 'text-orange-600'
    },
    {
      name: 'حسابي',
      href: '/profile',
      icon: User,
      color: 'text-gray-600'
    }
  ]

  const isActive = (href: string) => {
    if (href === '/') {
      return pathname === '/'
    }
    return pathname.startsWith(href)
  }

  return (
    <>
      {/* مساحة فارغة لتجنب تداخل المحتوى مع التنقل السفلي */}
      <div className="h-20 md:hidden"></div>
      
      {/* شريط التنقل السفلي */}
      <nav className="md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50 safe-area-bottom">
        <div className="flex items-center justify-around py-2">
          {navigationItems.map((item) => {
            const Icon = item.icon
            const active = isActive(item.href)
            
            return (
              <Link
                key={item.name}
                href={item.href}
                className={`nav-item-mobile flex-1 ${active ? 'active' : ''}`}
              >
                <div className="flex flex-col items-center justify-center">
                  <div className={`p-2 rounded-xl transition-all duration-200 ${
                    active 
                      ? 'bg-green-100 scale-110' 
                      : 'hover:bg-gray-100'
                  }`}>
                    <Icon 
                      className={`h-6 w-6 transition-colors duration-200 ${
                        active 
                          ? 'text-green-600' 
                          : 'text-gray-500'
                      }`} 
                    />
                  </div>
                  <span className={`text-xs font-medium mt-1 transition-colors duration-200 ${
                    active 
                      ? 'text-green-600' 
                      : 'text-gray-500'
                  }`}>
                    {item.name}
                  </span>
                </div>
              </Link>
            )
          })}
        </div>
        
        {/* مؤشر الصفحة النشطة */}
        <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-green-500 to-blue-500 opacity-20"></div>
      </nav>
    </>
  )
}

export default MobileNavigation
