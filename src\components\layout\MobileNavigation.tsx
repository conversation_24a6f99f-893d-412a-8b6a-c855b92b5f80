'use client'

import React from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import {
  Home,
  Users,
  Search,
  MessageCircle,
  User,
  Store,
  TrendingUp,
  Heart,
  Bell
} from 'lucide-react'

const MobileNavigation: React.FC = () => {
  const pathname = usePathname()

  const navigationItems = [
    {
      name: 'الرئيسية',
      href: '/',
      icon: Home,
      color: 'text-green-600'
    },
    {
      name: 'المؤثرين',
      href: '/influencers',
      icon: Users,
      color: 'text-blue-600'
    },
    {
      name: 'البحث',
      href: '/search',
      icon: Search,
      color: 'text-purple-600'
    },
    {
      name: 'الرسائل',
      href: '/messages',
      icon: MessageCircle,
      color: 'text-orange-600'
    },
    {
      name: 'حسابي',
      href: '/profile',
      icon: User,
      color: 'text-gray-600'
    }
  ]

  const isActive = (href: string) => {
    if (href === '/') {
      return pathname === '/'
    }
    return pathname.startsWith(href)
  }

  return (
    <>
      {/* مساحة فارغة لتجنب تداخل المحتوى مع التنقل السفلي */}
      <div className="h-20 md:hidden"></div>

      {/* شريط التنقل السفلي */}
      <nav className="md:hidden fixed bottom-0 left-0 right-0 bg-slate-900/95 backdrop-blur-md border-t border-slate-700/50 z-50">
        <div className="flex items-center justify-around py-2">
          {navigationItems.map((item) => {
            const Icon = item.icon
            const active = isActive(item.href)

            return (
              <Link
                key={item.name}
                href={item.href}
                className="flex-1"
              >
                <div className="flex flex-col items-center justify-center py-3">
                  <div className={`p-2 rounded-lg transition-colors ${
                    active
                      ? 'bg-indigo-500/10'
                      : 'hover:bg-slate-800'
                  }`}>
                    <Icon
                      className={`h-5 w-5 transition-colors ${
                        active
                          ? 'text-indigo-400'
                          : 'text-slate-400'
                      }`}
                    />
                  </div>
                  <span className={`text-xs font-medium mt-1 transition-colors ${
                    active
                      ? 'text-indigo-400'
                      : 'text-slate-400'
                  }`}>
                    {item.name}
                  </span>
                </div>
              </Link>
            )
          })}
        </div>
      </nav>
    </>
  )
}

export default MobileNavigation
