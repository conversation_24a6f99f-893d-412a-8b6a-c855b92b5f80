'use client'

import React from 'react'
import Link from 'next/link'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import { 
  Search, 
  CreditCard, 
  CheckCircle, 
  Users, 
  Store,
  Shield,
  DollarSign,
  Star,
  ArrowLeft,
  Play
} from 'lucide-react'

const HowItWorksPage: React.FC = () => {
  const merchantSteps = [
    {
      step: 1,
      icon: Search,
      title: 'ابحث واختر',
      description: 'تصفح قائمة المؤثرين المعتمدين واختر الأنسب لعلامتك التجارية حسب التخصص والجمهور والموقع',
      details: [
        'فلترة حسب المدينة والتخصص',
        'مراجعة إحصائيات المتابعين',
        'قراءة تقييمات العملاء السابقين',
        'مقارنة الأسعار والخدمات'
      ]
    },
    {
      step: 2,
      icon: CreditCard,
      title: 'ادفع بأمان',
      description: 'ادفع المبلغ المتفق عليه بأمان تام. سنحتفظ بأموالك في نظام الضمان حتى اكتمال الحملة',
      details: [
        'دفع آمن عبر Apple Pay و Google Pay',
        'نظام ضمان الأموال',
        'حماية من الاحتيال',
        'إمكانية الاسترداد الكامل'
      ]
    },
    {
      step: 3,
      icon: CheckCircle,
      title: 'احصل على النتائج',
      description: 'بعد تنفيذ المؤثر للحملة وموافقتك على النتائج، سنحول المبلغ للمؤثر وتحصل على تقرير مفصل',
      details: [
        'مراجعة المحتوى قبل النشر',
        'تقارير الأداء والوصول',
        'إحصائيات التفاعل',
        'ضمان الجودة والالتزام'
      ]
    }
  ]

  const influencerSteps = [
    {
      step: 1,
      icon: Users,
      title: 'أنشئ ملفك الشخصي',
      description: 'سجل في المنصة وأنشئ ملفاً شخصياً احترافياً يعرض خبراتك ومحتواك وأسعارك',
      details: [
        'رفع نماذج من أعمالك',
        'تحديد أسعارك لكل نوع محتوى',
        'ربط حساباتك على وسائل التواصل',
        'كتابة نبذة تعريفية جذابة'
      ]
    },
    {
      step: 2,
      icon: Star,
      title: 'استقبل الطلبات',
      description: 'استقبل طلبات التعاون من التجار واختر المناسب منها حسب تخصصك وجدولك الزمني',
      details: [
        'مراجعة تفاصيل الحملة',
        'التفاوض على السعر والمواعيد',
        'قبول أو رفض الطلبات',
        'التواصل المباشر مع التجار'
      ]
    },
    {
      step: 3,
      icon: DollarSign,
      title: 'نفذ واحصل على أجرك',
      description: 'نفذ الحملة الإعلانية وفقاً للاتفاق واحصل على أجرك فور موافقة التاجر على النتائج',
      details: [
        'تنفيذ المحتوى حسب المطلوب',
        'تسليم النتائج في الموعد',
        'الحصول على الدفع فوراً',
        'بناء سمعة إيجابية'
      ]
    }
  ]

  const features = [
    {
      icon: Shield,
      title: 'ضمان الأموال',
      description: 'نحتفظ بأموال التجار في نظام ضمان آمن حتى اكتمال الحملة بنجاح'
    },
    {
      icon: CheckCircle,
      title: 'مؤثرين معتمدين',
      description: 'جميع المؤثرين في منصتنا معتمدين ومتحققين من هويتهم وحساباتهم'
    },
    {
      icon: Star,
      title: 'نظام التقييم',
      description: 'نظام تقييم شفاف يساعد في بناء الثقة بين المؤثرين والتجار'
    },
    {
      icon: DollarSign,
      title: 'أسعار شفافة',
      description: 'أسعار واضحة ومحددة مسبقاً بدون رسوم خفية أو مفاجآت'
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
      <Header />
      
      {/* Hero Section */}
      <section className="py-20 bg-gradient-primary text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            كيف تعمل منصة المؤثرين؟
          </h1>
          <p className="text-xl mb-8 opacity-90">
            عملية بسيطة وآمنة لربط المؤثرين بالتجار مع ضمان الحقوق للجميع
          </p>
          <Button size="xl" className="bg-white text-primary-600 hover:bg-gray-100">
            <Play className="h-6 w-6 ml-3" />
            شاهد الفيديو التوضيحي
          </Button>
        </div>
      </section>

      {/* For Merchants */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Store className="h-8 w-8 text-blue-600" />
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              للتجار وأصحاب الأعمال
            </h2>
            <p className="text-xl text-gray-600">
              خطوات بسيطة لإطلاق حملتك الإعلانية مع المؤثرين
            </p>
          </div>

          <div className="space-y-16">
            {merchantSteps.map((step, index) => {
              const Icon = step.icon
              return (
                <div key={step.step} className={`flex flex-col lg:flex-row items-center gap-12 ${
                  index % 2 === 1 ? 'lg:flex-row-reverse' : ''
                }`}>
                  <div className="flex-1">
                    <Card className="h-full">
                      <div className="flex items-center mb-6">
                        <div className="w-12 h-12 bg-primary-600 text-white rounded-full flex items-center justify-center text-xl font-bold ml-4">
                          {step.step}
                        </div>
                        <div>
                          <h3 className="text-2xl font-bold text-gray-900">{step.title}</h3>
                        </div>
                      </div>
                      <p className="text-gray-600 mb-6 leading-relaxed">
                        {step.description}
                      </p>
                      <ul className="space-y-3">
                        {step.details.map((detail, i) => (
                          <li key={i} className="flex items-center text-gray-700">
                            <CheckCircle className="h-5 w-5 text-green-500 ml-3 flex-shrink-0" />
                            <span>{detail}</span>
                          </li>
                        ))}
                      </ul>
                    </Card>
                  </div>
                  <div className="flex-1 flex justify-center">
                    <div className="w-64 h-64 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center">
                      <Icon className="h-32 w-32 text-primary-600" />
                    </div>
                  </div>
                </div>
              )
            })}
          </div>

          <div className="text-center mt-16">
            <Link href="/register/merchant">
              <Button size="xl" className="bg-blue-600 hover:bg-blue-700">
                ابدأ حملتك الآن
                <ArrowLeft className="h-6 w-6 mr-3" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* For Influencers */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Users className="h-8 w-8 text-purple-600" />
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              للمؤثرين ومنشئي المحتوى
            </h2>
            <p className="text-xl text-gray-600">
              اربح من محتواك وابني علامتك التجارية الشخصية
            </p>
          </div>

          <div className="space-y-16">
            {influencerSteps.map((step, index) => {
              const Icon = step.icon
              return (
                <div key={step.step} className={`flex flex-col lg:flex-row items-center gap-12 ${
                  index % 2 === 0 ? 'lg:flex-row-reverse' : ''
                }`}>
                  <div className="flex-1">
                    <Card className="h-full">
                      <div className="flex items-center mb-6">
                        <div className="w-12 h-12 bg-purple-600 text-white rounded-full flex items-center justify-center text-xl font-bold ml-4">
                          {step.step}
                        </div>
                        <div>
                          <h3 className="text-2xl font-bold text-gray-900">{step.title}</h3>
                        </div>
                      </div>
                      <p className="text-gray-600 mb-6 leading-relaxed">
                        {step.description}
                      </p>
                      <ul className="space-y-3">
                        {step.details.map((detail, i) => (
                          <li key={i} className="flex items-center text-gray-700">
                            <CheckCircle className="h-5 w-5 text-green-500 ml-3 flex-shrink-0" />
                            <span>{detail}</span>
                          </li>
                        ))}
                      </ul>
                    </Card>
                  </div>
                  <div className="flex-1 flex justify-center">
                    <div className="w-64 h-64 bg-gradient-to-br from-purple-100 to-pink-100 rounded-full flex items-center justify-center">
                      <Icon className="h-32 w-32 text-purple-600" />
                    </div>
                  </div>
                </div>
              )
            })}
          </div>

          <div className="text-center mt-16">
            <Link href="/register/influencer">
              <Button size="xl" className="bg-purple-600 hover:bg-purple-700">
                انضم كمؤثر
                <ArrowLeft className="h-6 w-6 mr-3" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Features */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              لماذا تختار منصتنا؟
            </h2>
            <p className="text-xl text-gray-600">
              مميزات تجعلنا الخيار الأول للمؤثرين والتجار
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon
              return (
                <Card key={index} hover className="text-center">
                  <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
                    <Icon className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {feature.description}
                  </p>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-primary text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            جاهز للبدء؟
          </h2>
          <p className="text-xl mb-8 opacity-90">
            انضم إلى آلاف المؤثرين والتجار الذين يحققون النجاح من خلال منصتنا
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/register/influencer">
              <Button size="xl" variant="secondary" className="w-full sm:w-auto">
                انضم كمؤثر
              </Button>
            </Link>
            <Link href="/register/merchant">
              <Button size="xl" className="w-full sm:w-auto bg-white text-primary-600 hover:bg-gray-100">
                ابدأ حملتك الإعلانية
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}

export default HowItWorksPage
