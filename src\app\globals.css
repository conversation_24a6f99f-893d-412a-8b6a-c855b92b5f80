@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* نظام ألوان محسن للوضوح والتباين - Dark Theme */

  /* الألوان الأساسية */
  --primary: 99, 102, 241;        /* Indigo 500 - لون أساسي احترافي */
  --primary-dark: 67, 56, 202;    /* Indigo 700 - أغمق للتباين */
  --primary-light: 165, 180, 252; /* Indigo 300 - أفتح للتباين */
  --primary-hover: 79, 70, 229;   /* Indigo 600 - للحالة التفاعلية */

  --secondary: 16, 185, 129;      /* Emerald 500 - للنجاح والإيجابية */
  --secondary-dark: 5, 150, 105;  /* Emerald 600 */
  --secondary-light: 52, 211, 153; /* Emerald 400 */

  --accent: 245, 158, 11;         /* Amber 500 - للتنبيهات والتمييز */
  --accent-dark: 217, 119, 6;     /* Amber 600 */
  --accent-light: 251, 191, 36;   /* Amber 400 */

  --error: 239, 68, 68;           /* Red 500 - للأخطاء */
  --error-dark: 220, 38, 38;      /* Red 600 */
  --error-light: 248, 113, 113;   /* Red 400 */

  --warning: 245, 158, 11;        /* Amber 500 - للتحذيرات */
  --success: 34, 197, 94;         /* Green 500 - للنجاح */
  --info: 59, 130, 246;           /* Blue 500 - للمعلومات */

  /* خلفيات محسنة للتباين */
  --bg-primary: 2, 6, 23;         /* Slate 950 - أغمق للتباين الأفضل */
  --bg-secondary: 15, 23, 42;     /* Slate 900 */
  --bg-tertiary: 30, 41, 59;      /* Slate 800 */
  --bg-card: 51, 65, 85;          /* Slate 700 */
  --bg-surface: 71, 85, 105;      /* Slate 600 */
  --bg-elevated: 100, 116, 139;   /* Slate 500 */

  /* نصوص محسنة للوضوح */
  --text-primary: 255, 255, 255;  /* White - أقصى تباين */
  --text-secondary: 226, 232, 240; /* Slate 200 - تباين عالي */
  --text-tertiary: 203, 213, 225; /* Slate 300 - تباين جيد */
  --text-muted: 148, 163, 184;    /* Slate 400 - للنصوص الثانوية */
  --text-disabled: 100, 116, 139; /* Slate 500 - للعناصر المعطلة */

  /* حدود محسنة */
  --border-primary: 71, 85, 105;  /* Slate 600 */
  --border-secondary: 100, 116, 139; /* Slate 500 */
  --border-light: 148, 163, 184;  /* Slate 400 */
  --border-focus: 99, 102, 241;   /* Primary color للتركيز */

  /* ظلال محسنة */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.6);

  /* متغيرات للشفافية */
  --opacity-disabled: 0.5;
  --opacity-hover: 0.8;
  --opacity-focus: 0.9;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  direction: rtl;
  font-family: 'Cairo', 'Tajawal', sans-serif;
}

body {
  color: rgb(var(--text-primary));
  background: linear-gradient(
    135deg,
    rgb(var(--bg-primary)) 0%,
    rgb(var(--bg-secondary)) 50%,
    rgb(var(--bg-tertiary)) 100%
  );
  min-height: 100vh;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
  font-size: 16px;
  line-height: 1.6;
  /* تحسين التباين للنصوص */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.english {
  direction: ltr;
  font-family: 'Inter', sans-serif;
}

.arabic {
  direction: rtl;
  font-family: 'Cairo', 'Tajawal', sans-serif;
}

/* Custom scrollbar - Dark Theme */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: rgb(var(--bg-secondary));
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgb(var(--primary-color)), rgb(var(--secondary-color)));
  border-radius: 10px;
  border: 2px solid rgb(var(--bg-secondary));
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgb(var(--secondary-color)), rgb(var(--accent-color)));
  transform: scale(1.1);
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-right {
  animation: slideInRight 0.5s ease-in-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.slide-in-left {
  animation: slideInLeft 0.5s ease-in-out;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Button hover effects */
.btn-hover {
  transition: all 0.3s ease;
}

.btn-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Card hover effects */
.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Gradient backgrounds - Dark Theme */
.gradient-primary {
  background: linear-gradient(135deg, rgb(var(--primary-color)) 0%, rgb(var(--secondary-color)) 100%);
}

.gradient-secondary {
  background: linear-gradient(135deg, rgb(var(--secondary-color)) 0%, rgb(var(--accent-color)) 100%);
}

.gradient-success {
  background: linear-gradient(135deg, rgb(var(--success-color)) 0%, rgb(var(--accent-color)) 100%);
}

.gradient-cosmic {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
}

.gradient-sunset {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
}

.gradient-ocean {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Loading spinner */
.spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ===== تحسينات للجوال والتطبيق ===== */

/* تحسينات عامة للجوال */
* {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

input, textarea, select {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
  font-size: 16px; /* منع التكبير التلقائي في iOS */
}

/* تصميم يشبه التطبيق - Dark Theme */
.app-container {
  min-height: 100vh;
  background: linear-gradient(135deg, rgb(var(--bg-primary)) 0%, rgb(var(--bg-secondary)) 100%);
  position: relative;
}

.mobile-container {
  max-width: 100vw;
  padding: 0 16px;
  margin: 0 auto;
}

/* بطاقات محسنة للجوال مع تباين أفضل */
.card {
  background: rgba(var(--bg-card), 0.98);
  border-radius: 16px;
  border: 1px solid rgba(var(--border-primary), 0.8);
  padding: 20px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-md);
  /* تحسين التباين للنصوص داخل البطاقات */
  color: rgb(var(--text-primary));
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: rgba(var(--primary), 0.3);
}

.card-interactive {
  cursor: pointer;
}

.card-interactive:hover {
  background: rgba(var(--bg-surface), 0.8);
  border-color: rgba(var(--primary), 0.5);
}

.card-mobile::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, rgb(139, 92, 246), rgb(236, 72, 153), rgb(34, 211, 238));
  opacity: 0;
  transition: opacity 0.4s ease;
}

.card-mobile:hover::before {
  opacity: 1;
}

/* تأثير الضوء المتحرك */
.card-mobile::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(139, 92, 246, 0.1), transparent);
  transform: rotate(45deg);
  transition: all 0.6s ease;
  opacity: 0;
}

.card-mobile:hover::after {
  animation: shimmer 1.5s ease-in-out;
}

/* أزرار بتصميم الجوال */
.btn-mobile {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 14px 24px;
  border-radius: 50px;
  font-weight: 600;
  font-size: 16px;
  line-height: 1.5;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  cursor: pointer;
  min-height: 48px;
  position: relative;
  overflow: hidden;
}

.btn-mobile::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-mobile:hover::before {
  left: 100%;
}

/* أزرار محسنة مع تباين أفضل */
.btn-primary {
  background: linear-gradient(135deg, rgb(var(--primary)) 0%, rgb(var(--primary-hover)) 100%);
  color: rgb(var(--text-primary));
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.2s ease;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-height: 44px; /* للمس السهل على الجوال */
  box-shadow: var(--shadow-md);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.btn-primary:hover {
  background: linear-gradient(135deg, rgb(var(--primary-hover)) 0%, rgb(var(--primary-dark)) 100%);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
  opacity: var(--opacity-hover);
}

.btn-primary:focus {
  outline: 2px solid rgb(var(--border-focus));
  outline-offset: 2px;
}

.btn-primary:active {
  transform: translateY(0);
}

.btn-secondary {
  background: rgba(var(--bg-surface), 0.3);
  color: rgb(var(--text-primary));
  border: 1px solid rgba(var(--border-secondary), 0.8);
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.2s ease;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-height: 44px;
  box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
  background: rgba(var(--bg-surface), 0.6);
  border-color: rgb(var(--primary));
  color: rgb(var(--text-primary));
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-secondary:focus {
  outline: 2px solid rgb(var(--border-focus));
  outline-offset: 2px;
}

.btn-outline {
  background: transparent;
  color: rgb(var(--primary));
  border: 2px solid rgb(var(--primary));
  border-radius: 12px;
  padding: 10px 22px;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.2s ease;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-height: 44px;
}

.btn-outline:hover {
  background: rgb(var(--primary));
  color: white;
}

/* حقول الإدخال محسنة مع تباين أفضل */
.input {
  width: 100%;
  padding: 14px 16px;
  border: 2px solid rgba(var(--border-primary), 0.6);
  border-radius: 12px;
  font-size: 16px;
  background: rgba(var(--bg-secondary), 0.9);
  color: rgb(var(--text-primary));
  transition: all 0.2s ease;
  min-height: 48px;
  box-shadow: var(--shadow-sm);
}

.input:focus {
  outline: none;
  border-color: rgb(var(--primary));
  box-shadow: 0 0 0 3px rgba(var(--primary), 0.2);
  background: rgba(var(--bg-secondary), 1);
  color: rgb(var(--text-primary));
}

.input::placeholder {
  color: rgb(var(--text-muted));
  opacity: 0.8;
}

.input:disabled {
  opacity: var(--opacity-disabled);
  cursor: not-allowed;
  background: rgba(var(--bg-surface), 0.3);
  color: rgb(var(--text-disabled));
}

/* شارات الثقة السعودية - Dark Theme */
.saudi-trust-badge {
  display: inline-flex;
  align-items: center;
  padding: 10px 20px;
  background: linear-gradient(135deg, rgb(16, 185, 129) 0%, rgb(34, 211, 238) 100%);
  color: white;
  border-radius: 50px;
  font-size: 14px;
  font-weight: 700;
  box-shadow: 0 4px 20px rgba(16, 185, 129, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.trust-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(16, 185, 129, 0.2);
  color: rgb(16, 185, 129);
  border-radius: 25px;
  font-size: 13px;
  font-weight: 600;
  border: 1px solid rgba(16, 185, 129, 0.3);
  backdrop-filter: blur(10px);
}

/* تحسينات النصوص مع تباين أفضل */
.heading {
  font-size: clamp(24px, 5vw, 36px);
  font-weight: 700;
  line-height: 1.2;
  color: rgb(var(--text-primary));
  margin-bottom: 16px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.subheading {
  font-size: clamp(16px, 3vw, 20px);
  font-weight: 500;
  line-height: 1.5;
  color: rgb(var(--text-secondary));
  margin-bottom: 24px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.text-muted {
  color: rgb(var(--text-muted));
  opacity: 0.9;
}

.text-primary {
  color: rgb(var(--primary-light));
  font-weight: 600;
}

.text-secondary {
  color: rgb(var(--secondary-light));
  font-weight: 600;
}

.text-white {
  color: rgb(var(--text-primary));
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.text-contrast {
  color: rgb(var(--text-primary));
  background: rgba(var(--bg-primary), 0.8);
  padding: 2px 8px;
  border-radius: 4px;
}

/* شارات وعلامات */
.badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge-primary {
  background: rgba(var(--primary), 0.1);
  color: rgb(var(--primary));
  border: 1px solid rgba(var(--primary), 0.2);
}

.badge-success {
  background: rgba(var(--secondary), 0.1);
  color: rgb(var(--secondary));
  border: 1px solid rgba(var(--secondary), 0.2);
}

.badge-warning {
  background: rgba(var(--accent), 0.1);
  color: rgb(var(--accent));
  border: 1px solid rgba(var(--accent), 0.2);
}

/* تدرجات ملونة للنصوص - Dark Theme */
.text-gradient-primary {
  background: linear-gradient(135deg, rgb(139, 92, 246) 0%, rgb(236, 72, 153) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-secondary {
  background: linear-gradient(135deg, rgb(236, 72, 153) 0%, rgb(34, 211, 238) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-cosmic {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-gold {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #f59e0b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* تحسينات الشبكة للجوال */
.grid-mobile {
  display: grid;
  gap: 16px;
}

@media (min-width: 640px) {
  .grid-mobile {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
}

@media (min-width: 1024px) {
  .grid-mobile {
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
  }
}

/* تأثيرات الحركة المحسنة */
.bounce-in {
  animation: bounceIn 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.slide-up {
  animation: slideUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 640px) {
  .mobile-container {
    padding: 0 12px;
  }

  .card-mobile {
    padding: 16px;
    border-radius: 16px;
  }

  .btn-mobile {
    padding: 12px 20px;
    font-size: 15px;
    min-height: 44px;
  }

  .input-mobile {
    padding: 14px 16px;
    border-radius: 12px;
    min-height: 52px;
  }
}

/* تحسينات الأداء */
.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

.will-change-transform {
  will-change: transform;
}

/* منطقة الأمان للأجهزة الحديثة */
.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-left {
  padding-left: env(safe-area-inset-left);
}

.safe-area-right {
  padding-right: env(safe-area-inset-right);
}

/* تحسينات اللمس */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* تأثيرات التحميل */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* تأثيرات حركية جديدة وجميلة */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(var(--primary-color), 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(var(--primary-color), 0.6);
  }
}

@keyframes pulse-ring {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(2.4);
    opacity: 0;
  }
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes sparkle {
  0%, 100% {
    opacity: 0;
    transform: scale(0);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
}

/* فئات التأثيرات */
.float-animation {
  animation: float 3s ease-in-out infinite;
}

.glow-effect {
  animation: glow 2s ease-in-out infinite alternate;
}

.pulse-ring-effect::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  border: 2px solid rgba(var(--primary-color), 0.6);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: pulse-ring 2s infinite;
}

.gradient-bg {
  background: linear-gradient(-45deg, rgb(var(--primary-color)), rgb(var(--secondary-color)), rgb(var(--accent-color)), rgb(var(--success-color)));
  background-size: 400% 400%;
  animation: gradient-shift 8s ease infinite;
}

/* تأثيرات الجسيمات المتلألئة */
.sparkle-container {
  position: relative;
  overflow: hidden;
}

.sparkle-container::before,
.sparkle-container::after {
  content: '✨';
  position: absolute;
  font-size: 20px;
  animation: sparkle 3s infinite;
}

.sparkle-container::before {
  top: 20%;
  left: 20%;
  animation-delay: 0s;
}

.sparkle-container::after {
  top: 60%;
  right: 20%;
  animation-delay: 1.5s;
}

/* تحسينات إضافية للتباين والوضوح */

/* فئات مساعدة للتباين */
.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.text-shadow-lg {
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
}

.bg-contrast {
  background: rgba(var(--bg-primary), 0.95);
  border: 1px solid rgba(var(--border-primary), 0.8);
}

.bg-contrast-high {
  background: rgba(var(--bg-secondary), 0.98);
  border: 2px solid rgba(var(--border-secondary), 0.9);
}

/* تحسينات للروابط */
a {
  color: rgb(var(--primary-light));
  text-decoration: none;
  transition: all 0.2s ease;
}

a:hover {
  color: rgb(var(--text-primary));
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

a:focus {
  outline: 2px solid rgb(var(--border-focus));
  outline-offset: 2px;
  border-radius: 4px;
}

/* تحسينات للعناوين */
h1, h2, h3, h4, h5, h6 {
  color: rgb(var(--text-primary));
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* تحسينات للفقرات */
p {
  color: rgb(var(--text-secondary));
  line-height: 1.7;
}

/* تحسينات للقوائم */
ul, ol {
  color: rgb(var(--text-secondary));
}

li {
  margin-bottom: 0.5rem;
}

/* تحسينات للجداول */
table {
  background: rgba(var(--bg-card), 0.95);
  border: 1px solid rgba(var(--border-primary), 0.8);
  border-radius: 12px;
  overflow: hidden;
}

th {
  background: rgba(var(--bg-surface), 0.9);
  color: rgb(var(--text-primary));
  font-weight: 600;
  padding: 12px 16px;
  border-bottom: 2px solid rgba(var(--border-primary), 0.8);
}

td {
  color: rgb(var(--text-secondary));
  padding: 12px 16px;
  border-bottom: 1px solid rgba(var(--border-primary), 0.4);
}

/* تحسينات للنماذج */
form {
  background: rgba(var(--bg-card), 0.95);
  border: 1px solid rgba(var(--border-primary), 0.6);
  border-radius: 16px;
  padding: 24px;
}

fieldset {
  border: 2px solid rgba(var(--border-primary), 0.6);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
}

legend {
  color: rgb(var(--text-primary));
  font-weight: 600;
  padding: 0 8px;
}

/* تحسينات للتنبيهات */
.alert {
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  border: 1px solid;
  font-weight: 500;
}

.alert-success {
  background: rgba(var(--success), 0.1);
  color: rgb(var(--success));
  border-color: rgba(var(--success), 0.3);
}

.alert-warning {
  background: rgba(var(--warning), 0.1);
  color: rgb(var(--warning));
  border-color: rgba(var(--warning), 0.3);
}

.alert-error {
  background: rgba(var(--error), 0.1);
  color: rgb(var(--error));
  border-color: rgba(var(--error), 0.3);
}

.alert-info {
  background: rgba(var(--info), 0.1);
  color: rgb(var(--info));
  border-color: rgba(var(--info), 0.3);
}

/* تحسينات للتركيز */
*:focus {
  outline: 2px solid rgb(var(--border-focus));
  outline-offset: 2px;
}

/* تحسينات للتمرير */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(var(--bg-secondary), 0.5);
}

::-webkit-scrollbar-thumb {
  background: rgba(var(--border-primary), 0.8);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(var(--primary), 0.8);
}

/* تحسينات للطباعة */
@media print {
  * {
    background: white !important;
    color: black !important;
    text-shadow: none !important;
    box-shadow: none !important;
  }
}
