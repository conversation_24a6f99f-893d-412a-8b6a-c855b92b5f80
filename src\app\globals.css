@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* نظام الألوان الداكن الجديد */
  --foreground-rgb: 255, 255, 255;
  --background-start-rgb: 15, 23, 42;
  --background-end-rgb: 30, 41, 59;

  /* الألوان الأساسية الجديدة */
  --primary-color: 139, 92, 246;
  --secondary-color: 236, 72, 153;
  --accent-color: 34, 211, 238;
  --success-color: 16, 185, 129;
  --warning-color: 245, 158, 11;
  --error-color: 239, 68, 68;

  /* ألوان الخلفية */
  --bg-primary: 15, 23, 42;
  --bg-secondary: 30, 41, 59;
  --bg-tertiary: 51, 65, 85;
  --bg-card: 30, 41, 59;

  /* ألوان النصوص */
  --text-primary: 248, 250, 252;
  --text-secondary: 203, 213, 225;
  --text-muted: 148, 163, 184;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  direction: rtl;
  font-family: 'Cairo', 'Tajawal', sans-serif;
}

body {
  color: rgb(var(--text-primary));
  background: linear-gradient(
    135deg,
    rgb(var(--bg-primary)) 0%,
    rgb(var(--bg-secondary)) 50%,
    rgb(var(--bg-tertiary)) 100%
  );
  min-height: 100vh;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
  font-size: 16px;
}

.english {
  direction: ltr;
  font-family: 'Inter', sans-serif;
}

.arabic {
  direction: rtl;
  font-family: 'Cairo', 'Tajawal', sans-serif;
}

/* Custom scrollbar - Dark Theme */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: rgb(var(--bg-secondary));
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgb(var(--primary-color)), rgb(var(--secondary-color)));
  border-radius: 10px;
  border: 2px solid rgb(var(--bg-secondary));
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgb(var(--secondary-color)), rgb(var(--accent-color)));
  transform: scale(1.1);
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-right {
  animation: slideInRight 0.5s ease-in-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.slide-in-left {
  animation: slideInLeft 0.5s ease-in-out;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Button hover effects */
.btn-hover {
  transition: all 0.3s ease;
}

.btn-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Card hover effects */
.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Gradient backgrounds - Dark Theme */
.gradient-primary {
  background: linear-gradient(135deg, rgb(var(--primary-color)) 0%, rgb(var(--secondary-color)) 100%);
}

.gradient-secondary {
  background: linear-gradient(135deg, rgb(var(--secondary-color)) 0%, rgb(var(--accent-color)) 100%);
}

.gradient-success {
  background: linear-gradient(135deg, rgb(var(--success-color)) 0%, rgb(var(--accent-color)) 100%);
}

.gradient-cosmic {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
}

.gradient-sunset {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
}

.gradient-ocean {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Loading spinner */
.spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ===== تحسينات للجوال والتطبيق ===== */

/* تحسينات عامة للجوال */
* {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

input, textarea, select {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
  font-size: 16px; /* منع التكبير التلقائي في iOS */
}

/* تصميم يشبه التطبيق - Dark Theme */
.app-container {
  min-height: 100vh;
  background: linear-gradient(135deg, rgb(var(--bg-primary)) 0%, rgb(var(--bg-secondary)) 100%);
  position: relative;
}

.mobile-container {
  max-width: 100vw;
  padding: 0 16px;
  margin: 0 auto;
}

/* بطاقات بتصميم الجوال - Dark Theme */
.card-mobile {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 24px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.card-mobile:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
  border-color: rgba(139, 92, 246, 0.5);
}

.card-mobile::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, rgb(139, 92, 246), rgb(236, 72, 153), rgb(34, 211, 238));
  opacity: 0;
  transition: opacity 0.4s ease;
}

.card-mobile:hover::before {
  opacity: 1;
}

/* تأثير الضوء المتحرك */
.card-mobile::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(139, 92, 246, 0.1), transparent);
  transform: rotate(45deg);
  transition: all 0.6s ease;
  opacity: 0;
}

.card-mobile:hover::after {
  animation: shimmer 1.5s ease-in-out;
}

/* أزرار بتصميم الجوال */
.btn-mobile {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 14px 24px;
  border-radius: 50px;
  font-weight: 600;
  font-size: 16px;
  line-height: 1.5;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  cursor: pointer;
  min-height: 48px;
  position: relative;
  overflow: hidden;
}

.btn-mobile::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-mobile:hover::before {
  left: 100%;
}

.btn-primary-mobile {
  background: linear-gradient(135deg, rgb(139, 92, 246) 0%, rgb(236, 72, 153) 100%);
  color: white;
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-primary-mobile:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 15px 40px rgba(139, 92, 246, 0.6);
  background: linear-gradient(135deg, rgb(236, 72, 153) 0%, rgb(34, 211, 238) 100%);
}

.btn-secondary-mobile {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(10px);
  color: rgb(248, 250, 252);
  border: 2px solid rgba(139, 92, 246, 0.3);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.btn-secondary-mobile:hover {
  border-color: rgb(139, 92, 246);
  color: rgb(139, 92, 246);
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 8px 30px rgba(139, 92, 246, 0.3);
  background: rgba(139, 92, 246, 0.1);
}

/* حقول الإدخال - Dark Theme */
.input-mobile {
  width: 100%;
  padding: 18px 24px;
  border: 2px solid rgba(139, 92, 246, 0.3);
  border-radius: 20px;
  font-size: 16px;
  line-height: 1.5;
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(10px);
  color: rgb(248, 250, 252);
  transition: all 0.4s ease;
  min-height: 60px;
}

.input-mobile:focus {
  outline: none;
  border-color: rgb(139, 92, 246);
  box-shadow: 0 0 0 4px rgba(139, 92, 246, 0.2);
  background: rgba(30, 41, 59, 0.9);
}

.input-mobile::placeholder {
  color: rgb(148, 163, 184);
}

/* شارات الثقة السعودية - Dark Theme */
.saudi-trust-badge {
  display: inline-flex;
  align-items: center;
  padding: 10px 20px;
  background: linear-gradient(135deg, rgb(16, 185, 129) 0%, rgb(34, 211, 238) 100%);
  color: white;
  border-radius: 50px;
  font-size: 14px;
  font-weight: 700;
  box-shadow: 0 4px 20px rgba(16, 185, 129, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.trust-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(16, 185, 129, 0.2);
  color: rgb(16, 185, 129);
  border-radius: 25px;
  font-size: 13px;
  font-weight: 600;
  border: 1px solid rgba(16, 185, 129, 0.3);
  backdrop-filter: blur(10px);
}

/* تحسينات النصوص للجوال - Dark Theme */
.heading-mobile {
  font-size: clamp(28px, 6vw, 42px);
  font-weight: 900;
  line-height: 1.1;
  color: rgb(248, 250, 252);
  margin-bottom: 20px;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.subheading-mobile {
  font-size: clamp(18px, 4vw, 22px);
  font-weight: 500;
  line-height: 1.6;
  color: rgb(203, 213, 225);
  margin-bottom: 28px;
}

/* تدرجات ملونة للنصوص - Dark Theme */
.text-gradient-primary {
  background: linear-gradient(135deg, rgb(139, 92, 246) 0%, rgb(236, 72, 153) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-secondary {
  background: linear-gradient(135deg, rgb(236, 72, 153) 0%, rgb(34, 211, 238) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-cosmic {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-gold {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #f59e0b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* تحسينات الشبكة للجوال */
.grid-mobile {
  display: grid;
  gap: 16px;
}

@media (min-width: 640px) {
  .grid-mobile {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
}

@media (min-width: 1024px) {
  .grid-mobile {
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
  }
}

/* تأثيرات الحركة المحسنة */
.bounce-in {
  animation: bounceIn 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.slide-up {
  animation: slideUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 640px) {
  .mobile-container {
    padding: 0 12px;
  }

  .card-mobile {
    padding: 16px;
    border-radius: 16px;
  }

  .btn-mobile {
    padding: 12px 20px;
    font-size: 15px;
    min-height: 44px;
  }

  .input-mobile {
    padding: 14px 16px;
    border-radius: 12px;
    min-height: 52px;
  }
}

/* تحسينات الأداء */
.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

.will-change-transform {
  will-change: transform;
}

/* منطقة الأمان للأجهزة الحديثة */
.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-left {
  padding-left: env(safe-area-inset-left);
}

.safe-area-right {
  padding-right: env(safe-area-inset-right);
}

/* تحسينات اللمس */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* تأثيرات التحميل */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* تأثيرات حركية جديدة وجميلة */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(var(--primary-color), 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(var(--primary-color), 0.6);
  }
}

@keyframes pulse-ring {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(2.4);
    opacity: 0;
  }
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes sparkle {
  0%, 100% {
    opacity: 0;
    transform: scale(0);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
}

/* فئات التأثيرات */
.float-animation {
  animation: float 3s ease-in-out infinite;
}

.glow-effect {
  animation: glow 2s ease-in-out infinite alternate;
}

.pulse-ring-effect::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  border: 2px solid rgba(var(--primary-color), 0.6);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: pulse-ring 2s infinite;
}

.gradient-bg {
  background: linear-gradient(-45deg, rgb(var(--primary-color)), rgb(var(--secondary-color)), rgb(var(--accent-color)), rgb(var(--success-color)));
  background-size: 400% 400%;
  animation: gradient-shift 8s ease infinite;
}

/* تأثيرات الجسيمات المتلألئة */
.sparkle-container {
  position: relative;
  overflow: hidden;
}

.sparkle-container::before,
.sparkle-container::after {
  content: '✨';
  position: absolute;
  font-size: 20px;
  animation: sparkle 3s infinite;
}

.sparkle-container::before {
  top: 20%;
  left: 20%;
  animation-delay: 0s;
}

.sparkle-container::after {
  top: 60%;
  right: 20%;
  animation-delay: 1.5s;
}
